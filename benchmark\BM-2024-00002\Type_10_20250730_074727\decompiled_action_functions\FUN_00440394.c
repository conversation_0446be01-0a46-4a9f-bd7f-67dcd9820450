
undefined4 FUN_00440394(int param_1)

{
  int iVar1;
  int iVar2;
  char *__s;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","FirmwareDownloadCancel");
  }
  else {
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar2 = mxmlNewElement(iVar2,"soap:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"FirmwareDownloadCancelResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("FirmwareDownloadCancelResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar2 = mxmlNewElement(iVar2,"FirmwareDownloadCancelResult");
            if (iVar2 == 0) {
              mxmlDelete(iVar1);
              puts("FirmwareDownloadCancelResult=NULL");
            }
            else {
              mxmlNewText(iVar2,0,"O");
              if ("" == 0) {
                __s = mxmlSaveAllocString(iVar1,0);
                if (__s != 0) {
                  system("killall wget");
                  puts(__s);
                  FUN_0041ed70("",200,__s,"");
                  free(__s);
                }
              }
              mxmlDelete(iVar1);
            }
          }
        }
      }
    }
  }
  return 0;
}

