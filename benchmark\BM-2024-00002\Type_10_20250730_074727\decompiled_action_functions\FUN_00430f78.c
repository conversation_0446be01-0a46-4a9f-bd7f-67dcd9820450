
undefined4 FUN_00430f78(void)

{
  int iVar1;
  int iVar2;
  int iVar3;
  int iVar4;
  FILE *__stream;
  int iVar5;
  char *pcVar6;
  void *__ptr;
  char acStack_2cc [512];
  undefined auStack_cc [64];
  undefined4 local_8c;
  undefined4 local_88;
  undefined4 local_84;
  undefined4 local_80;
  undefined4 local_7c;
  undefined4 local_78;
  undefined4 local_74;
  undefined4 local_70;
  undefined4 local_6c;
  undefined4 local_68;
  undefined4 local_64;
  undefined4 local_60;
  undefined4 local_5c;
  undefined4 local_58;
  undefined4 local_54;
  undefined4 local_50;
  undefined4 local_4c;
  undefined4 local_48;
  undefined4 local_44;
  undefined4 local_40;
  undefined4 local_3c;
  undefined4 local_38;
  undefined4 local_34;
  undefined4 local_30;
  undefined4 local_2c;
  undefined4 local_28;
  undefined4 local_24;
  undefined4 local_20;
  undefined4 local_1c;
  undefined4 local_18;
  undefined4 local_14;
  undefined4 local_10;
  undefined4 local_c;
  
  memset(acStack_2cc,0,512);
  memset(auStack_cc,0,"@");
  local_8c = 0;
  local_88 = 0;
  local_84 = 0;
  local_80 = 0;
  local_7c = 0;
  local_78 = 0;
  local_74 = 0;
  local_70 = 0;
  local_6c = 0;
  local_68 = 0;
  local_64 = 0;
  local_60 = 0;
  local_5c = 0;
  local_58 = 0;
  local_54 = 0;
  local_50 = 0;
  local_4c = 0;
  local_48 = 0;
  local_44 = 0;
  local_40 = 0;
  local_3c = 0;
  local_38 = 0;
  local_34 = 0;
  local_30 = 0;
  local_2c = 0;
  local_28 = 0;
  local_24 = 0;
  local_20 = 0;
  local_1c = 0;
  local_18 = 0;
  local_14 = 0;
  local_10 = 0;
  apmib_get(0x1b5c,&local_4c);
  apmib_get(0x1b5d,&local_2c);
  iVar1 = mxmlNewXML("1.0");
  if (iVar1 == 0) {
    puts("Create new xml error!!!");
  }
  else {
    iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
    if (iVar2 == 0) {
      puts("Create new element error!!!");
      mxmlDelete(iVar1);
    }
    else {
      mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
      mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
      mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
      iVar2 = mxmlNewElement(iVar2,"soap:Body");
      if (iVar2 == 0) {
        puts("Create new element error!!!");
        mxmlDelete(iVar1);
      }
      else {
        iVar2 = mxmlNewElement(iVar2,"GetQosSettingsResponse");
        if (iVar2 == 0) {
          puts("Create new element error!!!");
          mxmlDelete(iVar1);
        }
        else {
          mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
          iVar3 = mxmlNewElement(iVar2,"GetQoSSettingsResult");
          if (iVar3 == 0) {
            puts("Create new element error!!!");
            mxmlDelete(iVar1);
          }
          else {
            iVar3 = mxmlNewText(iVar3,0,"O");
            if (iVar3 == 0) {
              puts("Create new text error!!!");
              mxmlDelete(iVar1);
            }
            else {
              iVar3 = mxmlNewElement(iVar2,"QoSInfoList");
              if (iVar3 == 0) {
                puts("Create new element error!!!");
                mxmlDelete(iVar1);
              }
              else {
                iVar4 = FUN_0042cc4c("/tmp/new_qos.rule");
                if (iVar4 == 1) {
                  __stream = fopen("/tmp/new_qos.rule","r");
                  if (__stream == 0) {
                    printf("Open file %s error!!!\n","/tmp/new_qos.rule");
                    mxmlDelete(iVar1);
                  }
                  else {
                    do {
                      pcVar6 = fgets(acStack_2cc,512,__stream);
                      if (pcVar6 == 0) {
                        fclose(__stream);
                        goto LAB_0043175c;
                      }
                      sscanf(acStack_2cc,"%63s %15s %17s %*d %*s %*s %d %15s %*s",auStack_cc,
                             &local_8c,&local_7c,&local_c,&local_5c);
                      iVar4 = mxmlNewElement(iVar3,"QoSInfo");
                      if (iVar4 == 0) {
                        puts("Create new element error!!!");
                        mxmlDelete(iVar1);
                        return 0;
                      }
                      iVar5 = mxmlNewElement(iVar4,"Hostname");
                      if (iVar5 == 0) {
                        puts("Create new element error!!!");
                        mxmlDelete(iVar1);
                        return 0;
                      }
                      iVar5 = mxmlNewText(iVar5,0,auStack_cc);
                      if (iVar5 == 0) {
                        puts("Create new text error!!!");
                        mxmlDelete(iVar1);
                        return 0;
                      }
                      iVar5 = mxmlNewElement(iVar4,"IPAddress");
                      if (iVar5 == 0) {
                        puts("Create new element error!!!");
                        mxmlDelete(iVar1);
                        return 0;
                      }
                      iVar5 = mxmlNewText(iVar5,0,&local_8c);
                      if (iVar5 == 0) {
                        puts("Create new text error!!!");
                        mxmlDelete(iVar1);
                        return 0;
                      }
                      iVar5 = mxmlNewElement(iVar4,"MACAddress");
                      if (iVar5 == 0) {
                        puts("Create new element error!!!");
                        mxmlDelete(iVar1);
                        return 0;
                      }
                      iVar5 = mxmlNewText(iVar5,0,&local_7c);
                      if (iVar5 == 0) {
                        puts("Create new text error!!!");
                        mxmlDelete(iVar1);
                        return 0;
                      }
                      iVar5 = mxmlNewElement(iVar4,"Priority");
                      if (iVar5 == 0) {
                        puts("Create new element error!!!");
                        mxmlDelete(iVar1);
                        return 0;
                      }
                      iVar5 = mxmlNewInteger(iVar5,local_c);
                      if (iVar5 == 0) {
                        puts("Create new text error!!!");
                        mxmlDelete(iVar1);
                        return 0;
                      }
                      iVar4 = mxmlNewElement(iVar4,"Type");
                      if (iVar4 == 0) {
                        puts("Create new element error!!!");
                        mxmlDelete(iVar1);
                        return 0;
                      }
                      iVar4 = mxmlNewText(iVar4,0,&local_5c);
                    } while (iVar4 != 0);
                    puts("Create new text error!!!");
                    mxmlDelete(iVar1);
                  }
                }
                else {
LAB_0043175c:
                  iVar3 = mxmlNewElement(iVar2,"UploadBandwidth");
                  if (iVar3 == 0) {
                    puts("Create new element error!!!");
                    mxmlDelete(iVar1);
                  }
                  else {
                    iVar3 = mxmlNewText(iVar3,0,&local_4c);
                    if (iVar3 == 0) {
                      puts("Create new text error!!!");
                      mxmlDelete(iVar1);
                    }
                    else {
                      iVar2 = mxmlNewElement(iVar2,"DownloadBandwidth");
                      if (iVar2 == 0) {
                        puts("Create new element error!!!");
                        mxmlDelete(iVar1);
                      }
                      else {
                        iVar2 = mxmlNewText(iVar2,0,&local_2c);
                        if (iVar2 == 0) {
                          puts("Create new text error!!!");
                          mxmlDelete(iVar1);
                        }
                        else {
                          __ptr = mxmlSaveAllocString(iVar1,0);
                          if (__ptr != 0) {
                            FUN_0041ed70("",200,__ptr,"");
                            free(__ptr);
                          }
                          mxmlDelete(iVar1);
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

