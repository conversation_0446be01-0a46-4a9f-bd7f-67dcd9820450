
undefined4 FUN_0044e308(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  void *__ptr;
  undefined4 local_14;
  char local_10 [8];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetWanConnectionType");
  }
  else {
    local_10[0] = '\0';
    local_10[1] = '\0';
    local_10[2] = '\0';
    local_10[3] = '\0';
    iVar1 = FUN_00451ef0("eth1",3);
    if (iVar1 == 1) {
      local_14 = 1;
    }
    else if (iVar1 == 2) {
      local_14 = 2;
    }
    else if (iVar1 == 0) {
      local_14 = 0;
    }
    else {
      local_14 = 0xffffffff;
    }
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar2 = mxmlNewElement(iVar2,"soap:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"GetWanConnectionTypeResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("GetWanConnectionTypeResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar3 = mxmlNewElement(iVar2,"GetWanConnectionTypeResult");
            if (iVar3 == 0) {
              mxmlDelete(iVar1);
              puts("GetWanConnectionTypeResult=NULL");
            }
            else {
              mxmlNewText(iVar3,0,FUN_004ad49c);
              iVar2 = mxmlNewElement(iVar2,"Type");
              if (iVar2 == 0) {
                mxmlDelete(iVar1);
                puts("Type=NULL");
              }
              else {
                snprintf(local_10,3,"%d",local_14);
                mxmlNewText(iVar2,0,local_10);
                if ("" == 0) {
                  __ptr = mxmlSaveAllocString(iVar1,0);
                  if (__ptr != 0) {
                    FUN_0041ed70("",200,__ptr,"");
                    free(__ptr);
                  }
                }
                mxmlDelete(iVar1);
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

