
/* WARNING: Type propagation algorithm not settling */

int FUN_00450b50(undefined4 param_1)

{
  int iVar1;
  short sVar2;
  char *__nptr;
  undefined *puVar3;
  int iVar4;
  int local_450;
  undefined4 local_44c;
  undefined4 local_448;
  int local_444 [2];
  int *local_43c;
  undefined4 local_438;
  undefined4 *local_434;
  undefined4 local_430;
  undefined4 *local_42c;
  undefined4 local_428;
  int *local_424;
  undefined4 local_420;
  int *local_41c;
  undefined4 local_418;
  undefined4 local_414;
  undefined auStack_40c [10];
  undefined auStack_402 [30];
  undefined auStack_3e4 [30];
  undefined auStack_3c6 [934];
  
  local_444[1] = 10;
  local_43c = &local_450;
  local_434 = &local_44c;
  local_42c = &local_448;
  local_424 = local_444 + 1;
  local_41c = local_444;
  local_450 = 1;
  local_44c = 0;
  local_448 = 0;
  local_444[0] = 0;
  local_414 = 0;
  local_438 = 0;
  local_430 = 0;
  local_428 = 0;
  local_420 = 0;
  local_418 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar2 = HttpDenyPage(param_1);
  }
  else {
    __nptr = httpGetEnv(param_1,"curPage");
    if (__nptr == 0) {
      local_450 = 1;
    }
    else {
      local_450 = atoi(__nptr);
    }
    printf("curPage = %d \n",local_450);
    FUN_00450464(&local_450,&local_448,&local_44c,local_444 + 1,local_444,auStack_40c);
    printf("totalItem =%d \n",local_448);
    printf("totalPage =%d \n",local_44c);
    printf("curPage =%d \n",local_450);
    printf("pageNum =%d \n",local_444[0]);
    puVar3 = auStack_40c;
    iVar1 = 0;
    while( true ) {
      if (local_444[0] <= iVar1) break;
      printf("%d %s %s %s %s\n",iVar1,puVar3,puVar3 + 10,puVar3 + "(",puVar3 + "F");
      puVar3 = puVar3 + 100;
      iVar1 = iVar1 + 1;
    }
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "pageInfo");
    pageDynParaPrintf(&local_43c,0,param_1);
    pageDynParaPrintf(&local_43c,1,param_1);
    pageDynParaPrintf(&local_43c,2,param_1);
    pageDynParaPrintf(&local_43c,3,param_1);
    pageDynParaPrintf(&local_43c,4,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "actSessionList");
    for (iVar1 = 0; iVar1 < local_444[0]; iVar1 = iVar1 + 1) {
      iVar4 = iVar1 * 100;
      writePageParamSet(param_1,""%s",","Used",0);
      writePageParamSet(param_1,""%s",",auStack_40c + iVar4,1);
      writePageParamSet(param_1,""%s",",auStack_402 + iVar4,2);
      writePageParamSet(param_1,""%s",",auStack_3e4 + iVar4,3);
      writePageParamSet(param_1,""%s",",auStack_3c6 + iVar4,4);
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,0);
    iVar1 = httpRpmFsA(param_1,"/userRpm/activeSessionRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar2 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar2;
}

