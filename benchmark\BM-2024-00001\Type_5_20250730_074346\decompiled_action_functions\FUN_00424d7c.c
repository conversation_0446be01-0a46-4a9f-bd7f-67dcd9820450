
/* WARNING: Type propagation algorithm not settling */

int FUN_00424d7c(undefined4 param_1)

{
  int iVar1;
  short sVar9;
  uint uVar2;
  undefined4 uVar3;
  char *pcVar4;
  size_t sVar5;
  char *pcVar6;
  int iVar7;
  uint uVar8;
  uint uVar10;
  uint uVar11;
  uint local_170;
  uint local_16c;
  undefined auStack_168 [4];
  undefined auStack_164 [4];
  undefined auStack_160 [4];
  undefined auStack_15c [4];
  undefined auStack_158 [4];
  undefined4 local_154;
  char acStack_150 [24];
  undefined local_138;
  char local_137;
  char local_136 [9];
  undefined local_12d;
  undefined *local_12c;
  undefined4 local_128;
  undefined *local_124;
  undefined4 local_120;
  undefined *local_11c;
  undefined4 local_118;
  undefined *local_114;
  undefined4 local_110;
  undefined *local_10c;
  undefined4 local_108;
  undefined4 local_104;
  undefined auStack_fc [28];
  undefined4 local_e0;
  uint local_dc;
  uint local_d8;
  uint local_d4;
  uint local_d0;
  uint local_cc;
  uint local_c8;
  uint local_c4 [2];
  char acStack_bc [5];
  char acStack_b7 [7];
  undefined *local_b0;
  undefined4 local_ac;
  undefined4 *local_a8;
  undefined4 local_a4;
  uint *local_a0;
  undefined4 local_9c;
  uint *local_98;
  undefined4 local_94;
  uint *local_90;
  undefined4 local_8c;
  uint *local_88;
  undefined4 local_84;
  uint *local_80;
  undefined4 local_7c;
  uint *local_78;
  undefined4 local_74;
  uint *local_70;
  undefined4 local_6c;
  uint *local_68;
  undefined4 local_64;
  char *local_60;
  undefined4 local_5c;
  char *local_58;
  undefined4 local_54;
  undefined4 local_50;
  byte local_48 [4];
  undefined4 *local_44;
  undefined *local_40;
  undefined **local_3c;
  char *local_38;
  char *local_34;
  char *local_30;
  byte *local_2c;
  
  local_16c = 0;
  swGetScheduleTableSize();
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar9 = HttpDenyPage(param_1);
    goto LAB_0042596c;
  }
  iVar1 = httpGetEnv(param_1,"Add");
  if ((iVar1 != 0) || (iVar1 = httpGetEnv(param_1,"Modify"), iVar1 != 0)) {
    iVar1 = FUN_0042486c(param_1);
    return iVar1;
  }
  memset(&local_154,0,"(");
  local_12c = auStack_168;
  local_124 = auStack_164;
  local_11c = auStack_160;
  local_114 = auStack_15c;
  local_10c = auStack_158;
  local_b0 = auStack_fc;
  local_a8 = &local_e0;
  local_a0 = &local_dc;
  local_98 = &local_d8;
  local_90 = &local_d4;
  local_88 = &local_d0;
  local_80 = &local_cc;
  local_78 = &local_c8;
  local_70 = local_c4;
  local_68 = local_c4 + 1;
  local_60 = acStack_bc;
  local_58 = acStack_b7;
  local_ac = 25;
  local_104 = 0;
  local_128 = 0;
  local_120 = 0;
  local_118 = 0;
  local_110 = 0;
  local_108 = 0;
  local_50 = 0;
  local_a4 = 0;
  local_9c = 0;
  local_94 = 0;
  local_8c = 0;
  local_84 = 0;
  local_7c = 0;
  local_74 = 0;
  local_6c = 0;
  local_54 = 5;
  local_5c = 5;
  local_64 = 0;
  uVar2 = getEnvToInt(param_1,"Page",1,0x7fffffff);
  uVar10 = 1;
  if (uVar2 != 0xffffff80) {
    uVar10 = uVar2;
  }
  uVar3 = swGetScheduleTableSize();
  getEnvToInt(param_1,"EntryIndex",0,uVar3);
  pcVar4 = httpGetEnv(param_1,"doAll");
  if (pcVar4 == 0) {
    pcVar4 = httpGetEnv(param_1,"Del");
    if (pcVar4 != 0) {
      iVar1 = atoi(pcVar4);
      uVar3 = swDelFilterEntry(2,(uVar10 - 1) * 8 + iVar1,1);
      goto LAB_00425514;
    }
    iVar1 = httpGetEnv(param_1,"Save");
    if (iVar1 == 0) goto LAB_00425510;
    memset(&local_154,0,"(");
    local_154 = 1;
    pcVar4 = httpGetEnv(param_1,"time_sched_name");
    if (pcVar4 != 0) {
      local_138 = 0;
      strncpy(acStack_150,pcVar4,24);
    }
    iVar1 = getEnvToInt(param_1,"day_type",0,1);
    local_137 = -128;
    if (iVar1 == 0) {
      local_170 = 0;
      iVar1 = httpGetEnv(param_1,"Mon_select");
      if (iVar1 != 0) {
        local_170 = 0x1000000;
      }
      iVar1 = httpGetEnv(param_1,"Tue_select");
      if (iVar1 != 0) {
        local_170 = local_170 | 0x2000000;
      }
      iVar1 = httpGetEnv(param_1,"Wed_select");
      if (iVar1 != 0) {
        local_170 = local_170 | 0x4000000;
      }
      iVar1 = httpGetEnv(param_1,"Thu_select");
      if (iVar1 != 0) {
        local_170 = local_170 | 0x8000000;
      }
      iVar1 = httpGetEnv(param_1,"Fri_select");
      if (iVar1 != 0) {
        local_170 = local_170 | 0x10000000;
      }
      iVar1 = httpGetEnv(param_1,"Sat_select");
      if (iVar1 != 0) {
        local_170 = local_170 | 0x20000000;
      }
      iVar1 = httpGetEnv(param_1,"Sun_select");
      if (iVar1 != 0) {
        local_170 = local_170 | 0x40000000;
      }
      local_137 = swWeekDaysToUint8(local_170);
    }
    iVar1 = httpGetEnv(param_1,"all_hours");
    if (iVar1 != 0) {
      builtin_strncpy(local_136,"0000",5);
      local_12d = 0;
      memcpy(local_136 + 5,"2400",4);
LAB_0042546c:
      pcVar4 = httpGetEnv(param_1,"Changed");
      iVar1 = strcmp(pcVar4,"1");
      if (iVar1 == 0) {
        pcVar4 = httpGetEnv(param_1,"SelIndex");
        iVar1 = 12;
        if (pcVar4 != 0) {
          iVar1 = atoi(pcVar4);
        }
        uVar3 = 1;
      }
      else {
        iVar1 = 0;
        uVar3 = 0;
      }
      uVar3 = swSetScheduleEntry(&local_154,iVar1,uVar3,1);
      goto LAB_00425514;
    }
    pcVar4 = httpGetEnv(param_1,"time_sched_start_time");
    if (pcVar4 == 0) {
LAB_004253a4:
      iVar1 = 8000;
    }
    else {
      local_136[4] = 0;
      strncpy(local_136,pcVar4,4);
      iVar1 = 0;
      sVar5 = strlen(local_136);
      pcVar6 = pcVar4;
      if (sVar5 != 4) goto LAB_004253a4;
      do {
        iVar1 = iVar1 + 1;
        if (9 < (byte)(*pcVar6 - 0x30U)) goto LAB_004253a4;
        pcVar6 = pcVar4 + iVar1;
      } while (iVar1 != 4);
      iVar1 = swChkLegalFirewallTime(local_136);
      if (iVar1 == 0) goto LAB_004253a4;
      pcVar4 = httpGetEnv(param_1,"time_sched_end_time");
      if (pcVar4 != 0) {
        local_12d = 0;
        strncpy(local_136 + 5,pcVar4,4);
        sVar5 = strlen(local_136 + 5);
        iVar1 = 0;
        pcVar6 = pcVar4;
        if (sVar5 == 4) {
          do {
            iVar1 = iVar1 + 1;
            if (9 < (byte)(*pcVar6 - 0x30U)) goto LAB_0042545c;
            pcVar6 = pcVar4 + iVar1;
          } while (iVar1 != 4);
          iVar1 = swChkLegalFirewallTime(local_136 + 5);
          if (iVar1 != 0) goto LAB_0042546c;
        }
      }
LAB_0042545c:
      iVar1 = 0x1f41;
    }
LAB_00425960:
    pcVar4 = 0;
  }
  else {
    iVar1 = strcmp(pcVar4,"DelAll");
    if (iVar1 == 0) {
      uVar3 = swDelAllFilterEntry(2);
    }
    else {
LAB_00425510:
      uVar3 = 0;
    }
LAB_00425514:
    iVar1 = swFilterFindErrorNum(uVar3);
    if (iVar1 == 0) {
      memset(&local_154,0,"(");
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "timeSchedList");
      iVar1 = swGetScheduleEntry((uVar10 - 1) * 8,&local_154);
      if ((iVar1 != 0) || (iVar1 = uVar10 - 2, uVar2 = uVar10 - 1, uVar10 < 2)) {
        iVar1 = uVar10 - 1;
        uVar2 = uVar10;
      }
      local_44 = &local_154;
      local_40 = auStack_fc;
      local_3c = &local_b0;
      local_38 = acStack_150;
      local_34 = acStack_bc;
      local_30 = acStack_b7;
      local_2c = local_48;
      uVar10 = 0;
      uVar11 = 0;
      while (uVar8 = swGetScheduleTableSize(), uVar10 < uVar8) {
        memset(local_44,0,"(");
        memset(local_40,0,"L");
        iVar7 = swGetScheduleEntry(uVar10,local_44);
        if (iVar7 == 0) break;
        uVar10 = uVar10 + 1;
        if (iVar1 << 3 < uVar10) {
          if ((int)(uVar2 << 3) < uVar10) break;
          pageParaSet(local_3c,local_38,0);
          if (local_137 < '\0') {
            local_e0 = 1;
          }
          else {
            local_e0 = 0;
            swUint8ToWeekDays(local_2c,local_137);
            local_dc = local_48[0] & 1;
            local_d8 = ((local_48[0] & 2) << 24) >> 25;
            local_d4 = ((local_48[0] & 4) << 24) >> 26;
            local_d0 = ((local_48[0] & 8) << 24) >> 27;
            local_cc = ((local_48[0] & 16) << 24) >> 28;
            local_c8 = ((local_48[0] & " ") << 24) >> 29;
            local_c4[0] = ((local_48[0] & "@") << 24) >> 30;
          }
          iVar7 = strcmp(local_136,"0000");
          if ((iVar7 == 0) && (iVar7 = strcmp(local_136 + 5,"2400"), iVar7 == 0)) {
            local_c4[1] = 1;
          }
          else {
            local_c4[1] = 0;
          }
          uVar11 = uVar11 + 1;
          strcpy(local_34,local_136);
          strcpy(local_30,local_136 + 5);
          pageDynParaListPrintf(local_3c,param_1);
        }
      }
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      local_16c = uVar2;
      pageParaSet(&local_12c,&local_16c,0);
      local_16c = (uint)((int)(uVar2 << 3) < uVar10);
      pageParaSet(&local_12c,&local_16c,1);
      local_16c = uVar11;
      pageParaSet(&local_12c,&local_16c,2);
      local_16c = 12;
      pageParaSet(&local_12c,&local_16c,3);
      local_16c = swGetFilterEntryNumCfg(2);
      pageParaSet(&local_12c,&local_16c,4);
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "timeSchedPara");
      pageDynParaListPrintf(&local_12c,param_1);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      HttpWebV4Head(param_1,0,1);
      iVar1 = httpRpmFsA(param_1,"/userRpm/AccessCtrlTimeSchedRpm.htm");
      if (iVar1 == 2) {
        return 2;
      }
      iVar1 = 10;
      goto LAB_00425960;
    }
    if (iVar1 != 0x714f) goto LAB_00425960;
    pcVar4 = "../userRpm/AccessCtrlTimeSchedRpm.htm";
    iVar1 = 0x714f;
  }
  sVar9 = HttpErrorPage(param_1,iVar1,pcVar4,0);
LAB_0042596c:
  return sVar9;
}

