
int FUN_0045c4ec(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  short sVar8;
  char *pcVar3;
  uint32_t uVar4;
  uint32_t uVar5;
  uint32_t uVar6;
  uint32_t uVar7;
  int iVar9;
  uint uVar10;
  uint uVar11;
  uint local_168;
  undefined auStack_164 [10];
  ushort local_15a;
  char acStack_158 [16];
  char local_148 [16];
  uint32_t local_138;
  undefined4 local_134;
  undefined4 local_130;
  uint local_12c;
  uint local_128;
  short local_124;
  short local_122;
  undefined2 local_120;
  undefined2 local_11e;
  uint32_t local_11c;
  undefined2 local_118;
  uint local_114;
  char acStack_110 [68];
  undefined auStack_cc [16];
  undefined auStack_bc [16];
  undefined auStack_ac [16];
  undefined4 local_9c;
  undefined4 local_98;
  undefined auStack_94 [4];
  undefined auStack_90 [4];
  uint local_8c;
  uint local_88;
  undefined *local_84;
  undefined4 local_80;
  undefined *local_7c;
  undefined4 local_78;
  undefined *local_74;
  undefined4 local_70;
  undefined4 *local_6c;
  undefined4 local_68;
  undefined4 *local_64;
  undefined4 local_60;
  undefined *local_5c;
  undefined4 local_58;
  undefined *local_54;
  undefined4 local_50;
  uint *local_4c;
  undefined4 local_48;
  uint *local_44;
  undefined4 local_40;
  undefined4 local_3c;
  int local_30;
  
  local_30 = HttpIsAccessFromLAN();
  local_84 = auStack_cc;
  local_7c = auStack_bc;
  local_74 = auStack_ac;
  local_6c = &local_9c;
  local_64 = &local_98;
  local_5c = auStack_94;
  local_54 = auStack_90;
  local_4c = &local_8c;
  local_44 = &local_88;
  local_70 = 16;
  local_80 = 16;
  local_78 = 16;
  local_3c = 0;
  local_68 = 0;
  local_60 = 0;
  local_58 = 0;
  local_50 = 0;
  local_48 = 0;
  local_40 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    sVar8 = HttpDenyPage(param_1);
    goto LAB_0045cb40;
  }
  local_128 = 0;
  local_138 = 0;
  local_134 = 0;
  local_130 = 0;
  local_12c = 0;
  pcVar3 = httpGetEnv(param_1,"Page");
  uVar11 = 1;
  if (pcVar3 != 0) {
    uVar11 = atoi(pcVar3);
    iVar2 = getDefaultVSTblSize();
    if (((int)(((uint)(iVar2 >> 31) >> 29) + iVar2) >> 3 < uVar11) || (uVar11 < 1)) {
      uVar11 = 1;
    }
  }
  iVar2 = httpGetEnv(param_1,"Add");
  uVar10 = 0;
  if (iVar2 == 0) {
    pcVar3 = httpGetEnv(param_1,"Modify");
    if (pcVar3 == 0) {
      iVar2 = FUN_0045b83c(param_1);
      return iVar2;
    }
    uVar10 = atoi(pcVar3);
    if (-1 < uVar10) {
      bVar1 = true;
      iVar2 = getDefaultVSTblSize();
      if (uVar10 < iVar2) goto LAB_0045c744;
    }
    uVar10 = 0;
    bVar1 = true;
  }
  else {
    bVar1 = false;
  }
LAB_0045c744:
  memset(auStack_cc,0,"H");
  swGetFirewallHttpCtrl(auStack_164);
  if (bVar1) {
    swGetVsEntry(uVar10,&local_124);
    local_12c = local_114;
    local_128 = CONCAT22(local_118,local_128._2_2_);
    local_130 = CONCAT22(local_120,local_11e);
    local_138 = local_11c;
    local_134 = CONCAT22(local_124,local_122);
    if (local_124 == local_122) {
      sprintf(local_148,"%u");
    }
    else {
      sprintf(local_148,"%u-%u");
    }
    pageParaSet(&local_84,local_148,0);
    if (local_134 == local_130) {
      local_148[12] = '\0';
      local_148[13] = '\0';
      local_148[14] = '\0';
      local_148[15] = '\0';
      local_148[0] = '\0';
      local_148[1] = '\0';
      local_148[2] = '\0';
      local_148[3] = '\0';
      local_148[4] = '\0';
      local_148[5] = '\0';
      local_148[6] = '\0';
      local_148[7] = '\0';
      local_148[8] = '\0';
      local_148[9] = '\0';
      local_148[10] = '\0';
      local_148[11] = '\0';
    }
    else {
      local_148[0] = '\0';
      local_148[1] = '\0';
      local_148[2] = '\0';
      local_148[3] = '\0';
      local_148[4] = '\0';
      local_148[5] = '\0';
      local_148[6] = '\0';
      local_148[7] = '\0';
      local_148[8] = '\0';
      local_148[9] = '\0';
      local_148[10] = '\0';
      local_148[11] = '\0';
      local_148[12] = '\0';
      local_148[13] = '\0';
      local_148[14] = '\0';
      local_148[15] = '\0';
      sprintf(local_148,"%u",local_130 >> 16);
    }
    pageParaSet(&local_84,local_148,1);
    uVar4 = ntohl(local_138);
    uVar5 = ntohl(local_138);
    uVar6 = ntohl(local_138);
    uVar7 = ntohl(local_138);
    sprintf(acStack_158,"%d.%d.%d.%d",uVar4 >> 24,uVar5 >> 16 & 255,(int)(uVar6 & -256) >> 8,
            uVar7 & 255);
    pageParaSet(&local_84,acStack_158,2);
    local_168 = local_128 >> 16;
    pageParaSet(&local_84,&local_168,3);
    local_168 = local_12c;
    pageParaSet(&local_84,&local_168,4);
    local_168 = 1;
    pageParaSet(&local_84,&local_168,5);
    local_168 = uVar10;
    pageParaSet(&local_84,&local_168,6);
    local_168 = uVar11;
    pageParaSet(&local_84,&local_168,7);
    local_168 = local_15a;
    pageParaSet(&local_84,&local_168,8);
  }
  else {
    local_88 = local_15a;
    local_9c = 1;
    local_98 = 1;
    local_8c = uVar11;
  }
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "vsEditInf");
  iVar2 = 0;
  do {
    iVar9 = iVar2 + 1;
    pageDynParaPrintf(&local_84,iVar2,param_1);
    iVar2 = iVar9;
  } while (iVar9 != 9);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  HttpWebV4Head(param_1,0,1);
  memset(acStack_110,0,"A");
  iVar2 = isOEMWanLogin();
  if ((iVar2 == 0) || (local_30 == 0)) {
    pcVar3 = "/userRpm/VirtualServerAdvRpm.htm";
  }
  else {
    pcVar3 = "/userRpm/VirtualServerAdvRpm_Ltv.htm";
  }
  strcpy(acStack_110,pcVar3);
  iVar2 = httpRpmFsA(param_1,acStack_110);
  if (iVar2 == 2) {
    return 2;
  }
  sVar8 = HttpErrorPage(param_1,10,0,0);
LAB_0045cb40:
  return sVar8;
}

