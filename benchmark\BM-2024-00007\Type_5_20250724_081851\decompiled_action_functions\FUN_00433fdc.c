
int FUN_00433fdc(undefined4 param_1)

{
  int iVar1;
  char *pcVar2;
  long lVar3;
  size_t sVar4;
  short sVar5;
  char *pcVar6;
  undefined1 *puVar7;
  undefined4 uVar8;
  int iVar9;
  uint uVar10;
  code *pcVar11;
  uint uStack_360;
  int iStack_35c;
  undefined4 auStack_358 [2];
  int iStack_350;
  in_addr_t iStack_34c;
  in_addr_t iStack_348;
  undefined4 uStack_344;
  char acStack_340 [16];
  undefined *puStack_330;
  undefined4 uStack_32c;
  undefined *puStack_328;
  undefined4 uStack_324;
  undefined *puStack_320;
  undefined4 uStack_31c;
  undefined *puStack_318;
  undefined4 uStack_314;
  undefined4 *puStack_310;
  undefined4 uStack_30c;
  undefined4 *puStack_308;
  undefined4 uStack_304;
  undefined4 *puStack_300;
  undefined4 uStack_2fc;
  undefined *puStack_2f8;
  undefined4 uStack_2f4;
  undefined *puStack_2f0;
  undefined4 uStack_2ec;
  undefined *puStack_2e8;
  undefined4 uStack_2e4;
  undefined4 uStack_2e0;
  undefined auStack_2d8 [4];
  undefined auStack_2d4 [4];
  undefined auStack_2d0 [120];
  undefined auStack_258 [120];
  undefined4 uStack_1e0;
  undefined4 uStack_1dc;
  undefined4 uStack_1d8;
  undefined auStack_1d4 [4];
  undefined auStack_1d0 [16];
  undefined auStack_1c0 [16];
  undefined auStack_1b0 [119];
  undefined uStack_139;
  undefined auStack_138 [119];
  undefined uStack_c1;
  undefined4 uStack_4c;
  
  uStack_360 = 0;
  iStack_35c = 0;
  memset(auStack_2d8,0,296);
  puStack_328 = auStack_2d4;
  puStack_320 = auStack_2d0;
  puStack_318 = auStack_258;
  puStack_310 = &uStack_1e0;
  puStack_308 = &uStack_1dc;
  puStack_300 = &uStack_1d8;
  puStack_2f8 = auStack_1d4;
  puStack_2f0 = auStack_1d0;
  puStack_2e8 = auStack_1c0;
  uStack_314 = "x";
  uStack_31c = "x";
  uStack_2e4 = 16;
  uStack_2ec = 16;
  uStack_2e0 = 0;
  uStack_32c = 0;
  uStack_324 = 0;
  uStack_30c = 0;
  uStack_304 = 0;
  uStack_2fc = 0;
  uStack_2f4 = 0;
  puStack_330 = auStack_2d8;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    iVar1 = HttpDenyPage(param_1);
    iVar1 = iVar1 << 16;
    goto LAB_00434540;
  }
  memset(auStack_1b0,0,400);
  uStack_344 = 0;
  iStack_350 = 0;
  iStack_34c = 0;
  iStack_348 = 0;
  pcVar2 = httpGetEnv(param_1,"ClientId");
  uVar10 = 0;
  if (pcVar2 != 0) {
    lVar3 = atol(pcVar2);
    uVar10 = (uint)lVar3;
  }
  iVar1 = httpGetEnv(param_1,"Next");
  if (iVar1 == 0) {
    iVar1 = httpGetEnv(param_1,"Return");
    if (iVar1 == 0) {
      swGetPppoeCfg(auStack_1b0);
      swGetPppoePlusCfg(&iStack_350);
      uStack_360 = uVar10;
      pageParaSet(&puStack_330,&uStack_360,0);
      uStack_360 = 0;
      pageParaSet(&puStack_330,&uStack_360,1);
      pageParaSet(&puStack_330,auStack_1b0,2);
      pageParaSet(&puStack_330,auStack_138,3);
      swGetSystemMode(auStack_358);
      uStack_1e0 = 3;
      uStack_1dc = swIsMultiSystemMode();
      uStack_1d8 = auStack_358[0];
      pageParaSet(&puStack_330,&iStack_350,7);
      uStack_360 = iStack_34c;
      sprintf(acStack_340,"%d.%d.%d.%d",iStack_34c >> 24,iStack_34c >> 16 & 255,
              iStack_34c >> 8 & 255,iStack_34c & 255);
      pageParaSet(&puStack_330,acStack_340,8);
      uStack_360 = iStack_348;
      sprintf(acStack_340,"%d.%d.%d.%d",iStack_348 >> 24,iStack_348 >> 16 & 255,
              iStack_348 >> 8 & 255,iStack_348 & 255);
      pageParaSet(&puStack_330,acStack_340,9);
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "wzdPPPoEInf");
      iVar1 = 0;
      do {
        iVar9 = iVar1 + 1;
        pageDynParaPrintf(&puStack_330,iVar1,param_1);
        iVar1 = iVar9;
      } while (iVar9 != 10);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      httpWizardPrintStepInfo(param_1);
      HttpWebV4Head(param_1,0,0);
      iVar1 = httpRpmFsA(param_1,"/userRpm/WzdPPPoERpm.htm");
      if (iVar1 == 2) {
        return 2;
      }
      iVar1 = 10;
      puVar7 = 0;
      goto LAB_004345ec;
    }
    pcVar11 = wzdStepFindPrev;
LAB_0043462c:
    iVar1 = (*pcVar11)(&iStack_35c);
    if (iVar1 == 0) {
      return 2;
    }
    iVar1 = GoUrl(param_1,iStack_35c + 8);
    iVar1 = iVar1 << 16;
  }
  else {
    swGetPppoeCfg(auStack_1b0);
    pcVar2 = httpGetEnv(param_1,"acc");
    if (pcVar2 == 0) {
LAB_00434248:
      pcVar11 = memset;
      pcVar6 = 0;
      uVar8 = "x";
    }
    else {
      do {
        pcVar6 = pcVar2;
        pcVar2 = pcVar6 + 1;
      } while (*pcVar6 == ' ');
      if ((pcVar6 == 0) || (*pcVar6 == '\0')) goto LAB_00434248;
      sVar4 = strlen(pcVar6);
      if ("w" < sVar4) {
        puVar7 = "";
        iVar1 = 0x3eb;
        goto LAB_004345ec;
      }
      pcVar11 = strncpy;
      uStack_139 = 0;
      uVar8 = "w";
    }
    (*pcVar11)(auStack_1b0,pcVar6,uVar8);
    pcVar2 = httpGetEnv(param_1,"psw");
    if (pcVar2 == 0) {
LAB_00434300:
      pcVar11 = memset;
      pcVar6 = 0;
      uVar8 = "x";
    }
    else {
      do {
        pcVar6 = pcVar2;
        pcVar2 = pcVar6 + 1;
      } while (*pcVar6 == ' ');
      if ((pcVar6 == 0) || (*pcVar6 == '\0')) goto LAB_00434300;
      sVar4 = strlen(pcVar6);
      if ("w" < sVar4) {
        puVar7 = "";
        iVar1 = 0x3ec;
        goto LAB_004345ec;
      }
      pcVar11 = strncpy;
      uStack_c1 = 0;
      uVar8 = "w";
    }
    (*pcVar11)(auStack_138,pcVar6,uVar8);
    pcVar2 = httpGetEnv(param_1,"SecType");
    if (pcVar2 == 0) {
      iStack_350 = 0;
    }
    else {
      iStack_350 = atoi(pcVar2);
    }
    if (iStack_350 == 2) {
      pcVar2 = httpGetEnv(param_1,"sta_ip");
      iVar1 = swChkDotIpAddr(pcVar2);
      if (iVar1 == 0) {
        puVar7 = "";
        iVar1 = 0x138b;
      }
      else {
        iStack_34c = inet_addr(pcVar2);
        pcVar2 = httpGetEnv(param_1,"sta_mask");
        iVar1 = swChkDotIpAddr(pcVar2);
        if (iVar1 != 0) {
          iStack_348 = inet_addr(pcVar2);
          goto LAB_00434434;
        }
        puVar7 = "";
        iVar1 = 0x138c;
      }
    }
    else {
LAB_00434434:
      uStack_344 = 0x5c8;
      sVar5 = swChkPppoeCfg(auStack_1b0);
      iVar1 = sVar5;
      if (iVar1 == 0) {
        sVar5 = swChkPppoePlusCfg(&iStack_350);
        iVar1 = sVar5;
        if (iVar1 == 0) {
          uStack_4c = 2;
          swSetPppoeCfg(auStack_1b0);
          swSetPppoePlusCfg(&iStack_350);
          swSecLinkUpReq();
          taskDelay("<");
          pcVar11 = wzdStepFindNext;
          goto LAB_0043462c;
        }
      }
      puVar7 = "";
    }
LAB_004345ec:
    iVar1 = HttpErrorPage(param_1,iVar1,puVar7,0);
    iVar1 = iVar1 << 16;
  }
LAB_00434540:
  return iVar1 >> 16;
}

