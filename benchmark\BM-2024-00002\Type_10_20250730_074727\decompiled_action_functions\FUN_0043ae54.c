
undefined4 FUN_0043ae54(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  int iVar3;
  int iVar4;
  char *pcVar5;
  size_t sVar6;
  uint uVar7;
  uint uVar8;
  int local_c0;
  undefined local_b8;
  int local_94;
  in_addr iStack_80;
  in_addr iStack_7c;
  undefined2 local_78;
  undefined2 local_76;
  undefined local_74;
  in_addr iStack_73;
  undefined auStack_6f [7];
  undefined4 local_68;
  undefined4 local_50;
  undefined4 local_4c;
  undefined auStack_48 [64];
  
  local_50 = 0;
  local_4c = 0;
  bVar1 = true;
  local_c0 = 0;
  memset(auStack_48,0,"@");
  iVar2 = mxmlLoadString(0,param_1,0);
  if (iVar2 == 0) {
    return 0;
  }
  iVar3 = mxmlFindElement(iVar2,iVar2,"soap:Envelope",0,0,1);
  if ((iVar3 != 0) &&
     (iVar4 = mxmlFindElement(iVar3,iVar2,"SetIPv4FirewallSettings",0,0,1), iVar4 != 0)) {
    iVar4 = mxmlFindElement(iVar3,iVar2,"IPv4_FirewallStatus",0,0,1);
    if ((iVar4 != 0) && (pcVar5 = mxmlGetText(iVar4,0), pcVar5 != 0)) {
      iVar4 = strcmp(pcVar5,"Disable");
      if (iVar4 == 0) {
        local_50 = 0;
      }
      else {
        iVar4 = strcmp(pcVar5,"Enable_BlackList");
        if (iVar4 == 0) {
          local_50 = 1;
          local_4c = 0;
        }
        else {
          iVar4 = strcmp(pcVar5,"Enable_WhiteList");
          if (iVar4 != 0) {
            bVar1 = false;
            memcpy(auStack_48,"ERROR",6);
            goto LAB_0043bce0;
          }
          local_50 = 1;
          local_4c = 1;
        }
      }
      apmib_set("t",&local_50);
      apmib_set(0x1b5a,&local_4c);
    }
    local_94 = mxmlFindElement(iVar3,iVar2,"IPv4FirewallRuleLists",0,0,1);
    if (local_94 != 0) {
      apmib_set(0x40079,&iStack_80);
      do {
        memset(&iStack_80,0,"0");
        iVar3 = mxmlFindElement(local_94,iVar2,"IPv4FirewallRule",0,0,1);
        if (iVar3 != 0) {
          local_c0 = local_c0 + 1;
          if (24 < local_c0) {
            bVar1 = false;
            memcpy(auStack_48,"ERROR",6);
            break;
          }
          iVar4 = mxmlFindElement(iVar3,iVar2,"Name",0,0,1);
          if ((iVar4 != 0) && (pcVar5 = mxmlGetText(iVar4,0), pcVar5 != 0)) {
            sVar6 = strlen(pcVar5);
            if (20 < sVar6) {
              bVar1 = false;
              memcpy(auStack_48,"ERROR_BAD_NAME",15);
              break;
            }
            strncpy((char *)(&local_68 + 3),pcVar5,20);
          }
          iVar4 = mxmlFindElement(iVar3,iVar2,"SrcInterface",0,0,1);
          if (iVar4 != 0) {
            pcVar5 = mxmlGetText(iVar4,0);
            if (pcVar5 == 0) {
              local_b8 = 1;
            }
            else {
              iVar4 = strcmp(pcVar5,&PTR_0x004e414c);
              if (iVar4 == 0) {
                local_b8 = 1;
              }
              else {
                iVar4 = strcmp(pcVar5,&PTR_0x004e4157);
                if (iVar4 != 0) {
                  bVar1 = false;
                  memcpy(auStack_48,"ERROR_BAD_SRCINTERFACE",23);
                  break;
                }
                local_b8 = 2;
              }
            }
            local_74 = local_b8;
          }
          iVar4 = mxmlFindElement(iVar3,iVar2,"SrcIPv4AddressRangeStart",0,0,1);
          if ((iVar4 != 0) && (pcVar5 = mxmlGetText(iVar4,0), pcVar5 != 0)) {
            iVar4 = inet_aton(pcVar5,&iStack_80);
            if (iVar4 == 0) {
              bVar1 = false;
              memcpy(auStack_48,"ERROR_BAD_SRCIPV4ADDRESSRANGE",30);
              break;
            }
            memcpy(&iStack_7c,&iStack_80,4);
          }
          iVar4 = mxmlFindElement(iVar3,iVar2,"SrcIPv4AddressRangeEnd",0,0,1);
          if ((iVar4 != 0) && (pcVar5 = mxmlGetText(iVar4,0), pcVar5 != 0)) {
            memset(&iStack_7c,0,4);
            iVar4 = inet_aton(pcVar5,&iStack_7c);
            if (iVar4 == 0) {
              bVar1 = false;
              memcpy(auStack_48,"ERROR_BAD_SRCIPV4ADDRESSRANGE",30);
              break;
            }
          }
          iVar4 = mxmlFindElement(iVar3,iVar2,"ProtocolSrcRangeStart",0,0,1);
          if ((iVar4 != 0) && (pcVar5 = mxmlGetText(iVar4,0), pcVar5 != 0)) {
            iVar4 = FUN_0042e5cc(pcVar5);
            if (iVar4 == -1) {
              bVar1 = false;
              memcpy(auStack_48,"ERROR_BAD_SRCPORTRANGE",23);
              break;
            }
            uVar7 = atoi(pcVar5);
            local_78 = uVar7;
            iVar4 = FUN_0042e6b4(uVar7 & -1);
            if (iVar4 == -1) {
              bVar1 = false;
              memcpy(auStack_48,"ERROR_BAD_SRCPORTRANGE",23);
              break;
            }
            local_76 = local_78;
          }
          iVar4 = mxmlFindElement(iVar3,iVar2,"ProtocolSrcRangeEnd",0,0,1);
          if ((iVar4 != 0) && (pcVar5 = mxmlGetText(iVar4,0), pcVar5 != 0)) {
            iVar4 = FUN_0042e5cc(pcVar5);
            if (iVar4 == -1) {
              bVar1 = false;
              memcpy(auStack_48,"ERROR_BAD_SRCPORTRANGE",23);
              break;
            }
            uVar7 = atoi(pcVar5);
            local_76 = uVar7;
            iVar4 = FUN_0042e6b4(uVar7 & -1);
            if (iVar4 == -1) {
              bVar1 = false;
              memcpy(auStack_48,"ERROR_BAD_SRCPORTRANGE",23);
              break;
            }
          }
          iVar4 = mxmlFindElement(iVar3,iVar2,"DestInterface",0,0,1);
          if (iVar4 != 0) {
            pcVar5 = mxmlGetText(iVar4,0);
            if (pcVar5 == 0) {
              local_b8 = 1;
            }
            else {
              iVar4 = strcmp(pcVar5,&PTR_0x004e414c);
              if (iVar4 == 0) {
                local_b8 = 1;
              }
              else {
                iVar4 = strcmp(pcVar5,&PTR_0x004e4157);
                if (iVar4 != 0) {
                  bVar1 = false;
                  memcpy(auStack_48,"ERROR_BAD_DESTINTERFACE",24);
                  break;
                }
                local_b8 = 2;
              }
            }
            local_68._0_2_ = CONCAT11(local_b8,local_68);
          }
          iVar4 = mxmlFindElement(iVar3,iVar2,"DestIPv4AddressRangeStart",0,0,1);
          if ((iVar4 != 0) && (pcVar5 = mxmlGetText(iVar4,0), pcVar5 != 0)) {
            iVar4 = inet_aton(pcVar5,&iStack_73);
            if (iVar4 == 0) {
              bVar1 = false;
              memcpy(auStack_48,"ERROR_BAD_DESTIPV4ADDRESSRANGE",31);
              break;
            }
            memcpy(auStack_6f,&iStack_73,4);
          }
          iVar4 = mxmlFindElement(iVar3,iVar2,"DestIPv4AddressRangeEnd",0,0,1);
          if (((iVar4 != 0) && (pcVar5 = mxmlGetText(iVar4,0), pcVar5 != 0)) &&
             (iVar4 = inet_aton(pcVar5,auStack_6f), iVar4 == 0)) {
            bVar1 = false;
            memcpy(auStack_48,"ERROR_BAD_DESTIPV4ADDRESSRANGE",31);
            break;
          }
          iVar4 = mxmlFindElement(iVar3,iVar2,"ProtocolRangeStart",0,0,1);
          if ((iVar4 != 0) && (pcVar5 = mxmlGetText(iVar4,0), pcVar5 != 0)) {
            iVar4 = FUN_0042e5cc(pcVar5);
            if (iVar4 == -1) {
              bVar1 = false;
              memcpy(auStack_48,"ERROR_BAD_PORTRANGE",20);
              break;
            }
            uVar7 = atoi(pcVar5);
            auStack_6f._3_4_ = auStack_6f._3_4_ & 0xff0000ff | (uVar7 & -1) << 8;
            iVar4 = FUN_0042e6b4((uint)(auStack_6f._3_4_ << 8) >> 16);
            if (iVar4 == -1) {
              bVar1 = false;
              memcpy(auStack_48,"ERROR_BAD_PORTRANGE",20);
              break;
            }
            uVar7 = auStack_6f._3_4_ << 8;
            auStack_6f._3_4_ = auStack_6f._3_4_ & 0xffffff | (uVar7 >> 16) << 24;
            local_68 = local_68 & 0xffffff00 | uVar7 >> 24;
          }
          iVar4 = mxmlFindElement(iVar3,iVar2,"ProtocolRangeEnd",0,0,1);
          if ((iVar4 != 0) && (pcVar5 = mxmlGetText(iVar4,0), pcVar5 != 0)) {
            iVar4 = FUN_0042e5cc(pcVar5);
            if (iVar4 == -1) {
              bVar1 = false;
              memcpy(auStack_48,"ERROR_BAD_PORTRANGE",20);
              break;
            }
            uVar7 = atoi(pcVar5);
            auStack_6f._3_4_ = auStack_6f._3_4_ & 0xffffff | uVar7 << 24;
            uVar8 = (uVar7 & -1) >> 8;
            local_68 = local_68 & 0xffffff00 | uVar8;
            iVar4 = FUN_0042e6b4(uVar8 << 8 | uVar7 & 255);
            if (iVar4 == -1) {
              bVar1 = false;
              memcpy(auStack_48,"ERROR_BAD_PORTRANGE",20);
              break;
            }
          }
          iVar4 = mxmlFindElement(iVar3,iVar2,"Protocol",0,0,1);
          if (iVar4 != 0) {
            pcVar5 = mxmlGetText(iVar4,0);
            if (pcVar5 == 0) {
              local_68._0_3_ = CONCAT12(3,local_68);
            }
            else {
              iVar4 = strcmp(pcVar5,&PTR_0x00504354);
              if (iVar4 == 0) {
                local_68._0_3_ = CONCAT12(1,local_68);
              }
              else {
                iVar4 = strcmp(pcVar5,&PTR_0x00504455);
                if (iVar4 == 0) {
                  local_68._0_3_ = CONCAT12(2,local_68);
                }
                else {
                  iVar4 = strcmp(pcVar5,"Any");
                  if (iVar4 != 0) {
                    bVar1 = false;
                    memcpy(auStack_48,"ERROR_BAD_PROTOCOL",19);
                    break;
                  }
                  local_68._0_3_ = CONCAT12(3,local_68);
                }
              }
            }
          }
          apmib_set(0x10077,&iStack_80);
          local_94 = iVar3;
        }
      } while (iVar3 != 0);
    }
  }
LAB_0043bce0:
  mxmlDelete(iVar2);
  if (("" == 0) || (!bVar1)) {
    if (bVar1) {
      memcpy(auStack_48,"O",3);
      apmib_update(4);
      FUN_00421468("firewall.sh",0,0);
    }
    FUN_004260e0("SetIPv4FirewallSettings",auStack_48);
  }
  return 0;
}

