
/* WARNING: Type propagation algorithm not settling */

int FUN_004450a4(undefined4 param_1)

{
  char cVar1;
  ushort uVar2;
  uint uVar3;
  int iVar4;
  short sVar7;
  char *pcVar5;
  int iVar6;
  ushort *puVar8;
  char *pcVar9;
  undefined1 *puVar10;
  int iVar11;
  int local_188;
  timeval local_184;
  uint local_17c;
  int local_178;
  int local_174;
  int local_170;
  int local_16c;
  int local_168;
  int local_164;
  undefined auStack_160 [44];
  uint local_134 [2];
  char local_12c [32];
  char local_10c [36];
  undefined4 local_e8;
  int local_e4;
  int local_e0;
  int local_dc;
  int local_d8 [4];
  int local_c8;
  int local_c4;
  int local_c0;
  undefined4 local_bc;
  int *local_b8;
  undefined4 local_b4;
  int *local_b0;
  undefined4 local_ac;
  int *local_a8;
  undefined4 local_a4;
  int *local_a0;
  undefined4 local_9c;
  int *local_98;
  undefined4 local_94;
  int *local_90;
  undefined4 local_8c;
  uint *local_88;
  undefined4 local_84;
  char *local_80;
  undefined4 local_7c;
  char *local_78;
  undefined4 local_74;
  int *local_70;
  undefined4 local_6c;
  int *local_68;
  undefined4 local_64;
  int *local_60;
  undefined4 local_5c;
  int *local_58;
  undefined4 local_54;
  int *local_50;
  undefined4 local_4c;
  int *local_48;
  undefined4 local_44;
  int *local_40;
  undefined4 local_3c;
  int *local_38;
  undefined4 local_34;
  int *local_30;
  undefined4 local_2c;
  undefined4 *local_28;
  undefined4 local_24;
  undefined4 local_20;
  
  local_b8 = &local_174;
  local_b0 = &local_170;
  local_a8 = &local_178;
  local_a0 = &local_16c;
  local_98 = &local_168;
  local_90 = &local_164;
  local_80 = local_12c;
  local_78 = local_10c;
  local_70 = &local_e4;
  local_68 = &local_e0;
  local_60 = &local_dc;
  local_58 = local_d8;
  local_50 = local_d8 + 2;
  local_48 = local_d8 + 3;
  local_40 = &local_c8;
  local_38 = &local_c4;
  local_30 = &local_c0;
  local_88 = &local_17c;
  local_28 = &local_bc;
  local_74 = " ";
  local_7c = " ";
  local_20 = 0;
  local_b4 = 0;
  local_ac = 0;
  local_a4 = 0;
  local_9c = 0;
  local_94 = 0;
  local_8c = 0;
  local_84 = 0;
  local_6c = 0;
  local_64 = 0;
  local_5c = 0;
  local_54 = 0;
  local_4c = 0;
  local_44 = 0;
  local_3c = 0;
  local_34 = 0;
  local_2c = 0;
  local_24 = 0;
  iVar4 = HttpAccessPermit();
  if (iVar4 == 0) {
    sVar7 = HttpDenyPage(param_1);
    goto LAB_00445dbc;
  }
  local_164 = 0;
  local_17c = 0;
  local_178 = 0;
  local_174 = 0;
  local_170 = 0;
  local_16c = 0;
  local_168 = 0;
  memset(auStack_160,0,",");
  memset(local_134,0,"|");
  iVar4 = httpGetEnv(param_1,"Save");
  if ((iVar4 == 0) &&
     ((iVar4 = httpGetEnv(param_1,"GetGmtTime"), iVar4 == 0 || ("" != 0)))) {
LAB_00445c0c:
    "" = 0;
    local_17c = 0;
    local_178 = 0;
    local_174 = 0;
    local_170 = 0;
    local_16c = 0;
    local_168 = 0;
    local_164 = 0;
    memset(auStack_160,0,",");
    swGetSntpCfg(local_134);
    swGetSntpSystemTime(auStack_160);
    swSntpTime2HttpTime(auStack_160,&local_17c);
    puVar8 = "";
    local_17c = local_134[0];
    do {
      uVar2 = *puVar8;
      puVar8 = puVar8 + 1;
      uVar3 = uVar2;
      if (local_134[0] == uVar2 - 720) break;
      uVar3 = local_17c;
    } while (puVar8 != "");
    local_17c = uVar3;
    httpStatusSet(param_1,0);
    httpHeaderGenerate(param_1);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "timeInf");
    iVar4 = 0;
    do {
      iVar11 = iVar4 + 1;
      pageDynParaPrintf(&local_b8,iVar4,param_1);
      iVar4 = iVar11;
    } while (iVar11 != 19);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar4 = httpRpmFsA(param_1,"/userRpm/DateTimeCfgRpm.htm");
    if (iVar4 == 2) {
      return 2;
    }
    iVar4 = 10;
    puVar10 = 0;
  }
  else {
    swGetSntpCfg(local_134);
    pcVar5 = httpGetEnv(param_1,"timezone");
    if (pcVar5 != 0) {
      iVar4 = atoi(pcVar5);
      local_17c = iVar4 - 720;
    }
    pcVar5 = httpGetEnv(param_1,"year");
    if (pcVar5 != 0) {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 != 0) {
        local_178 = atoi(pcVar9);
      }
    }
    pcVar5 = httpGetEnv(param_1,"month");
    if (pcVar5 != 0) {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 != 0) {
        local_174 = atoi(pcVar9);
      }
    }
    pcVar5 = httpGetEnv(param_1,"day");
    if (pcVar5 != 0) {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 != 0) {
        local_170 = atoi(pcVar9);
      }
    }
    pcVar5 = httpGetEnv(param_1,"hour");
    if (pcVar5 != 0) {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 != 0) {
        local_16c = atoi(pcVar9);
      }
    }
    pcVar5 = httpGetEnv(param_1,"minute");
    if (pcVar5 != 0) {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 != 0) {
        local_168 = atoi(pcVar9);
      }
    }
    pcVar5 = httpGetEnv(param_1,"second");
    if (pcVar5 != 0) {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 != 0) {
        local_164 = atoi(pcVar9);
      }
    }
    pcVar5 = httpGetEnv(param_1,"ntpA");
    if (pcVar5 == 0) {
      strcpy(local_12c,"0.0.0.0");
    }
    else {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      iVar4 = 0;
      do {
        cVar1 = pcVar9[iVar4];
        if ((cVar1 == '\0') || (iVar11 = iVar4 + 1, cVar1 == ' ')) {
          local_12c[iVar4] = '\0';
          break;
        }
        local_12c[iVar4] = cVar1;
        iVar4 = iVar11;
      } while (iVar11 != 31);
    }
    pcVar5 = httpGetEnv(param_1,"ntpB");
    if (pcVar5 == 0) {
      strcpy(local_10c,"0.0.0.0");
    }
    else {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      iVar4 = 0;
      do {
        cVar1 = pcVar9[iVar4];
        if ((cVar1 == '\0') || (iVar11 = iVar4 + 1, cVar1 == ' ')) {
          local_10c[iVar4] = '\0';
          break;
        }
        local_10c[iVar4] = cVar1;
        iVar4 = iVar11;
      } while (iVar11 != 31);
    }
    pcVar5 = httpGetEnv(param_1,"isTimeChanged");
    if (pcVar5 == 0) {
      iVar4 = 0;
    }
    else {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 == 0) {
        iVar4 = 0;
      }
      else {
        iVar4 = atoi(pcVar9);
      }
    }
    pcVar5 = httpGetEnv(param_1,"isDaylightSavingChanged");
    if (pcVar5 == 0) {
      iVar11 = 0;
    }
    else {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 == 0) {
        iVar11 = 0;
      }
      else {
        iVar11 = atoi(pcVar9);
      }
    }
    pcVar5 = httpGetEnv(param_1,"DaylightSaving");
    if (pcVar5 == 0) {
      local_c0 = 0;
    }
    else {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 == 0) {
        local_c0 = 0;
      }
      else {
        iVar6 = strcmp(pcVar9,"on");
        local_c0 = 1;
        if (iVar6 != 0) {
          local_c0 = atoi(pcVar9);
        }
      }
    }
    if (local_c0 == 1) {
      pcVar5 = httpGetEnv(param_1,"start_month");
      if (pcVar5 != 0) {
        local_e4 = atoi(pcVar5);
      }
      pcVar5 = httpGetEnv(param_1,"start_count");
      if (pcVar5 != 0) {
        local_e0 = atoi(pcVar5);
      }
      pcVar5 = httpGetEnv(param_1,"start_week");
      if (pcVar5 != 0) {
        local_dc = atoi(pcVar5);
      }
      pcVar5 = httpGetEnv(param_1,"start_hour");
      if (pcVar5 != 0) {
        local_d8[0] = atoi(pcVar5);
      }
      pcVar5 = httpGetEnv(param_1,"end_month");
      if (pcVar5 != 0) {
        local_d8[2] = atoi(pcVar5);
      }
      pcVar5 = httpGetEnv(param_1,"end_count");
      if (pcVar5 != 0) {
        local_d8[3] = atoi(pcVar5);
      }
      pcVar5 = httpGetEnv(param_1,"end_week");
      if (pcVar5 != 0) {
        local_c8 = atoi(pcVar5);
      }
      pcVar5 = httpGetEnv(param_1,"end_hour");
      if (pcVar5 != 0) {
        local_c4 = atoi(pcVar5);
      }
    }
    iVar6 = local_c0;
    if (((iVar11 == 0) || (local_c0 != 1)) ||
       (sVar7 = swCheckDaylightSaving(&local_e8,local_d8 + 1), sVar7 == 0)) {
      local_134[0] = local_17c;
      iVar6 = httpGetEnv(param_1,"GetGmtTime");
      if (iVar6 != 0) {
        local_134[1] = 0;
        sVar7 = swSetSntpCfg(local_134);
        iVar4 = sVar7;
        if (iVar4 == 0) {
          iVar4 = FUN_00444dac(param_1);
          return iVar4;
        }
        goto LAB_00445c04;
      }
      if (iVar4 == 0) {
        iVar4 = getProductId();
        if (iVar4 == 0x9411001) {
          gettimeofday(&local_184,0);
          local_188 = 0;
          ucGetTimeZone(&local_188);
          local_184.tv_sec = local_184.tv_sec + (local_17c - local_188) * "<";
          settimeofday(&local_184,0);
        }
        sVar7 = swSetSntpCfg(local_134);
        iVar4 = sVar7;
        if (iVar4 != 0) goto LAB_00445c04;
        if (iVar11 == 0) goto LAB_00445c0c;
        sVar7 = daylightSavingAction();
      }
      else {
        swHttpTime2SntpTime(&local_17c,auStack_160);
        sVar7 = swSetSntpTime(auStack_160);
        iVar4 = sVar7;
        if (iVar4 != 0) goto LAB_00445c04;
        local_134[1] = 1;
        if (local_c0 == 0) {
LAB_00445ae8:
          local_bc = 0;
        }
        else {
          iVar4 = swIsDaylightSavingPeriod(&local_e8,local_d8 + 1,auStack_160);
          if (iVar4 != 1) {
            if (iVar4 != 0) goto LAB_00445afc;
            goto LAB_00445ae8;
          }
          local_bc = 1;
        }
        sVar7 = swSetSntpCfg(local_134);
      }
      iVar4 = sVar7;
      if (iVar4 != 0) goto LAB_00445c04;
      goto LAB_00445c0c;
    }
    local_d8[2] = 10;
    local_c4 = 3;
    local_d8[3] = iVar6;
    local_e4 = 2;
    local_e0 = 2;
    local_dc = 0;
    local_d8[0] = 2;
    local_e8 = 0;
    local_c8 = 0;
    local_d8[1] = 0;
    local_bc = 0;
    local_c0 = 0;
    if (local_134[0] == 180) {
      local_c0 = 2;
    }
    sVar7 = swSetSntpCfg(local_134);
    iVar4 = sVar7;
    if (iVar4 == 0) {
LAB_00445afc:
      puVar10 = "";
      iVar4 = 0x4656;
    }
    else {
LAB_00445c04:
      puVar10 = "";
    }
  }
  sVar7 = HttpErrorPage(param_1,iVar4,puVar10,0);
LAB_00445dbc:
  return sVar7;
}

