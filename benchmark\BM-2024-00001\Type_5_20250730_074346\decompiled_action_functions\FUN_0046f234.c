
int FUN_0046f234(undefined4 param_1)

{
  int iVar1;
  undefined4 uVar2;
  int iVar3;
  short sVar4;
  int iStack_1c0;
  int iStack_1bc;
  int iStack_1b8;
  int iStack_1b4;
  undefined4 uStack_1b0;
  undefined4 uStack_1ac;
  int *piStack_1a8;
  undefined4 uStack_1a4;
  int *piStack_1a0;
  undefined4 uStack_19c;
  undefined4 uStack_198;
  undefined auStack_190 [4];
  uint uStack_18c;
  
  iStack_1b4 = 0;
  uStack_1b0 = 0;
  uStack_1ac = 0;
  iStack_1b8 = 0;
  iStack_1c0 = 0;
  iStack_1bc = 0;
  swWlanBasicCfgGet(0,auStack_190);
  HTTP_DEBUG_PRINT("wireless/httpWlanThroughputIframe.c:58","\n######wlan working mode: %d\n",
                   uStack_18c);
  iVar1 = swWlanGetMbssidNum(0);
  iVar3 = 0;
  while (iVar3 < iVar1) {
    uVar2 = swWlanGetRealIdxByVidx(0,iVar3);
    swGetWlanTraffic(&iStack_1b8,uVar2);
    iStack_1bc = iStack_1bc + iStack_1b8;
    iStack_1c0 = iStack_1c0 + iStack_1b4;
    iVar3 = iVar3 + 1;
  }
  if ((1 < uStack_18c) && (iVar3 = getStaConnectStatus(), iVar3 != 0)) {
    swGetWlanTraffic(&iStack_1b8,7);
    iStack_1bc = iStack_1bc + iStack_1b8;
    iStack_1c0 = iStack_1c0 + iStack_1b4;
  }
  HTTP_DEBUG_PRINT("wireless/httpWlanThroughputIframe.c:78",
                   "httpWlanTraffic.rxbytes:%d, httpWlanTraffic.txbytes:%d",iStack_1bc,iStack_1c0);
  piStack_1a8 = &iStack_1c0;
  piStack_1a0 = &iStack_1bc;
  uStack_198 = 0;
  uStack_1a4 = 0;
  uStack_19c = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar3 = HttpAccessPermit(param_1);
  if (iVar3 == 0) {
    sVar4 = HttpDenyPage(param_1);
  }
  else {
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "WlanThroughputPara");
    pageDynParaListPrintf(&piStack_1a8,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    iVar3 = httpRpmFsA(param_1,"/userRpm/WlanThroughputIframe.htm");
    if (iVar3 == 2) {
      return 2;
    }
    sVar4 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar4;
}

