
int FUN_0044efc0(undefined4 param_1)

{
  int iVar1;
  short sVar3;
  char *pcVar2;
  undefined4 uVar4;
  undefined1 *puVar5;
  int iVar6;
  undefined4 local_220;
  undefined *local_21c;
  undefined4 local_218;
  undefined *local_214;
  undefined4 local_210;
  undefined *local_20c;
  undefined4 local_208;
  undefined *local_204;
  undefined4 local_200;
  undefined *local_1fc;
  undefined4 local_1f8;
  undefined *local_1f4;
  undefined4 local_1f0;
  undefined *local_1ec;
  undefined4 local_1e8;
  undefined *local_1e4;
  undefined4 local_1e0;
  undefined4 local_1dc;
  undefined4 local_1d8;
  undefined auStack_1d4 [4];
  undefined auStack_1d0 [48];
  undefined auStack_1a0 [4];
  undefined auStack_19c [48];
  undefined auStack_16c [4];
  undefined auStack_168 [45];
  undefined auStack_13b [47];
  undefined auStack_10c [4];
  char acStack_108 [90];
  char acStack_ae [46];
  int local_80;
  int local_7c [2];
  char acStack_74 [45];
  char acStack_47 [47];
  
  local_214 = auStack_1d0;
  local_20c = auStack_1a0;
  local_204 = auStack_19c;
  local_1fc = auStack_16c;
  local_1f4 = auStack_168;
  local_1ec = auStack_13b;
  local_21c = auStack_1d4;
  local_1e4 = auStack_10c;
  local_1e8 = "-";
  local_210 = "-";
  local_200 = "-";
  local_1f0 = "-";
  local_220 = 0;
  local_1dc = 0;
  local_218 = 0;
  local_208 = 0;
  local_1f8 = 0;
  local_1e0 = 0;
  local_1d8 = 0;
  memset(local_21c,0,204);
  memset(acStack_108,0,240);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar3 = HttpDenyPage(param_1);
    goto LAB_0044f5c4;
  }
  swGetStaticIpv6Cfg(acStack_108);
  iVar1 = httpGetEnv(param_1,"Save");
  if (iVar1 == 0) {
LAB_0044f3dc:
    LanIpv6RpmHtm(param_1,3);
    local_220 = 3;
    pageParaSet(&local_21c,&local_220,0);
    pageParaSet(&local_21c,acStack_108,1);
    pageParaSet(&local_21c,&local_80,2);
    pageParaSet(&local_21c,acStack_ae,3);
    pageParaSet(&local_21c,local_7c,4);
    pageParaSet(&local_21c,acStack_74,5);
    pageParaSet(&local_21c,acStack_47,6);
    local_220 = swGetIPv6Enable();
    pageParaSet(&local_21c,&local_220,7);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "staticIpv6Inf");
    iVar1 = 0;
    do {
      iVar6 = iVar1 + 1;
      pageDynParaPrintf(&local_21c,iVar1,param_1);
      iVar1 = iVar6;
    } while (iVar6 != 8);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintfWanIpv6TypeInfo(param_1);
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/WanStaticIpV6CfgRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    uVar4 = 10;
    puVar5 = 0;
  }
  else {
    iVar1 = httpGetEnv(param_1,"ipv6Enable");
    if (iVar1 == 0) {
      IPV6_ECHO("ucSetIPv6Enable(FALSE);");
    }
    else {
      IPV6_ECHO("ucSetIPv6Enable(TRUE);");
    }
    ucSetIPv6Enable(iVar1 != 0);
    pcVar2 = httpGetEnv(param_1,"i");
    if (pcVar2 != 0) {
      strcpy(acStack_108,pcVar2);
    }
    pcVar2 = httpGetEnv(param_1,"prefix");
    if (pcVar2 != 0) {
      local_80 = atoi(pcVar2);
    }
    pcVar2 = httpGetEnv(param_1,"gateway");
    if (pcVar2 == 0) {
      memset(acStack_ae,0,"-");
    }
    else {
      strcpy(acStack_ae,pcVar2);
      printf("%s %d wanStaticv6Cfg.sGateway = %s \r\n","StaticIpv6CfgRpmHtm","r",acStack_ae);
    }
    pcVar2 = httpGetEnv(param_1,"mtu");
    if (pcVar2 != 0) {
      local_7c[0] = atoi(pcVar2);
    }
    pcVar2 = httpGetEnv(param_1,"dnsserver1");
    if (pcVar2 == 0) {
      memset(acStack_74,0,"-");
    }
    else {
      strcpy(acStack_74,pcVar2);
    }
    pcVar2 = httpGetEnv(param_1,"dnsserver2");
    if (pcVar2 == 0) {
      memset(acStack_47,0,"-");
    }
    else {
      strcpy(acStack_47,pcVar2);
    }
    iVar1 = swSetStaticIpv6Cfg(acStack_108);
    if (iVar1 != -2) {
      if (iVar1 == -3) {
        swGetStaticIpv6Cfg(acStack_108);
      }
      else {
        uVar4 = 51000;
        if (iVar1 == -1) {
          puVar5 = "";
          goto LAB_0044f5bc;
        }
      }
      goto LAB_0044f3dc;
    }
    puVar5 = "";
    uVar4 = 0xc739;
  }
LAB_0044f5bc:
  sVar3 = HttpErrorPage(param_1,uVar4,puVar5,0);
LAB_0044f5c4:
  return sVar3;
}

