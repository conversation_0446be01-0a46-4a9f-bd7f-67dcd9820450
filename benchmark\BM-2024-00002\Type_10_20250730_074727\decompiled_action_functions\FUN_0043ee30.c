
undefined4 FUN_0043ee30(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  void *__ptr;
  undefined4 local_a34;
  undefined4 local_a30;
  undefined4 local_a2c;
  undefined4 local_a28;
  undefined4 local_a24;
  undefined4 local_a20;
  undefined4 local_a1c;
  undefined4 local_a18;
  undefined4 local_a14;
  undefined2 local_a10;
  undefined local_a0e;
  undefined4 local_a0c;
  undefined auStack_a08 [256];
  undefined auStack_908 [256];
  undefined auStack_808 [1024];
  undefined auStack_408 [1024];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetFirmwareStatus");
  }
  else {
    local_a34 = 0;
    local_a30 = 0;
    local_a2c = 0;
    local_a28 = 0;
    local_a24 = 0;
    local_a20 = 0;
    local_a1c = 0;
    local_a18 = 0;
    local_a14 = 0;
    local_a10 = 0;
    local_a0e = 0;
    local_a0c = 0;
    memset(auStack_a08,0,256);
    memset(auStack_908,0,256);
    memset(auStack_808,0,0x400);
    FUN_0042e7f0(&local_a34,auStack_808,&local_a0c,auStack_a08,auStack_908);
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar2 = mxmlNewElement(iVar2,"soap:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"GetFirmwareStatusResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("GetFirmwareStatusResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar3 = mxmlNewElement(iVar2,"GetFirmwareStatusResult");
            if (iVar3 == 0) {
              mxmlDelete(iVar1);
              puts("GetFirmwareStatusResult=NULL");
            }
            else {
              mxmlNewText(iVar3,0,"O");
              iVar3 = mxmlNewElement(iVar2,"CurrentFWVersion");
              if (iVar3 == 0) {
                mxmlDelete(iVar1);
                puts("CurrentFWVersion=NULL");
              }
              else {
                mxmlNewText(iVar3,0,"1.0.2");
                iVar3 = mxmlNewElement(iVar2,"LatestFWVersion");
                if (iVar3 == 0) {
                  mxmlDelete(iVar1);
                  puts("LatestFWVersion=NULL");
                }
                else {
                  mxmlNewText(iVar3,0,&local_a34);
                  iVar3 = mxmlNewElement(iVar2,"LatestFWVersionDate");
                  if (iVar3 == 0) {
                    mxmlDelete(iVar1);
                    puts("LatestFWVersionDate=NULL");
                  }
                  else {
                    mxmlNewText(iVar3,0,&local_a24);
                    iVar3 = mxmlNewElement(iVar2,"ReleaseNote");
                    if (iVar3 == 0) {
                      mxmlDelete(iVar1);
                      puts("ReleaseNote=NULL");
                    }
                    else {
                      memset(auStack_408,0,0x400);
                      FUN_0043ed54("UTF-8","GB2312",auStack_808,0x400,auStack_408,0x400);
                      mxmlNewText(iVar3,0,auStack_408);
                      iVar3 = mxmlNewElement(iVar2,"RequireLevel");
                      if (iVar3 == 0) {
                        mxmlDelete(iVar1);
                        puts("RequireLevel=NULL");
                      }
                      else {
                        mxmlNewText(iVar3,0,&local_a0c);
                        iVar3 = mxmlNewElement(iVar2,"FWDownloadUrl");
                        if (iVar3 == 0) {
                          mxmlDelete(iVar1);
                          puts("FWDownloadUrl=NULL");
                        }
                        else {
                          mxmlNewText(iVar3,0,auStack_a08);
                          iVar2 = mxmlNewElement(iVar2,"FWUploadUrl");
                          if (iVar2 == 0) {
                            mxmlDelete(iVar1);
                            puts("FWUploadUrl=NULL");
                          }
                          else {
                            mxmlNewText(iVar2,0,auStack_908);
                            if ("" == 0) {
                              __ptr = mxmlSaveAllocString(iVar1,0);
                              if (__ptr != 0) {
                                FUN_0041ed70("",200,__ptr,"");
                                free(__ptr);
                              }
                            }
                            mxmlDelete(iVar1);
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

