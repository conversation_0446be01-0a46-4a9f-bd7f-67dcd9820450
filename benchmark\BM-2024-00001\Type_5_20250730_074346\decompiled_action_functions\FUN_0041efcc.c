
int FUN_0041efcc(undefined4 param_1)

{
  int iVar1;
  short sVar7;
  char *pcVar2;
  uint32_t uVar3;
  uint32_t uVar4;
  uint32_t uVar5;
  uint32_t uVar6;
  char *pcVar8;
  undefined4 local_a8;
  undefined4 local_a4;
  uint32_t local_a0;
  undefined4 local_9c;
  in_addr_t local_98;
  char acStack_94 [16];
  char acStack_84 [16];
  undefined auStack_74 [4];
  undefined auStack_70 [16];
  undefined auStack_60 [16];
  undefined *local_50;
  undefined4 local_4c;
  undefined *local_48;
  undefined4 local_44;
  undefined *local_40;
  undefined4 local_3c;
  char *local_38;
  undefined4 local_34;
  undefined4 local_30;
  
  local_50 = auStack_74;
  local_48 = auStack_70;
  local_40 = auStack_60;
  local_34 = 16;
  local_44 = 16;
  local_3c = 16;
  local_38 = 0;
  local_30 = 0;
  local_4c = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar7 = HttpDenyPage(param_1);
    goto LAB_0041f4d4;
  }
  iVar1 = httpGetEnv(param_1,"Save");
  if (iVar1 == 0) {
LAB_0041f218:
    local_9c = 0;
    local_98 = 0;
    swGetForwardDmzCfg(&local_9c);
    uVar3 = ntohl(local_98);
    uVar4 = ntohl(local_98);
    uVar5 = ntohl(local_98);
    uVar6 = ntohl(local_98);
    sprintf(acStack_94,"%d.%d.%d.%d",uVar3 >> 24,(uVar4 & 0xff0000) >> 16,
            (int)(uVar5 & -256) >> 8,uVar6 & 255);
    local_a8 = local_9c;
    pageParaSet(&local_50,&local_a8,0);
    pageParaSet(&local_50,acStack_94,1);
    swGetLanIpMask(&local_a4,&local_a0);
    uVar3 = ntohl(local_a0);
    uVar4 = ntohl(local_a0);
    uVar5 = ntohl(local_a0);
    uVar6 = ntohl(local_a0);
    sprintf(acStack_84,"%d.%d.%d.%d",uVar3 >> 24,(uVar4 & 0xff0000) >> 16,
            (int)(uVar5 & -256) >> 8,uVar6 & 255);
    local_38 = acStack_84;
    swIpAddr2Str(local_a4,auStack_60);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "DMZInf");
    pageDynParaPrintf(&local_50,0,param_1);
    pageDynParaPrintf(&local_50,1,param_1);
    pageDynParaPrintf(&local_50,2,param_1);
    pageDynParaPrintf(&local_50,3,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/DMZRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    iVar1 = 10;
  }
  else {
    local_9c = 0;
    local_98 = 0;
    pcVar2 = httpGetEnv(param_1,"enable");
    if (pcVar2 == 0) {
      local_9c = 0;
    }
    else {
      do {
        pcVar8 = pcVar2;
        pcVar2 = pcVar8 + 1;
      } while (*pcVar8 == ' ');
      if ((pcVar8 == 0) || (iVar1 = atoi(pcVar8), iVar1 != 1)) {
        local_9c = 0;
      }
      else {
        local_9c = 1;
      }
    }
    pcVar2 = httpGetEnv(param_1,"ipAddr");
    if (pcVar2 == 0) {
      local_98 = 0;
LAB_0041f1d0:
      sVar7 = swChkForwardDmzCfg(&local_9c);
      iVar1 = sVar7;
      if (iVar1 == 0) {
        swSetForwardDmzCfg(&local_9c);
        goto LAB_0041f218;
      }
    }
    else {
      do {
        pcVar8 = pcVar2;
        pcVar2 = pcVar8 + 1;
      } while (*pcVar8 == ' ');
      if ((pcVar8 == 0) || (*pcVar8 == '\0')) {
        local_98 = 0;
        goto LAB_0041f1d0;
      }
      iVar1 = swChkDotIpAddr(pcVar8);
      if (iVar1 != 0) {
        local_98 = inet_addr(pcVar8);
        goto LAB_0041f1d0;
      }
      iVar1 = 13000;
    }
  }
  sVar7 = HttpErrorPage(param_1,iVar1,0,0);
LAB_0041f4d4:
  return sVar7;
}

