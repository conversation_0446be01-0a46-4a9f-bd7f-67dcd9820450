
/* WARNING: Type propagation algorithm not settling */

void FUN_00455e3c(undefined4 param_1)

{
  int iVar1;
  undefined4 uVar2;
  int iVar3;
  char *pcVar4;
  undefined4 uVar5;
  void *__ptr;
  int local_19c;
  int local_184;
  int local_180;
  int local_17c;
  undefined4 local_e4;
  undefined4 local_e0;
  undefined4 local_dc;
  undefined4 local_d8;
  undefined4 local_d4;
  undefined4 local_d0;
  undefined4 local_cc;
  undefined4 local_c8;
  undefined4 local_c4;
  undefined4 local_c0;
  undefined4 local_bc;
  undefined4 local_b8;
  undefined4 local_b4;
  undefined4 local_b0;
  undefined4 local_ac;
  undefined4 local_a8;
  undefined4 local_a4;
  undefined4 local_a0;
  undefined4 local_9c;
  undefined4 local_98;
  char local_94 [32];
  int local_74 [2];
  undefined auStack_6c [4];
  undefined auStack_68 [6];
  char acStack_62 [38];
  undefined local_3c [4];
  undefined auStack_38 [44];
  in_addr iStack_c;
  
  local_e4 = 0;
  local_e0 = 0;
  local_dc = 0;
  local_d8 = 0;
  local_d4 = 0;
  local_d0 = 0;
  local_cc = 0;
  local_c8 = 0;
  local_c4 = 0;
  local_c0 = 0;
  local_bc = 0;
  local_b8 = 0;
  local_b4 = 0;
  local_b0 = 0;
  local_ac = 0;
  local_a8 = 0;
  local_a4 = 0;
  local_a0 = 0;
  local_9c = 0;
  local_98 = 0;
  local_94[0] = '\0';
  local_94[1] = '\0';
  local_94[2] = '\0';
  local_94[3] = '\0';
  local_94[4] = '\0';
  local_94[5] = '\0';
  local_94[6] = '\0';
  local_94[7] = '\0';
  local_94[8] = '\0';
  local_94[9] = '\0';
  local_94[10] = '\0';
  local_94[11] = '\0';
  local_94[12] = '\0';
  local_94[13] = '\0';
  local_94[14] = '\0';
  local_94[15] = '\0';
  local_94[16] = '\0';
  local_94[17] = '\0';
  local_94[18] = '\0';
  local_94[19] = '\0';
  local_94[20] = '\0';
  local_94[21] = '\0';
  local_94[22] = '\0';
  local_94[23] = '\0';
  local_94[24] = '\0';
  local_94[25] = '\0';
  local_94[26] = '\0';
  local_94[27] = '\0';
  local_94[28] = '\0';
  local_94[29] = '\0';
  local_94[30] = '\0';
  local_94[31] = '\0';
  iVar1 = mxmlLoadString(0,param_1,0);
  if (iVar1 != 0) {
    uVar2 = mxmlFindElement(iVar1,iVar1,"soap:Envelope",0,0,1);
    uVar2 = mxmlFindElement(uVar2,iVar1,"SetStaticClientInfo",0,0,1);
    uVar2 = mxmlFindElement(uVar2,iVar1,"StaticClientInfoLists",0,0,1);
    local_74[1] = 1;
    iVar3 = apmib_set(292,local_74 + 1);
    if (iVar3 != 0) {
      iVar3 = apmib_get(293,local_74);
      if (iVar3 == 0) {
        printf("Get entry number error!");
      }
      else {
        local_180 = 0;
        for (local_19c = mxmlFindElement(uVar2,iVar1,"ClientInfo",0,0,1); local_19c != 0;
            local_19c = mxmlFindElement(local_19c,iVar1,"ClientInfo",0,0,1)) {
          local_180 = local_180 + 1;
        }
        if (local_74[0] < local_180) {
          memcpy(local_94,"add",3);
        }
        else {
          memcpy(local_94,"del",3);
        }
        iVar3 = strncmp(local_94,"add",3);
        if (iVar3 == 0) {
          for (local_19c = mxmlFindElement(uVar2,iVar1,"ClientInfo",0,0,1); local_19c != 0;
              local_19c = mxmlFindElement(local_19c,iVar1,"ClientInfo",0,0,1)) {
            memset(auStack_6c,0,".");
            memset(&local_e4,0," ");
            uVar2 = mxmlFindElement(local_19c,iVar1,"MacAddress",0,0,1);
            iVar3 = mxmlGetText(uVar2,0);
            if (iVar3 != 0) {
              FUN_00452ec0(iVar3,&local_e4,17);
              FUN_00452f88(&local_e4,auStack_68,12);
            }
            uVar2 = mxmlFindElement(local_19c,iVar1,"DeviceName",0,0,1);
            pcVar4 = mxmlGetText(uVar2,0);
            if (pcVar4 != 0) {
              strncpy(acStack_62,pcVar4," ");
            }
            uVar2 = mxmlFindElement(local_19c,iVar1,"IPv4Address",0,0,1);
            pcVar4 = mxmlGetText(uVar2,0);
            if (pcVar4 != 0) {
              inet_aton(pcVar4,&iStack_c);
              memcpy(auStack_6c,&iStack_c,4);
            }
            for (local_17c = 1; local_17c <= local_74[0]; local_17c = local_17c + 1) {
              memset(local_3c,0,".");
              local_3c[0] = local_17c;
              iVar3 = apmib_get(0x8126,local_3c);
              if (iVar3 == 0) {
                printf("Get table entry error!");
                goto LAB_00456768;
              }
              iVar3 = memcmp(auStack_38,auStack_68,6);
              if (iVar3 == 0) break;
            }
            if ((local_74[0] < local_17c) && (iVar3 = apmib_set(0x10127,auStack_6c), iVar3 == 0)) {
              printf("Delete table entry error!");
              break;
            }
          }
        }
        else {
          for (local_17c = 1; local_17c <= local_74[0]; local_17c = local_17c + 1) {
            memset(local_3c,0,".");
            local_3c[0] = local_17c;
            iVar3 = apmib_get(0x8126,local_3c);
            if (iVar3 == 0) {
              printf("Get table entry error!");
              break;
            }
            local_184 = 0;
            local_19c = mxmlFindElement(uVar2,iVar1,"ClientInfo",0,0,1);
            while (local_19c != 0) {
              memset(auStack_6c,0,".");
              uVar5 = mxmlFindElement(local_19c,iVar1,"MacAddress",0,0,1);
              iVar3 = mxmlGetText(uVar5,0);
              if (iVar3 != 0) {
                FUN_00452ec0(iVar3,&local_e4,17);
                FUN_00452f88(&local_e4,auStack_68,12);
              }
              uVar5 = mxmlFindElement(local_19c,iVar1,"DeviceName",0,0,1);
              pcVar4 = mxmlGetText(uVar5,0);
              if (pcVar4 != 0) {
                strncpy(acStack_62,pcVar4," ");
              }
              uVar5 = mxmlFindElement(local_19c,iVar1,"IPv4Address",0,0,1);
              pcVar4 = mxmlGetText(uVar5,0);
              if (pcVar4 != 0) {
                inet_aton(pcVar4,&iStack_c);
                memcpy(auStack_6c,&iStack_c,4);
              }
              iVar3 = memcmp(auStack_38,auStack_68,6);
              if (iVar3 == 0) break;
              local_19c = mxmlFindElement(local_19c,iVar1,"ClientInfo",0,0,1);
              local_184 = local_184 + 1;
            }
            if ((local_180 <= local_184) && (iVar3 = apmib_set(0x20128,local_3c), iVar3 == 0)) {
              printf("Delete table entry error!");
              break;
            }
          }
        }
      }
    }
  }
LAB_00456768:
  apmib_update(4);
  iVar1 = mxmlNewXML("1.0");
  if (iVar1 == 0) {
    printf("xml is NULL!");
  }
  else {
    iVar3 = mxmlNewElement(iVar1,"soap:Envelope");
    if (iVar3 == 0) {
      printf("xml is NULL!");
    }
    else {
      mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
      mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
      mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
      iVar3 = mxmlNewElement(iVar3,"soap:Body");
      if (iVar3 == 0) {
        printf("body is NULL!");
      }
      else {
        iVar3 = mxmlNewElement(iVar3,"SetStaticClientInfoResponse");
        if (iVar3 == 0) {
          printf("SetStaticClientInfoResponse is NULL!");
        }
        else {
          mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
          iVar3 = mxmlNewElement(iVar3,"SetStaticClientInfoResult");
          if (iVar3 == 0) {
            printf("SetStaticClientInfoResponse is NULL!");
          }
          else {
            mxmlNewText(iVar3,0,"O");
            __ptr = mxmlSaveAllocString(iVar1,0);
            FUN_0041ed70("",200,__ptr,"");
            free(__ptr);
            mxmlDelete(iVar1);
            system("init.sh gw all");
          }
        }
      }
    }
  }
  return;
}

