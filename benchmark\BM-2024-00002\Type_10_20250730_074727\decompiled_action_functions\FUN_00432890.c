
undefined4 FUN_00432890(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  int iVar3;
  char *__s1;
  undefined4 local_11c;
  undefined4 local_118;
  undefined4 local_114;
  undefined4 local_110;
  char local_10c [260];
  
  bVar1 = false;
  local_11c = 0;
  local_118 = 0;
  local_114 = 0;
  local_110 = 0;
  memset(local_10c,0,256);
  iVar2 = mxmlLoadString(0,param_1,0);
  if (iVar2 != 0) {
    iVar3 = mxmlFindElement(iVar2,iVar2,"soap:Envelope",0,0,1);
    if ((((iVar3 != 0) && (iVar3 = mxmlFindElement(iVar3,iVar2,"SetWanSpeedTest",0,0,1), iVar3 != 0)
         ) && (iVar3 = mxmlFindElement(iVar3,iVar2,"WanSpeedTest",0,0,1), iVar3 != 0)) &&
       ((__s1 = mxmlGetText(iVar3,0), __s1 != 0 &&
        (iVar3 = strcmp(__s1,"true"), iVar3 == 0)))) {
      bVar1 = true;
    }
    mxmlDelete(iVar2);
    if ("" == 0) {
      if (bVar1) {
        memcpy(&local_11c,"O",3);
        FUN_00432718(local_10c,255);
        if (local_10c[0] != '\0') {
          apmib_set(0x1b5f,local_10c);
          apmib_update(4);
        }
        FUN_00421468("speedtest&");
      }
      else {
        memcpy(&local_11c,"ERROR",6);
      }
      FUN_004260e0("SetWanSpeedTest",&local_11c);
    }
  }
  return 0;
}

