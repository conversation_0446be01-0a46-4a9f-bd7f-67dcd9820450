
undefined4 FUN_0045a27c(void)

{
  int iVar1;
  undefined4 uVar2;
  int iVar3;
  int iVar4;
  void *__ptr;
  int local_b4;
  int local_b0 [34];
  undefined auStack_28 [32];
  
  local_b4 = 0;
  local_b0[0] = 0;
  iVar1 = mxmlNewXML("1.0");
  if (iVar1 == 0) {
    puts("xml is null");
    uVar2 = 0;
  }
  else {
    iVar3 = mxmlNewElement(iVar1,"soap:Envelope");
    if (iVar3 == 0) {
      puts("soap_env is null");
      uVar2 = 0;
    }
    else {
      mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
      mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
      mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
      iVar3 = mxmlNewElement(iVar3,"soap:Body");
      if (iVar3 == 0) {
        puts("soap_env is null");
        uVar2 = 0;
      }
      else {
        iVar3 = mxmlNewElement(iVar3,"GetDDNSSettingsResponse");
        if (iVar3 == 0) {
          puts("soap_env is null");
          uVar2 = 0;
        }
        else {
          mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
          iVar4 = mxmlNewElement(iVar3,"GetDDNSSettingsResult");
          if (iVar4 == 0) {
            puts("soap_env is null");
            uVar2 = 0;
          }
          else {
            mxmlNewText(iVar4,0,"O");
            iVar4 = apmib_get(187,&local_b4);
            if (iVar4 == 0) {
              printf("Set enabled flag error!");
            }
            else {
              uVar2 = mxmlNewElement(iVar3,"Enable");
              if (local_b4 == 1) {
                mxmlNewText(uVar2,0,"true");
              }
              else {
                mxmlNewText(uVar2,0,"false");
              }
              iVar4 = apmib_get(188,local_b0);
              if (iVar4 == 0) {
                printf("Set DDNS Type error!");
              }
              else {
                memset(auStack_28,0,30);
                iVar4 = mxmlNewElement(iVar3,"Provider");
                if (iVar4 == 0) {
                  puts("Provider is null");
                  return 0;
                }
                if (local_b0[0] == 0) {
                  mxmlNewText(iVar4,0,"dyndns.org");
                }
                else if (local_b0[0] == 1) {
                  mxmlNewText(iVar4,0,"no-ip.com");
                }
                else if (local_b0[0] == 2) {
                  mxmlNewText(iVar4,0,"oray.com");
                }
                else {
                  mxmlNewText(iVar4,0,"dyndns.org");
                }
                memset(auStack_28,0,30);
                iVar4 = apmib_get(189,auStack_28);
                if (iVar4 == 0) {
                  printf("Set DDNS Password String error!");
                }
                else {
                  uVar2 = mxmlNewElement(iVar3,"Hostname");
                  mxmlNewText(uVar2,0,auStack_28);
                  memset(auStack_28,0,30);
                  iVar4 = apmib_get(190,auStack_28);
                  if (iVar4 == 0) {
                    printf("Set DDNS User String error!");
                  }
                  else {
                    iVar4 = mxmlNewElement(iVar3,"Username");
                    if (iVar4 == 0) {
                      puts("Username is null");
                      return 0;
                    }
                    mxmlNewText(iVar4,0,auStack_28);
                    memset(auStack_28,0,30);
                    iVar4 = apmib_get(191,auStack_28);
                    if (iVar4 == 0) {
                      printf("Set DDNS Password String error!");
                    }
                    else {
                      iVar3 = mxmlNewElement(iVar3,"Password");
                      if (iVar3 == 0) {
                        puts("Password is null");
                        return 0;
                      }
                      mxmlNewText(iVar3,0,auStack_28);
                      __ptr = mxmlSaveAllocString(iVar1,0);
                      FUN_0041ed70("",200,__ptr,"");
                      free(__ptr);
                      mxmlDelete(iVar1);
                    }
                  }
                }
              }
            }
            uVar2 = apmib_update(4);
          }
        }
      }
    }
  }
  return uVar2;
}

