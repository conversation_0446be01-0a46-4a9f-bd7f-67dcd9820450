
undefined4 FUN_0045bcbc(void)

{
  int iVar1;
  char *pcVar2;
  undefined4 uVar3;
  undefined4 uVar4;
  undefined4 uVar5;
  void *__ptr;
  char local_5c [32];
  undefined4 local_3c;
  undefined4 local_38;
  undefined4 local_34;
  undefined4 local_30;
  undefined4 local_2c;
  undefined4 local_28;
  undefined4 local_24;
  undefined4 local_20;
  in_addr local_1c;
  in_addr local_18 [4];
  
  local_5c[0] = '\0';
  local_5c[1] = '\0';
  local_5c[2] = '\0';
  local_5c[3] = '\0';
  local_5c[4] = '\0';
  local_5c[5] = '\0';
  local_5c[6] = '\0';
  local_5c[7] = '\0';
  local_5c[8] = '\0';
  local_5c[9] = '\0';
  local_5c[10] = '\0';
  local_5c[11] = '\0';
  local_5c[12] = '\0';
  local_5c[13] = '\0';
  local_5c[14] = '\0';
  local_5c[15] = '\0';
  local_5c[16] = '\0';
  local_5c[17] = '\0';
  local_5c[18] = '\0';
  local_5c[19] = '\0';
  local_5c[20] = '\0';
  local_5c[21] = '\0';
  local_5c[22] = '\0';
  local_5c[23] = '\0';
  local_5c[24] = '\0';
  local_5c[25] = '\0';
  local_5c[26] = '\0';
  local_5c[27] = '\0';
  local_5c[28] = '\0';
  local_5c[29] = '\0';
  local_5c[30] = '\0';
  local_5c[31] = '\0';
  local_3c = 0;
  local_38 = 0;
  local_34 = 0;
  local_30 = 0;
  local_2c = 0;
  local_28 = 0;
  local_24 = 0;
  local_20 = 0;
  iVar1 = apmib_get(0x1b67,&local_1c);
  if (iVar1 != 0) {
    pcVar2 = inet_ntoa(local_1c);
    strncpy(local_5c,pcVar2," ");
    iVar1 = apmib_get(0x1b68,local_18);
    if (iVar1 != 0) {
      pcVar2 = inet_ntoa(local_18[0]);
      strncpy(&local_3c,pcVar2," ");
    }
  }
  uVar3 = mxmlNewXML("1.0");
  uVar4 = mxmlNewElement(uVar3,"soap:Envelope");
  mxmlElementSetAttr(uVar4,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
  mxmlElementSetAttr(uVar4,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
  mxmlElementSetAttr(uVar4,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
  uVar4 = mxmlNewElement(uVar4,"soap:Body");
  uVar4 = mxmlNewElement(uVar4,"GetGuestNetworkSettingsResponse");
  mxmlElementSetAttr(uVar4,"xmlns","http://purenetworks.com/HNAP1/");
  uVar5 = mxmlNewElement(uVar4,"GetGuestNetworkSettingsResult");
  mxmlNewText(uVar5,0,"O");
  uVar5 = mxmlNewElement(uVar4,"IPAddress");
  mxmlNewText(uVar5,0,local_5c);
  uVar4 = mxmlNewElement(uVar4,"SubnetMask");
  mxmlNewText(uVar4,0,&local_3c);
  __ptr = mxmlSaveAllocString(uVar3,0);
  FUN_0041ed70("",200,__ptr,"");
  free(__ptr);
  mxmlDelete(uVar3);
  return 0;
}

