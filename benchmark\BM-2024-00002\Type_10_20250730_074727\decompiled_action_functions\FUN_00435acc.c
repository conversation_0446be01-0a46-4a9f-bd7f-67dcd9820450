
undefined4 FUN_00435acc(int param_1)

{
  int iVar1;
  int iVar2;
  void *__ptr;
  undefined4 local_c;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetIgnoreWizardConfig");
  }
  else {
    local_c = 0;
    syslog(6,"wizard is not set");
    local_c = 1;
    apmib_set(0x1b63,&local_c);
    apmib_update(4);
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"SOAP-ENV:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:SOAP-ENV","http://schemas.xmlsoap.org/soap/envelope/");
        mxmlElementSetAttr(iVar2,"SOAP-ENV:encodingStyle",
                           "http://schemas.xmlsoap.org/soap/encoding/");
        iVar2 = mxmlNewElement(iVar2,"SOAP-ENV:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"SetIgnoreWizardConfigResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("SetIgnoreWizardConfigResponse_xml=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar2 = mxmlNewElement(iVar2,"SetIgnoreWizardConfigResult");
            if (iVar2 == 0) {
              mxmlDelete(iVar1);
              puts("SetIgnoreWizardConfigResult_xml=NULL");
            }
            else {
              mxmlNewText(iVar2,0,"O");
              __ptr = mxmlSaveAllocString(iVar1,0);
              if (__ptr != 0) {
                FUN_0041ed70("",200,__ptr,"");
                free(__ptr);
              }
              mxmlDelete(iVar1);
            }
          }
        }
      }
    }
  }
  return 0;
}

