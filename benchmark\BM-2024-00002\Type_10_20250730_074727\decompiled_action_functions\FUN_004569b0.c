
undefined4 FUN_004569b0(void)

{
  char *__src;
  int iVar1;
  int iVar2;
  int iVar3;
  undefined4 uVar4;
  undefined4 uVar5;
  void *__ptr;
  int local_ce8;
  int local_ce4;
  in_addr local_ce0;
  byte local_cdc;
  byte local_cdb;
  byte local_cda;
  byte local_cd9;
  byte local_cd8;
  byte local_cd7;
  char acStack_cd6 [38];
  char local_cb0 [32];
  char acStack_c90 [64];
  char acStack_c50 [3140];
  
  local_cb0[0] = '\0';
  local_cb0[1] = '\0';
  local_cb0[2] = '\0';
  local_cb0[3] = '\0';
  local_cb0[4] = '\0';
  local_cb0[5] = '\0';
  local_cb0[6] = '\0';
  local_cb0[7] = '\0';
  local_cb0[8] = '\0';
  local_cb0[9] = '\0';
  local_cb0[10] = '\0';
  local_cb0[11] = '\0';
  local_cb0[12] = '\0';
  local_cb0[13] = '\0';
  local_cb0[14] = '\0';
  local_cb0[15] = '\0';
  local_cb0[16] = '\0';
  local_cb0[17] = '\0';
  local_cb0[18] = '\0';
  local_cb0[19] = '\0';
  local_cb0[20] = '\0';
  local_cb0[21] = '\0';
  local_cb0[22] = '\0';
  local_cb0[23] = '\0';
  local_cb0[24] = '\0';
  local_cb0[25] = '\0';
  local_cb0[26] = '\0';
  local_cb0[27] = '\0';
  local_cb0[28] = '\0';
  local_cb0[29] = '\0';
  memset(acStack_c90,0,0xc80);
  apmib_get(293,&local_ce4);
  for (local_ce8 = 1; local_ce8 <= local_ce4; local_ce8 = local_ce8 + 1) {
    memset(&local_ce0,0,".");
    memset(local_cb0,0,30);
    local_ce0.s_addr._0_1_ = local_ce8;
    apmib_get(0x8126,&local_ce0);
    sprintf(local_cb0,"%02x:%02x:%02x:%02x:%02x:%02x",local_cdc,local_cdb,
            local_cda,local_cd9,local_cd8,local_cd7);
    __src = inet_ntoa(local_ce0);
    strncpy(acStack_c90 + ((local_ce8 + -1) * 5 + 1) * " ",__src," ");
    strncpy(acStack_c90 + (local_ce8 + -1) * 160,local_cb0,30);
    strncpy(acStack_c50 + (local_ce8 + -1) * 160,acStack_cd6," ");
  }
  iVar1 = mxmlNewXML("1.0");
  if (iVar1 == 0) {
    printf("xml is NULL!");
  }
  else {
    iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
    if (iVar2 == 0) {
      printf("soap_env is NULL!");
    }
    else {
      mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
      mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
      mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
      iVar2 = mxmlNewElement(iVar2,"soap:Body");
      if (iVar2 == 0) {
        printf("body is NULL!");
      }
      else {
        iVar2 = mxmlNewElement(iVar2,"GetStaticClientInfoResponse");
        if (iVar2 == 0) {
          printf("GetStaticClientInfoResponse is NULL!");
        }
        else {
          mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
          iVar3 = mxmlNewElement(iVar2,"GetStaticClientInfoResult");
          if (iVar3 == 0) {
            printf("GetStaticClientInfoResult is NULL!");
          }
          else {
            mxmlNewText(iVar3,0,"O");
            iVar2 = mxmlNewElement(iVar2,"StaticClientInfoLists");
            if (iVar2 == 0) {
              printf("StaticClientInfoLists is NULL!");
            }
            else {
              for (local_ce8 = 0; local_ce8 < local_ce4; local_ce8 = local_ce8 + 1) {
                uVar4 = mxmlNewElement(iVar2,"ClientInfo");
                uVar5 = mxmlNewElement(uVar4,"MacAddress");
                mxmlNewText(uVar5,0,acStack_c90 + local_ce8 * 160);
                uVar5 = mxmlNewElement(uVar4,"IPv4Address");
                mxmlNewText(uVar5,0,acStack_c90 + (local_ce8 * 5 + 1) * " ");
                uVar4 = mxmlNewElement(uVar4,"DeviceName");
                mxmlNewText(uVar4,0,acStack_c50 + local_ce8 * 160);
              }
              __ptr = mxmlSaveAllocString(iVar1,0);
              FUN_0041ed70("",200,__ptr,"");
              free(__ptr);
              mxmlDelete(iVar1);
            }
          }
        }
      }
    }
  }
  return 0;
}

