
int FUN_0041f56c(undefined4 param_1)

{
  int iVar1;
  short sVar3;
  char *pcVar2;
  uint uVar4;
  uint local_188;
  undefined auStack_184 [4];
  undefined auStack_180 [4];
  undefined auStack_17c [4];
  undefined auStack_178 [16];
  undefined *local_168;
  undefined4 local_164;
  undefined *local_160;
  undefined4 local_15c;
  undefined *local_158;
  undefined4 local_154;
  undefined4 local_150;
  undefined *local_148;
  undefined4 local_144;
  undefined *local_140;
  undefined4 local_13c;
  undefined *local_138;
  undefined4 local_134;
  undefined *local_130;
  undefined4 local_12c;
  undefined *local_128;
  undefined4 local_124;
  undefined *local_120;
  undefined4 local_11c;
  undefined4 local_118;
  char acStack_110 [100];
  undefined auStack_ac [100];
  undefined auStack_48 [4];
  undefined auStack_44 [4];
  undefined auStack_40 [4];
  undefined auStack_3c [16];
  undefined auStack_2c [4];
  
  local_168 = auStack_184;
  local_160 = auStack_180;
  local_158 = auStack_17c;
  local_148 = auStack_ac;
  local_140 = auStack_48;
  local_138 = auStack_44;
  local_130 = auStack_40;
  local_128 = auStack_3c;
  local_120 = auStack_2c;
  local_144 = 100;
  local_124 = 16;
  local_150 = 0;
  local_164 = 0;
  local_15c = 0;
  local_154 = 0;
  local_118 = 0;
  local_13c = 0;
  local_134 = 0;
  local_12c = 0;
  local_11c = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar3 = HttpDenyPage(param_1);
  }
  else {
    iVar1 = httpGetEnv(param_1,"Upnpenable");
    if (((iVar1 != 0) || (iVar1 = httpGetEnv(param_1,"Upnpdisable"), iVar1 != 0)) &&
       (pcVar2 = httpGetEnv(param_1,"upnpenb"), pcVar2 != 0)) {
      iVar1 = strcmp("Enable",pcVar2);
      swSetUpnpState(iVar1 == 0);
    }
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "uPnPList");
    uVar4 = 0;
    do {
      iVar1 = pmGetEntry(uVar4);
      pcVar2 = "&nbsp;";
      if (iVar1 == 0) break;
      if (*(char *)(iVar1 + 28) != '\0') {
        pcVar2 = utf8ToUnicodeStr(iVar1 + 28,"&nbsp;");
      }
      uVar4 = uVar4 + 1;
      strcpy(acStack_110,pcVar2);
      pageParaSet(&local_148,acStack_110,0);
      local_188 = (uint)*(ushort *)(iVar1 + 8);
      pageParaSet(&local_148,&local_188,1);
      local_188 = (uint)(*(char *)(iVar1 + 12) != '\x06');
      pageParaSet(&local_148,&local_188,2);
      local_188 = (uint)*(ushort *)(iVar1 + 10);
      pageParaSet(&local_148,&local_188,3);
      inet_ntoa_b(*(undefined4 *)(iVar1 + 24),auStack_178);
      pageParaSet(&local_148,auStack_178,4);
      local_188 = (uint)*(byte *)(iVar1 + 13);
      pageParaSet(&local_148,&local_188,5);
      pageDynParaListPrintf(&local_148,param_1);
    } while (uVar4 != " ");
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "uPnPPara");
    local_188 = uVar4;
    pageParaSet(&local_168,&local_188,1);
    local_188 = igdIsEnabled();
    pageParaSet(&local_168,&local_188,0);
    local_188 = 6;
    pageParaSet(&local_168,&local_188,2);
    pageDynParaPrintf(&local_168,0,param_1);
    pageDynParaPrintf(&local_168,1,param_1);
    pageDynParaPrintf(&local_168,2,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/UpnpCfgRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar3 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar3;
}

