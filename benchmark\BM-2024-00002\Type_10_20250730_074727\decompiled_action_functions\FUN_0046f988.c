
undefined4 FUN_0046f988(undefined4 param_1)

{
  uint uVar1;
  int iVar2;
  undefined auStack_120 [16];
  undefined4 local_110;
  undefined4 local_10c;
  undefined auStack_108 [8];
  int local_100;
  int local_fc;
  int local_f8;
  int local_f4;
  undefined4 local_f0;
  char *local_ec;
  undefined *local_e8;
  int local_e4;
  undefined *local_e0;
  int local_dc;
  uint local_d8;
  uint local_d4;
  uint local_d0;
  undefined4 local_cc;
  uint local_c8;
  undefined4 local_c4;
  undefined *local_c0;
  uint local_b8;
  undefined4 local_b4;
  uint local_b0;
  uint local_ac;
  int local_a8;
  uint local_a4;
  int local_a0;
  uint local_9c;
  uint local_98;
  uint local_94;
  uint local_90;
  undefined4 local_8c;
  uint local_88;
  uint local_84;
  uint local_80;
  uint local_7c;
  uint local_78;
  undefined4 local_74;
  uint local_70;
  uint local_6c;
  int local_68;
  uint local_64;
  int local_60;
  uint local_5c;
  uint local_58;
  uint local_54;
  uint local_50;
  undefined4 local_4c;
  uint local_48;
  uint local_44;
  int local_40;
  uint local_3c;
  int local_38;
  uint local_34;
  uint local_30;
  uint local_2c;
  
  local_ec = 0;
  local_d8 = 0;
  local_d4 = 0;
  local_f0 = 0;
  local_f4 = 0;
  local_f8 = 0;
  local_fc = 0;
  local_100 = 0;
  local_f4 = mxmlLoadString(0,param_1,0);
  if (local_f4 == 0) {
    fwrite("AccessControl: tree is NULL,  exit\n",1,"#",stderr);
  }
  else {
    local_110 = 0;
    local_10c = 1;
    local_f8 = mxmlFindElement(local_f4,local_f4,"soap:Envelope",0);
    if (local_f8 == 0) {
      fwrite("AccessControl: state is NULL,  exit\n",1,"$",stderr);
      mxmlDelete(local_f4);
    }
    else {
      local_110 = 0;
      local_10c = 1;
      local_fc = mxmlFindElement(local_f8,local_f4,"GetAccessCtlList",0);
      if (local_fc == 0) {
        fwrite("AccessControl: state1 is failed\n",1," ",stderr);
        mxmlDelete(local_f4);
      }
      else {
        local_110 = 0;
        local_10c = 1;
        local_100 = mxmlFindElement(local_fc,local_f4,"ListType",0);
        local_ec = mxmlGetText(local_100,0);
        if ((local_100 == 0) || (local_ec == 0)) {
          fwrite("AccessControl: state2 or ListTypeValue is failed\n",1,"1",stderr);
          mxmlDelete(local_f4);
        }
        else {
          local_f0 = FUN_0046e090();
          iVar2 = strncmp(local_ec,"Black",5);
          if (iVar2 == 0) {
            iVar2 = apmib_get("{",&local_d8);
            if (iVar2 == 0) {
              fwrite("Get MIB_MACFILTER_TBL_NUM error\n",1," ",stderr);
              mxmlDelete(local_f4);
              return 0;
            }
            if (local_d8 == 0) {
              printf("No Black List in the MacFilter Table!\n ");
              FUN_0046deac();
            }
            else {
              local_dc = local_d8 - 1;
              local_b8 = local_d8;
              local_b4 = 0;
              local_ac = local_d8 >> 28;
              local_b0 = local_d8 * 16;
              local_a4 = local_b0 >> 30 | local_ac << 2;
              local_a8 = local_d8 * "@";
              uVar1 = local_d8 * "P";
              iVar2 = (uint)(uVar1 < local_b0) + local_ac + local_a4;
              local_9c = uVar1 >> 30 | iVar2 * 4;
              local_a0 = local_d8 * 320;
              local_98 = local_d8 * 400;
              local_94 = (uint)(local_98 < uVar1) + iVar2 + local_9c & 15;
              local_90 = local_d8;
              local_8c = 0;
              local_84 = local_d8 >> 28;
              local_88 = local_d8 * 16;
              uVar1 = local_d8 * "P";
              iVar2 = (uint)(uVar1 < local_88) + local_84 + (local_88 >> 30 | local_84 << 2);
              local_80 = local_d8 * 400;
              local_7c = (uint)(local_80 < uVar1) + iVar2 + (uVar1 >> 30 | iVar2 * 4) & 15;
              local_e0 = auStack_108 + -(local_d8 * "@" & 0xfffffff8);
              local_c0 = auStack_120;
              FUN_0046d358(local_e0,local_d8);
              iVar2 = FUN_0046e290(local_e0,local_d8,local_f0);
              if (iVar2 != 0) {
                puts("ApmibGetBlackAclInfo error");
                mxmlDelete(local_f4);
                return 0;
              }
              FUN_0046db40(local_e0,local_d8);
            }
          }
          else {
            iVar2 = strncmp(local_ec,"White",5);
            if (iVar2 == 0) {
              iVar2 = apmib_get(0x1cd4,&local_d4);
              if (iVar2 == 0) {
                fwrite("Get MIB_MACFILTER_WHITE_TBL_NUM error\n",1,"&",stderr);
                mxmlDelete(local_f4);
                return 0;
              }
              if (local_d4 == 0) {
                printf("No White List in the MacFilter Table!\n ");
                FUN_0046deac();
              }
              else {
                local_e4 = local_d4 - 1;
                local_78 = local_d4;
                local_74 = 0;
                local_c8 = local_d4;
                local_c4 = 0;
                local_6c = local_d4 >> 28;
                local_70 = local_d4 * 16;
                local_64 = local_70 >> 30 | local_6c << 2;
                local_68 = local_d4 * "@";
                uVar1 = local_d4 * "P";
                iVar2 = (uint)(uVar1 < local_70) + local_6c + local_64;
                local_5c = uVar1 >> 30 | iVar2 * 4;
                local_60 = local_d4 * 320;
                local_58 = local_d4 * 400;
                local_54 = (uint)(local_58 < uVar1) + iVar2 + local_5c & 15;
                local_50 = local_d4;
                local_4c = 0;
                local_d0 = local_d4;
                local_cc = 0;
                local_44 = local_d4 >> 28;
                local_48 = local_d4 * 16;
                local_3c = local_48 >> 30 | local_44 << 2;
                local_40 = local_d4 * "@";
                uVar1 = local_d4 * "P";
                iVar2 = (uint)(uVar1 < local_48) + local_44 + local_3c;
                local_34 = uVar1 >> 30 | iVar2 * 4;
                local_38 = local_d4 * 320;
                local_30 = local_d4 * 400;
                local_2c = (uint)(local_30 < uVar1) + iVar2 + local_34 & 15;
                local_e8 = auStack_108 + -(local_d4 * "@" & 0xfffffff8);
                FUN_0046d358(local_e8,local_d4);
                iVar2 = FUN_0046e60c(local_e8,local_d4,local_f0);
                if (iVar2 != 0) {
                  puts("ApmibGetBlackAclInfo error");
                  mxmlDelete(local_f4);
                  return 0;
                }
                FUN_0046db40(local_e8,local_d4);
              }
            }
          }
          mxmlDelete(local_f4);
        }
      }
    }
  }
  return 0;
}

