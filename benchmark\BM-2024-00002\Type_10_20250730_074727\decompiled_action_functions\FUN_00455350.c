
void FUN_00455350(void)

{
  int iVar1;
  FILE *pFVar2;
  int iVar3;
  int iVar4;
  undefined4 uVar5;
  undefined4 uVar6;
  void *__ptr;
  int local_29c4;
  void *local_29c0;
  int local_29b8;
  char acStack_29b0 [32];
  char acStack_2990 [32];
  char acStack_2970 [32];
  void *local_2950;
  char acStack_294c [100];
  undefined auStack_28e8 [32];
  char acStack_28c8 [32];
  stat sStack_28a8;
  size_t local_2810;
  char acStack_280c [64];
  char acStack_27cc [64];
  char acStack_278c [10116];
  
  local_29b8 = 0;
  local_29c0 = 0;
  local_2810 = 0;
  snprintf(acStack_294c,100,"%s/%s.pid","/var/run","udhcpd_br0");
  iVar1 = FUN_0042dad4(acStack_294c);
  snprintf(acStack_294c,100,"kill -SIGUSR1 %d\n",iVar1);
  if (0 < iVar1) {
    system(acStack_294c);
  }
  usleep(1000);
  memset(acStack_294c,0,100);
  snprintf(acStack_294c,100,"%s/%s.pid","/var/run","udhcpd_br1");
  iVar1 = FUN_0042dad4(acStack_294c);
  snprintf(acStack_294c,100,"kill -SIGUSR1 %d\n",iVar1);
  if (0 < iVar1) {
    system(acStack_294c);
  }
  usleep(1000);
  iVar1 = stat("/var/lib/misc/udhcpd.leases.br0",&sStack_28a8);
  if (iVar1 < 0) {
    printf("%s %d\n","GetDHCPClientInfo",0x43f);
  }
  else {
    local_2810 = sStack_28a8.st_blocks;
    if (sStack_28a8.st_blocks != 0) {
      local_29c0 = malloc(sStack_28a8.st_blocks);
      if (local_29c0 == 0) goto LAB_00455a28;
      memset(local_29c0,0,local_2810);
      pFVar2 = fopen("/var/lib/misc/udhcpd.leases.br0","r");
      if (pFVar2 == 0) goto LAB_00455a28;
      fread(local_29c0,1,local_2810,pFVar2);
      fclose(pFVar2);
      local_2950 = local_29c0;
      while( true ) {
        memset(acStack_29b0,0," ");
        memset(acStack_2990,0," ");
        memset(acStack_2970,0," ");
        memset(acStack_28c8,0," ");
        iVar1 = FUN_00455068(&local_2950,&local_2810,acStack_29b0,acStack_2990,acStack_2970,
                             auStack_28e8,acStack_28c8);
        if (iVar1 < 0) break;
        if (iVar1 != 0) {
          strncpy(acStack_280c + (local_29b8 * 5 + 1) * " ",acStack_29b0," ");
          strncpy(acStack_280c + local_29b8 * 160,acStack_2990," ");
          strncpy(acStack_27cc + local_29b8 * 160,acStack_28c8," ");
          strncpy(acStack_278c + local_29b8 * 160,acStack_2970," ");
          local_29b8 = local_29b8 + 1;
        }
      }
    }
  }
  iVar1 = stat("/var/lib/misc/udhcpd.leases.br1",&sStack_28a8);
  if (iVar1 < 0) {
    printf("%s %d\n","GetDHCPClientInfo",0x474);
  }
  else {
    local_2810 = sStack_28a8.st_blocks;
    if ((sStack_28a8.st_blocks != 0) &&
       (local_29c0 = malloc(sStack_28a8.st_blocks), local_29c0 != 0)) {
      memset(local_29c0,0,local_2810);
      pFVar2 = fopen("/var/lib/misc/udhcpd.leases.br1","r");
      if (pFVar2 != 0) {
        fread(local_29c0,1,local_2810,pFVar2);
        fclose(pFVar2);
        local_2950 = local_29c0;
        while( true ) {
          memset(acStack_29b0,0," ");
          memset(acStack_2990,0," ");
          memset(acStack_2970,0," ");
          memset(acStack_28c8,0," ");
          iVar1 = FUN_00455068(&local_2950,&local_2810,acStack_29b0,acStack_2990,acStack_2970,
                               auStack_28e8,acStack_28c8);
          if (iVar1 < 0) break;
          if (iVar1 != 0) {
            strncpy(acStack_280c + (local_29b8 * 5 + 1) * " ",acStack_29b0," ");
            strncpy(acStack_280c + local_29b8 * 160,acStack_2990," ");
            strncpy(acStack_27cc + local_29b8 * 160,acStack_28c8," ");
            strncpy(acStack_278c + local_29b8 * 160,acStack_2970," ");
            local_29b8 = local_29b8 + 1;
          }
        }
      }
    }
  }
LAB_00455a28:
  if (local_29c0 != 0) {
    free(local_29c0);
  }
  iVar1 = mxmlNewXML("1.0");
  if (iVar1 == 0) {
    printf("xml is NULL!");
  }
  else {
    iVar3 = mxmlNewElement(iVar1,"soap:Envelope");
    if (iVar3 == 0) {
      printf("soap_env is NULL!");
    }
    else {
      mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
      mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
      mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
      iVar3 = mxmlNewElement(iVar3,"soap:Body");
      if (iVar3 == 0) {
        printf("body is NULL!");
      }
      else {
        iVar3 = mxmlNewElement(iVar3,"GetDHCPClientInfoResponse");
        if (iVar3 == 0) {
          printf("GetDHCPClientInfoResponse is NULL!");
        }
        else {
          mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
          iVar4 = mxmlNewElement(iVar3,"GetDHCPClientInfoResult");
          if (iVar4 == 0) {
            printf("GetDHCPClientInfoResult is NULL!");
          }
          else {
            mxmlNewText(iVar4,0,"O");
            iVar3 = mxmlNewElement(iVar3,"DHCPClientInfoLists");
            if (iVar3 == 0) {
              printf("DHCPClientInfoLists is NULL!");
            }
            else {
              for (local_29c4 = 0; local_29c4 < local_29b8; local_29c4 = local_29c4 + 1) {
                if (acStack_280c[local_29c4 * 160] != '\0') {
                  uVar5 = mxmlNewElement(iVar3,"ClientInfo");
                  uVar6 = mxmlNewElement(uVar5,"MacAddress");
                  mxmlNewText(uVar6,0,acStack_280c + local_29c4 * 160);
                  uVar6 = mxmlNewElement(uVar5,"IPv4Address");
                  mxmlNewText(uVar6,0,acStack_280c + (local_29c4 * 5 + 1) * " ");
                  uVar6 = mxmlNewElement(uVar5,"DeviceName");
                  mxmlNewText(uVar6,0,acStack_27cc + local_29c4 * 160);
                  uVar5 = mxmlNewElement(uVar5,"LeaseTime");
                  mxmlNewText(uVar5,0,acStack_278c + local_29c4 * 160);
                }
              }
              __ptr = mxmlSaveAllocString(iVar1,0);
              FUN_0041ed70("",200,__ptr,"");
              free(__ptr);
              mxmlDelete(iVar1);
            }
          }
        }
      }
    }
  }
  return;
}

