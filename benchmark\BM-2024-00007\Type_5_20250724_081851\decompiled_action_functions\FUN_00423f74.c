
int FUN_00423f74(undefined4 param_1)

{
  bool bVar1;
  ushort uVar2;
  int *piVar3;
  int iVar4;
  short sVar10;
  char *pcVar5;
  char *pcVar6;
  size_t sVar7;
  int iVar8;
  undefined4 uVar9;
  long *plVar11;
  char *__s;
  undefined1 *__src;
  long *plVar12;
  long lVar13;
  char acStack_228 [8];
  char acStack_220 [15];
  undefined uStack_211;
  undefined4 *puStack_210;
  undefined4 uStack_20c;
  undefined *puStack_208;
  undefined4 uStack_204;
  undefined *puStack_200;
  undefined4 uStack_1fc;
  undefined *puStack_1f8;
  undefined4 uStack_1f4;
  uint *puStack_1f0;
  undefined4 uStack_1ec;
  char *pcStack_1e8;
  undefined4 uStack_1e4;
  char *pcStack_1e0;
  undefined4 uStack_1dc;
  int aiStack_1d8 [12];
  undefined4 uStack_1a8;
  int iStack_1a4;
  char acStack_1a0 [24];
  undefined uStack_188;
  in_addr_t iStack_184;
  in_addr_t iStack_180;
  char cStack_17c;
  ushort uStack_17a;
  ushort uStack_178;
  char acStack_176 [126];
  undefined4 uStack_f8;
  undefined auStack_f4 [25];
  undefined auStack_db [16];
  undefined auStack_cb [19];
  uint uStack_b8;
  char acStack_b4 [6];
  char acStack_ae [6];
  long alStack_a8 [31];
  long alStack_2c [2];
  
  puStack_210 = &uStack_f8;
  puStack_208 = auStack_f4;
  puStack_200 = auStack_db;
  puStack_1f8 = auStack_cb;
  puStack_1f0 = &uStack_b8;
  pcStack_1e8 = acStack_b4;
  pcStack_1e0 = acStack_ae;
  piVar3 = aiStack_1d8;
  plVar11 = alStack_a8;
  do {
    *piVar3 = plVar11;
    piVar3 = piVar3 + 2;
    plVar11 = (long *)(plVar11 + 31);
  } while (piVar3 != aiStack_1d8 + 8);
  uStack_204 = 25;
  uStack_1dc = 16;
  uStack_1fc = 16;
  uStack_1f4 = 16;
  uStack_1e4 = 16;
  aiStack_1d8[8] = 0;
  uStack_20c = 0;
  uStack_1ec = 0;
  piVar3 = aiStack_1d8 + 1;
  do {
    *piVar3 = 31;
    piVar3 = piVar3 + 2;
  } while (piVar3 != aiStack_1d8 + 9);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar4 = HttpAccessPermit(param_1);
  if (iVar4 == 0) {
    sVar10 = HttpDenyPage(param_1);
    goto LAB_00424888;
  }
  pcVar5 = httpGetEnv(param_1,"ClientId");
  lVar13 = 0;
  if (pcVar5 != 0) {
    lVar13 = atol(pcVar5);
  }
  memset(&uStack_1a8,0,176);
  memset(&uStack_f8,0,208);
  if ("" == 0) {
    memset(l_targetEntry,0,176);
  }
  iVar4 = httpGetEnv(param_1,"Return");
  if (iVar4 == 0) {
    iVar4 = httpGetEnv(param_1,"Next");
    if (iVar4 == 0) {
      iVar4 = swChkEntryName(1,0x5baa34);
      uVar9 = l_targetEntry._4_4_;
      if (iVar4 == 0) {
        "" = 0;
      }
      if ("" == 1) {
        uStack_f8 = l_targetEntry._4_4_;
        memcpy(auStack_f4,l_targetEntry + 8,25);
        if (uVar9 == 1) {
          if (l_targetEntry._36_4_ != 0) {
            inet_ntoa_b(l_targetEntry._36_4_,auStack_db);
          }
          if (l_targetEntry._40_4_ != 0) {
            inet_ntoa_b(l_targetEntry._40_4_,auStack_cb);
          }
          uStack_b8 = l_targetEntry[44];
          if (l_targetEntry._46_2_ != 0) {
            sprintf(acStack_b4,"%d");
          }
          if (l_targetEntry._48_2_ != 0) {
            sprintf(acStack_ae,"%d");
          }
        }
        else {
          uStack_b8 = l_targetEntry[44];
          __src = l_targetEntry + "2";
          plVar11 = alStack_a8;
          do {
            plVar12 = (long *)(plVar11 + 31);
            memcpy(plVar11,__src,31);
            __src = __src + 31;
            plVar11 = plVar12;
          } while (plVar12 != alStack_2c);
        }
      }
      else {
        uStack_f8 = 1;
      }
      alStack_2c[0] = lVar13;
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "wzdTargetInf");
      iVar4 = 0;
      do {
        iVar8 = iVar4 + 1;
        pageDynParaPrintf(&puStack_210,iVar4,param_1);
        iVar4 = iVar8;
      } while (iVar8 != 12);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      HttpWebV4Head(param_1,0,1);
      iVar4 = httpRpmFsA(param_1,"/userRpm/WzdAccessCtrlTargetAddRpm.htm");
      if (iVar4 == 2) {
        return 2;
      }
      iVar4 = 10;
    }
    else {
      uStack_1a8 = 1;
      iStack_1a4 = getEnvToInt(param_1,"target_type",0,1);
      pcVar5 = httpGetEnv(param_1,"targets_lists_name");
      if (pcVar5 != 0) {
        uStack_188 = 0;
        strncpy(acStack_1a0,pcVar5,24);
        printf("hosts_lists save: name - %s\n",acStack_1a0);
      }
      if (iStack_1a4 == 1) {
        pcVar5 = httpGetEnv(param_1,"dst_ip_start");
        if (pcVar5 != 0) {
          iVar4 = swChkDotIpAddr(pcVar5);
          if (iVar4 != 0) {
            acStack_220[0] = '\0';
            acStack_220[1] = '\0';
            acStack_220[2] = '\0';
            acStack_220[3] = '\0';
            uStack_211 = 0;
            strncpy(acStack_220,pcVar5,15);
            iStack_184 = inet_addr(acStack_220);
            goto LAB_00424288;
          }
LAB_004242c4:
          iVar4 = 0x1f43;
          goto LAB_0042487c;
        }
LAB_00424288:
        pcVar5 = httpGetEnv(param_1,"dst_ip_end");
        if (pcVar5 != 0) {
          iVar4 = swChkDotIpAddr(pcVar5);
          if (iVar4 == 0) goto LAB_004242c4;
          acStack_220[0] = '\0';
          acStack_220[1] = '\0';
          acStack_220[2] = '\0';
          acStack_220[3] = '\0';
          uStack_211 = 0;
          strncpy(acStack_220,pcVar5,15);
          iStack_180 = inet_addr(acStack_220);
        }
        if ((iStack_184 == 0) || (iStack_180 == 0)) {
          iStack_184 = iStack_184 + iStack_180;
          iStack_180 = iStack_184;
        }
        printf("hosts_lists save: ip %x-%x\n",iStack_184,iStack_180);
        pcVar5 = httpGetEnv(param_1,"proto");
        if (pcVar5 != 0) {
          iVar4 = atoi(pcVar5);
          cStack_17c = iVar4;
        }
        if (cStack_17c == '\x03') goto LAB_004245ac;
        pcVar5 = httpGetEnv(param_1,"dst_port_start");
        if (pcVar5 != 0) {
          iVar4 = atoi(pcVar5);
          if (iVar4 - 1U < -1) {
            uStack_17a = iVar4;
            goto LAB_004243fc;
          }
LAB_00424448:
          iVar4 = 0x1f44;
          goto LAB_0042487c;
        }
LAB_004243fc:
        pcVar5 = httpGetEnv(param_1,"dst_port_end");
        if (pcVar5 != 0) {
          iVar4 = atoi(pcVar5);
          if (-2 < iVar4 - 1U) goto LAB_00424448;
          uStack_178 = iVar4;
        }
        if ((uStack_17a == 0) || (uStack_178 == 0)) {
          uStack_17a = uStack_178 + uStack_17a;
          uStack_178 = uStack_17a;
        }
        uVar2 = uStack_17a;
        if (uStack_178 < uStack_17a) {
          uStack_17a = uStack_178;
          uStack_178 = uVar2;
        }
      }
      else {
        pcVar5 = acStack_176;
        iVar4 = 0;
        do {
          sprintf(acStack_228,"url_%d",iVar4);
          pcVar6 = httpGetEnv(param_1,acStack_228);
          if (pcVar6 != 0) {
            do {
              __s = pcVar6;
              pcVar6 = __s + 1;
            } while (*__s == ' ');
            if (__s != 0) {
              sVar7 = strlen(__s);
              if (30 < sVar7) {
                iVar4 = 9000;
                goto LAB_0042487c;
              }
              iVar8 = swChkLegalDomain(__s);
              if (iVar8 == 0) {
                iVar4 = 0x2329;
                goto LAB_0042487c;
              }
              pcVar5[30] = '\0';
              strncpy(pcVar5,__s,30);
            }
          }
          iVar4 = iVar4 + 1;
          pcVar5 = pcVar5 + 31;
        } while (iVar4 != 4);
      }
LAB_004245ac:
      bVar1 = "" != 0;
      if (bVar1) {
        iVar4 = swGetFilterEntryNumCfg(1);
        iVar4 = iVar4 + -1;
      }
      else {
        iVar4 = 0;
      }
      uVar9 = swSetAccessTargetsEntry(&uStack_1a8,iVar4,bVar1,1);
      iVar4 = swFilterFindErrorNum(uVar9);
      if (iVar4 == 0) {
        memcpy(l_targetEntry,&uStack_1a8,176);
        "" = 1;
        pcVar5 = "../userRpm/WzdAccessCtrlSchedAddRpm.htm";
        goto LAB_00424674;
      }
    }
LAB_0042487c:
    sVar10 = HttpErrorPage(param_1,iVar4,0,0);
  }
  else {
    pcVar5 = "../userRpm/WzdAccessCtrlHostAddRpm.htm";
LAB_00424674:
    sVar10 = GoUrl(param_1,pcVar5);
  }
LAB_00424888:
  return sVar10;
}

