
undefined4 FUN_004346ec(char *param_1)

{
  undefined4 uVar1;
  int iVar2;
  int iVar3;
  int iVar4;
  char *pcVar5;
  char *pcVar6;
  void *__ptr;
  uint local_334;
  undefined auStack_300 [68];
  undefined auStack_2bc [100];
  char acStack_258 [200];
  undefined4 local_190;
  undefined auStack_18c [128];
  undefined auStack_10c [260];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetDeviceSettings");
    uVar1 = 0;
  }
  else {
    memset(auStack_300,0,"A");
    memset(auStack_2bc,0,100);
    memset(acStack_258,0,200);
    local_190 = 0;
    iVar2 = mxmlLoadString(0,param_1,0);
    if (iVar2 == 0) {
      puts("ERROR!  tree is NULL");
      uVar1 = 0;
    }
    else {
      iVar3 = mxmlFindElement(iVar2,iVar2,"SetDeviceSettings",0,0,1);
      if (iVar3 != 0) {
        iVar4 = mxmlFindElement(iVar3,iVar2,"DeviceName",0,0,1);
        if (iVar4 == 0) {
          puts("ERROR!  state1 is NULL");
          return 0;
        }
        mxmlGetText(iVar4,0);
        iVar4 = mxmlFindElement(iVar3,iVar2,"PresentationURL",0,0,1);
        if (iVar4 == 0) {
          puts("ERROR!  state1 is NULL");
          return 0;
        }
        mxmlGetText(iVar4,0);
        iVar4 = mxmlFindElement(iVar3,iVar2,"CAPTCHA",0,0,1);
        if (iVar4 == 0) {
          puts("ERROR!  state1 is NULL");
          return 0;
        }
        pcVar5 = mxmlGetText(iVar4,0);
        if (pcVar5 != 0) {
          iVar4 = strcmp(pcVar5,"true");
          if (iVar4 == 0) {
            local_190 = 1;
            apmib_set(0x1b62,&local_190);
          }
          else {
            local_190 = 0;
            apmib_set(0x1b62,&local_190);
          }
        }
        memset(auStack_10c,0,256);
        iVar4 = mxmlFindElement(iVar3,iVar2,"ChangePassword",0,0,1);
        if (iVar4 == 0) {
          puts("ERROR!  state1 is NULL");
          return 0;
        }
        pcVar5 = mxmlGetText(iVar4,0);
        iVar3 = mxmlFindElement(iVar3,iVar2,"AdminPassword",0,0,1);
        if (iVar3 == 0) {
          puts("ERROR!  state1 is NULL");
          return 0;
        }
        iVar3 = mxmlGetText(iVar3,0);
        if (((iVar3 == 0) || ("" == 0)) ||
           (iVar4 = strcmp(pcVar5,"true"), iVar4 != 0)) {
          if (((iVar3 == 0) || ("" != 0)) ||
             (iVar4 = strcmp(pcVar5,"true"), iVar4 != 0)) {
            if (((iVar3 == 0) && ("" == 0)) &&
               (iVar3 = strcmp(pcVar5,"false"), iVar3 == 0)) {
              apmib_set(183,0);
            }
          }
          else {
            pcVar5 = strstr(param_1,"<SetDeviceSettings");
            memset(auStack_18c,0,128);
            pcVar6 = strstr(param_1,"</SetDeviceSettings");
            FUN_00427018(pcVar5,pcVar6,"<AdminPassword>","</AdminPassword>",auStack_18c);
            FUN_00426570(auStack_18c,auStack_10c);
            fprintf(stderr,"QSK_debug[%d]: %s --> %s\n",821,iVar3,auStack_10c);
            apmib_set(183,auStack_10c);
          }
        }
        else {
          for (local_334 = 0; local_334 < 20; local_334 = local_334 + 1) {
            if (((("")[local_334 * 20] != '\0') &&
                (pcVar5 = strstr("","" + local_334 * 20),
                pcVar5 != 0)) && ("" != '\0')) {
              memset(acStack_258,0,200);
              sprintf(acStack_258,"%s\\%s\\","","");
              FUN_004266e8(acStack_258,"" + local_334 * "!","");
              iVar4 = strcmp("","");
              if (iVar4 == 0) {
                FUN_00425ed0("decrypt","" + local_334 * "!",iVar3,auStack_300);
                FUN_00426570(auStack_300,auStack_10c);
                fprintf(stderr,"QSK_debug[%d]: %s --> %s\n",802,auStack_300,auStack_10c);
                apmib_set(183,auStack_10c);
                break;
              }
            }
          }
        }
      }
      iVar3 = mxmlNewXML("1.0");
      if (iVar3 == 0) {
        printf("Create new xml erro!!!");
        uVar1 = 0;
      }
      else {
        iVar4 = mxmlNewElement(iVar3,"SOAP-ENV:Envelope");
        if (iVar4 == 0) {
          mxmlDelete(iVar3);
          puts("soap_env=NULL");
          uVar1 = 0;
        }
        else {
          mxmlElementSetAttr(iVar4,"xmlns:SOAP-ENV","http://schemas.xmlsoap.org/soap/envelope/");
          mxmlElementSetAttr(iVar4,"SOAP-ENV:encodingStyle",
                             "http://schemas.xmlsoap.org/soap/encoding/");
          iVar4 = mxmlNewElement(iVar4,"SOAP-ENV:Body");
          if (iVar4 == 0) {
            mxmlDelete(iVar3);
            puts("body=NULL");
            uVar1 = 0;
          }
          else {
            iVar4 = mxmlNewElement(iVar4,"SetDeviceSettingsResponse");
            if (iVar4 == 0) {
              mxmlDelete(iVar3);
              puts("SetDeviceSettingsResponse_xml=NULL");
              uVar1 = 0;
            }
            else {
              mxmlElementSetAttr(iVar4,"xmlns","http://purenetworks.com/HNAP1/");
              iVar4 = mxmlNewElement(iVar4,"SetDeviceSettingsResult");
              if (iVar4 == 0) {
                mxmlDelete(iVar3);
                puts("SetDeviceSettingsResult_xml=NULL");
                uVar1 = 0;
              }
              else {
                mxmlNewText(iVar4,0,"O");
                if (("" == 0) &&
                   (__ptr = mxmlSaveAllocString(iVar3,0), __ptr != 0)) {
                  apmib_update(4);
                  FUN_0041ed70("",200,__ptr,"");
                  free(__ptr);
                }
                mxmlDelete(iVar3);
                uVar1 = mxmlDelete(iVar2);
              }
            }
          }
        }
      }
    }
  }
  return uVar1;
}

