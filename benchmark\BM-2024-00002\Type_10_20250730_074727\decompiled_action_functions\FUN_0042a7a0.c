
int FUN_0042a7a0(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  void *__ptr;
  undefined auStack_16c [200];
  char local_a4 [52];
  char acStack_70 [104];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetCAPTCHAsetting");
    iVar1 = 0;
  }
  else {
    memset(auStack_16c,0,200);
    local_a4[0] = '\0';
    local_a4[1] = '\0';
    local_a4[2] = '\0';
    local_a4[3] = '\0';
    local_a4[4] = '\0';
    local_a4[5] = '\0';
    local_a4[6] = '\0';
    local_a4[7] = '\0';
    local_a4[8] = '\0';
    local_a4[9] = '\0';
    local_a4[10] = '\0';
    local_a4[11] = '\0';
    local_a4[12] = '\0';
    local_a4[13] = '\0';
    local_a4[14] = '\0';
    local_a4[15] = '\0';
    local_a4[16] = '\0';
    local_a4[17] = '\0';
    local_a4[18] = '\0';
    local_a4[19] = '\0';
    local_a4[20] = '\0';
    local_a4[21] = '\0';
    local_a4[22] = '\0';
    local_a4[23] = '\0';
    local_a4[24] = '\0';
    local_a4[25] = '\0';
    local_a4[26] = '\0';
    local_a4[27] = '\0';
    local_a4[28] = '\0';
    local_a4[29] = '\0';
    local_a4[30] = '\0';
    local_a4[31] = '\0';
    local_a4[32] = '\0';
    local_a4[33] = '\0';
    local_a4[34] = '\0';
    local_a4[35] = '\0';
    local_a4[36] = '\0';
    local_a4[37] = '\0';
    local_a4[38] = '\0';
    local_a4[39] = '\0';
    local_a4[40] = '\0';
    local_a4[41] = '\0';
    local_a4[42] = '\0';
    local_a4[43] = '\0';
    local_a4[44] = '\0';
    local_a4[45] = '\0';
    local_a4[46] = '\0';
    local_a4[47] = '\0';
    local_a4[48] = '\0';
    local_a4[49] = '\0';
    memset(acStack_70,0,100);
    if (1 < 0x00000001) {
      sprintf(acStack_70,"rm /etc_ro/web/image/captcha_%d.gif",0x00000002 + -1);
    }
    FUN_004263f0(acStack_70,0,0);
    sprintf(local_a4,"/image/captcha_%d.gif",0x00000001);
    memset(acStack_70,0,100);
    snprintf(acStack_70,99,"captcha >/etc_ro/web/image/captcha_%d.gif",0x00000001);
    FUN_004263f0(acStack_70,0,0);
    memset(acStack_70,0,100);
    snprintf(acStack_70,99,"nvram_get verificationCode");
    memset("",0,10);
    FUN_004263f0(acStack_70,"",9);
    fprintf(stderr,"captchaData:%s\n","");
    
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      iVar1 = printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"SOAP-ENV:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
        iVar1 = 0;
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:SOAP-ENV","http://schemas.xmlsoap.org/soap/envelope/");
        mxmlElementSetAttr(iVar2,"SOAP-ENV:encodingStyle",
                           "http://schemas.xmlsoap.org/soap/encoding/");
        iVar2 = mxmlNewElement(iVar2,"SOAP-ENV:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("soap_env=NULL");
          iVar1 = 0;
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"GetCAPTCHAsettingResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("GetCAPTCHAsettingResponse_xml=NULL");
            iVar1 = 0;
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar3 = mxmlNewElement(iVar2,"GetCAPTCHAsettingResult");
            if (iVar3 == 0) {
              mxmlDelete(iVar1);
              puts("GetCAPTCHAsettingResult_xml=NULL");
              iVar1 = 0;
            }
            else {
              mxmlNewText(iVar3,0,"getCAPTCHAsettingResult");
              iVar2 = mxmlNewElement(iVar2,"CaptchaUrl");
              if (iVar2 == 0) {
                mxmlDelete(iVar1);
                puts("CaptchaUrl_xml=NULL");
                iVar1 = 0;
              }
              else {
                mxmlNewText(iVar2,0,local_a4);
                __ptr = mxmlSaveAllocString(iVar1,0);
                if (__ptr != 0) {
                  FUN_0041ed70("",200,__ptr,"");
                  free(__ptr);
                }
                iVar1 = mxmlDelete(iVar1);
              }
            }
          }
        }
      }
    }
  }
  return iVar1;
}

