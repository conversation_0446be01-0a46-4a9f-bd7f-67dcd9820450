
int FUN_0042c920(undefined4 param_1)

{
  int iVar1;
  short sVar4;
  char *pcVar2;
  uint32_t uVar3;
  char *__src;
  undefined4 uVar5;
  int iVar6;
  uint uVar7;
  code *pcVar8;
  uint local_510;
  uint local_50c;
  uint32_t local_508;
  uint32_t local_504;
  uint32_t local_500;
  uint32_t local_4fc;
  uint32_t local_4f8;
  undefined auStack_4f4 [8];
  char acStack_4ec [32];
  undefined *local_4cc;
  undefined4 local_4c8;
  undefined *local_4c4;
  undefined4 local_4c0;
  undefined *local_4bc;
  undefined4 local_4b8;
  undefined *local_4b4;
  undefined4 local_4b0;
  undefined *local_4ac;
  undefined4 local_4a8;
  undefined *local_4a4;
  undefined4 local_4a0;
  undefined *local_49c;
  undefined4 local_498;
  undefined *local_494;
  undefined4 local_490;
  undefined *local_48c;
  undefined4 local_488;
  undefined *local_484;
  undefined4 local_480;
  undefined *local_47c;
  undefined4 local_478;
  undefined *local_474;
  undefined4 local_470;
  undefined *local_46c;
  undefined4 local_468;
  undefined *local_464;
  undefined4 local_460;
  undefined *local_45c;
  undefined4 local_458;
  undefined *local_454;
  undefined4 local_450;
  undefined *local_44c;
  undefined4 local_448;
  undefined *local_444;
  undefined4 local_440;
  undefined *local_43c;
  undefined4 local_438;
  undefined *local_434;
  undefined4 local_430;
  undefined *local_42c;
  undefined4 local_428;
  undefined *local_424;
  undefined4 local_420;
  undefined *local_41c;
  undefined4 local_418;
  undefined *local_414;
  undefined4 local_410;
  undefined *local_40c;
  undefined4 local_408;
  undefined *local_404;
  undefined4 local_400;
  undefined *local_3fc;
  undefined4 local_3f8;
  undefined *local_3f4;
  undefined4 local_3f0;
  undefined4 local_3ec;
  undefined auStack_3e4 [8];
  uint local_3dc;
  in_addr_t local_3d8;
  in_addr_t local_3d4;
  in_addr_t local_3d0;
  in_addr_t local_3cc;
  char acStack_3c8 [63];
  undefined local_389;
  undefined auStack_388 [119];
  undefined local_311;
  undefined auStack_310 [119];
  undefined local_299;
  uint32_t local_298;
  uint32_t local_288;
  uint32_t local_284;
  uint local_280;
  uint local_27c;
  uint local_278;
  undefined auStack_274 [4];
  undefined auStack_270 [4];
  undefined auStack_26c [4];
  undefined auStack_268 [4];
  undefined auStack_264 [64];
  undefined auStack_224 [120];
  undefined auStack_1ac [120];
  undefined auStack_134 [4];
  undefined auStack_130 [4];
  undefined auStack_12c [16];
  undefined auStack_11c [16];
  undefined auStack_10c [16];
  undefined auStack_fc [4];
  undefined auStack_f8 [4];
  undefined auStack_f4 [4];
  undefined auStack_f0 [4];
  undefined auStack_ec [4];
  undefined auStack_e8 [16];
  undefined auStack_d8 [16];
  undefined auStack_c8 [16];
  undefined auStack_b8 [16];
  undefined auStack_a8 [16];
  undefined auStack_98 [16];
  undefined auStack_88 [16];
  undefined auStack_78 [16];
  undefined auStack_68 [4];
  undefined auStack_64 [64];
  undefined auStack_24 [8];
  
  local_4cc = auStack_274;
  local_4c4 = auStack_270;
  local_4bc = auStack_26c;
  local_4b4 = auStack_268;
  local_4ac = auStack_264;
  local_4a4 = auStack_224;
  local_49c = auStack_1ac;
  local_494 = auStack_134;
  local_48c = auStack_130;
  local_484 = auStack_12c;
  local_47c = auStack_11c;
  local_474 = auStack_10c;
  local_46c = auStack_fc;
  local_464 = auStack_f4;
  local_45c = auStack_f8;
  local_454 = auStack_f0;
  local_44c = auStack_ec;
  local_444 = auStack_e8;
  local_43c = auStack_d8;
  local_434 = auStack_c8;
  local_42c = auStack_b8;
  local_424 = auStack_a8;
  local_41c = auStack_98;
  local_414 = auStack_88;
  local_40c = auStack_78;
  local_404 = auStack_68;
  local_3fc = auStack_64;
  local_3f4 = auStack_24;
  local_4fc = 0;
  local_4f8 = 0;
  local_510 = 0;
  local_3ec = 0;
  local_4c8 = 0;
  local_4c0 = 0;
  local_3f8 = 16;
  local_480 = 16;
  local_478 = 16;
  local_470 = 16;
  local_440 = 16;
  local_438 = 16;
  local_430 = 16;
  local_428 = 16;
  local_420 = 16;
  local_418 = 16;
  local_410 = 16;
  local_408 = 16;
  local_498 = "x";
  local_4a0 = "x";
  local_4a8 = "@";
  local_4b8 = 0;
  local_4b0 = 0;
  local_490 = 0;
  local_488 = 0;
  local_468 = 0;
  local_460 = 0;
  local_458 = 0;
  local_450 = 0;
  local_448 = 0;
  local_400 = 0;
  local_3f0 = 0;
  memset(auStack_3e4,0,368);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar4 = HttpDenyPage(param_1);
    goto LAB_0042d9f0;
  }
  swGetL2tpUcCfg(auStack_3e4);
  iVar1 = httpGetEnv(param_1,"Connect");
  if ((iVar1 == 0) && (iVar1 = httpGetEnv(param_1,"Save"), iVar1 == 0)) {
    iVar1 = httpGetEnv(param_1,"Disconnect");
    uVar7 = 0;
    if (iVar1 != 0) {
      pcVar8 = swL2tpLinkDownReq;
LAB_0042d020:
      uVar7 = 1;
      (*pcVar8)();
      taskDelay("<");
    }
LAB_0042d048:
    swL2tpLinkStateGet(&local_510);
    swGetL2tpUcCfg(auStack_3e4);
    if (((local_3dc == 0) || (iVar1 = swDhcpcStateIsSucceed(0), iVar1 == 0)) || (local_510 == 0)) {
      local_508 = 0;
      local_504 = 0;
      local_500 = 0;
      local_4fc = 0;
      local_4f8 = 0;
    }
    else {
      swGetDhcpcNetInfo(0,&local_508,&local_504,&local_500,&local_4fc,&local_4f8);
    }
    local_50c = 1;
    pageParaSet(&local_4cc,&local_50c,0);
    local_50c = 0;
    pageParaSet(&local_4cc,&local_50c,1);
    local_50c = 6;
    pageParaSet(&local_4cc,&local_50c,2);
    pcVar2 = httpGetEnv(param_1,"IpType");
    if (pcVar2 != 0) {
      iVar1 = atoi(pcVar2);
      local_3dc = (uint)(iVar1 == 0);
    }
    local_50c = (uint)(local_3dc == 0);
    pageParaSet(&local_4cc,&local_50c,3);
    pageParaSet(&local_4cc,acStack_3c8,4);
    pageParaSet(&local_4cc,auStack_388,5);
    pageParaSet(&local_4cc,auStack_310,6);
    local_50c = (uint)(local_510 != 0);
    pageParaSet(&local_4cc,&local_50c,7);
    if (local_510 == 1) {
      local_50c = local_510;
    }
    else if (local_510 == 0) {
      local_50c = 0;
    }
    else {
      local_50c = 2;
    }
    pageParaSet(&local_4cc,&local_50c,8);
    local_50c = ntohl(local_508);
    sprintf(acStack_4ec,"%d.%d.%d.%d",local_50c >> 24,local_50c >> 16 & 255,
            local_50c >> 8 & 255,local_50c & 255);
    pageParaSet(&local_4cc,acStack_4ec,9);
    local_50c = ntohl(local_504);
    sprintf(acStack_4ec,"%d.%d.%d.%d",local_50c >> 24,local_50c >> 16 & 255,
            local_50c >> 8 & 255,local_50c & 255);
    pageParaSet(&local_4cc,acStack_4ec,10);
    local_50c = ntohl(local_500);
    sprintf(acStack_4ec,"%d.%d.%d.%d",local_50c >> 24,local_50c >> 16 & 255,
            local_50c >> 8 & 255,local_50c & 255);
    pageParaSet(&local_4cc,acStack_4ec,11);
    local_50c = ntohl(local_3d8);
    sprintf(acStack_4ec,"%d.%d.%d.%d",local_50c >> 24,local_50c >> 16 & 255,
            local_50c >> 8 & 255,local_50c & 255);
    pageParaSet(&local_4cc,acStack_4ec,18);
    local_50c = ntohl(local_3d4);
    sprintf(acStack_4ec,"%d.%d.%d.%d",local_50c >> 24,local_50c >> 16 & 255,
            local_50c >> 8 & 255,local_50c & 255);
    pageParaSet(&local_4cc,acStack_4ec,19);
    local_50c = ntohl(local_3d0);
    sprintf(acStack_4ec,"%d.%d.%d.%d",local_50c >> 24,local_50c >> 16 & 255,
            local_50c >> 8 & 255,local_50c & 255);
    pageParaSet(&local_4cc,acStack_4ec,20);
    local_50c = ntohl(local_3cc);
    sprintf(acStack_4ec,"%d.%d.%d.%d",local_50c >> 24,local_50c >> 16 & 255,
            local_50c >> 8 & 255,local_50c & 255);
    pageParaSet(&local_4cc,acStack_4ec,26);
    local_4fc = ntohl(local_4fc);
    sprintf(acStack_4ec,"%d.%d.%d.%d",local_4fc >> 24,local_4fc >> 16 & 255,
            local_4fc >> 8 & 255,local_4fc & 255);
    pageParaSet(&local_4cc,acStack_4ec,23);
    local_4f8 = ntohl(local_4f8);
    sprintf(acStack_4ec,"%d.%d.%d.%d",local_4f8 >> 24,local_4f8 >> 16 & 255,
            local_4f8 >> 8 & 255,local_4f8 & 255);
    pageParaSet(&local_4cc,acStack_4ec,24);
    local_50c = local_280;
    pageParaSet(&local_4cc,&local_50c,12);
    local_50c = local_27c;
    pageParaSet(&local_4cc,&local_50c,13);
    local_50c = local_278;
    pageParaSet(&local_4cc,&local_50c,14);
    local_50c = 0;
    pageParaSet(&local_4cc,&local_50c,15);
    pageParaSet(&local_4cc,&local_50c,16);
    swGetL2tpCfg(auStack_3e4);
    local_50c = ntohl(local_298);
    sprintf(acStack_4ec,"%d.%d.%d.%d",local_50c >> 24,local_50c >> 16 & 255,
            local_50c >> 8 & 255,local_50c & 255);
    pageParaSet(&local_4cc,acStack_4ec,17);
    local_50c = ntohl(local_288);
    uVar3 = ntohl(local_284);
    sprintf(acStack_4ec,"%d.%d.%d.%d",local_50c >> 24,local_50c >> 16 & 255,
            local_50c >> 8 & 255,local_50c & 255);
    pageParaSet(&local_4cc,acStack_4ec,21);
    sprintf(acStack_4ec,"%d.%d.%d.%d",uVar3 >> 24,uVar3 >> 16 & 255,uVar3 >> 8 & 255,
            uVar3 & 255);
    pageParaSet(&local_4cc,acStack_4ec,22);
    if ((uVar7 == 0) &&
       (((local_3dc == 1 && (local_3d8 == 0)) ||
        ((1 < local_510 || ((local_278 - 1 < 2 && (l2tpIpMaskGet(&local_50c,0), local_50c == 0))))))
       )) {
      uVar7 = 1;
    }
    local_50c = uVar7;
    pageParaSet(&local_4cc,&local_50c,25);
    swGetSystemMode(auStack_4f4);
    local_50c = 3;
    pageParaSet(&local_4cc,&local_50c,27);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "l2tpInf");
    iVar1 = 0;
    do {
      iVar6 = iVar1 + 1;
      pageDynParaPrintf(&local_4cc,iVar1,param_1);
      iVar1 = iVar6;
    } while (iVar6 != 28);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintfWanTypeInfo(param_1);
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/L2TPCfgRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    iVar1 = 10;
    pcVar2 = 0;
  }
  else {
    iVar1 = httpGetEnv(param_1,"L2TPName");
    if (iVar1 == 0) {
      pcVar8 = memset;
      iVar1 = 0;
      uVar5 = "x";
    }
    else {
      pcVar8 = strncpy;
      local_311 = 0;
      uVar5 = "w";
    }
    (*pcVar8)(auStack_388,iVar1,uVar5);
    iVar1 = httpGetEnv(param_1,"L2TPPwd");
    if (iVar1 == 0) {
      pcVar8 = memset;
      iVar1 = 0;
      uVar5 = "x";
    }
    else {
      pcVar8 = strncpy;
      local_299 = 0;
      uVar5 = "w";
    }
    (*pcVar8)(auStack_310,iVar1,uVar5);
    pcVar2 = httpGetEnv(param_1,"IpType");
    if (pcVar2 == 0) {
      local_3dc = 1;
    }
    else {
      iVar1 = atoi(pcVar2);
      local_3dc = (uint)(iVar1 == 0);
    }
    pcVar2 = httpGetEnv(param_1,"L2TPServerName");
    if (pcVar2 != 0) {
      do {
        __src = pcVar2;
        pcVar2 = __src + 1;
      } while (*__src == ' ');
      local_389 = 0;
      strncpy(acStack_3c8,__src,"?");
    }
    if (local_3dc == 0) {
      pcVar2 = httpGetEnv(param_1,"sta_ip");
      iVar1 = swChkDotIpAddr(pcVar2);
      if (iVar1 == 0) {
        pcVar2 = "";
        iVar1 = 0x138b;
      }
      else {
        local_3d8 = inet_addr(pcVar2);
        pcVar2 = httpGetEnv(param_1,"sta_mask");
        iVar1 = swChkDotIpAddr(pcVar2);
        if (iVar1 == 0) {
          pcVar2 = "";
          iVar1 = 0x138c;
        }
        else {
          local_3d4 = inet_addr(pcVar2);
          pcVar2 = httpGetEnv(param_1,"sta_gw");
          iVar1 = swChkDotIpAddr(pcVar2);
          if (iVar1 == 0) {
            pcVar2 = "";
            iVar1 = 0x138f;
          }
          else {
            local_3d0 = inet_addr(pcVar2);
            pcVar2 = httpGetEnv(param_1,"sta_dns");
            if (pcVar2 == 0) {
              pcVar2 = "0.0.0.0";
            }
            iVar1 = swChkDotIpAddr(pcVar2);
            if (iVar1 != 0) {
              local_3cc = inet_addr(pcVar2);
              goto LAB_0042ce74;
            }
            pcVar2 = "";
            iVar1 = 0x138d;
          }
        }
      }
    }
    else {
LAB_0042ce74:
      pcVar2 = httpGetEnv(param_1,"mtu");
      if (pcVar2 != 0) {
        local_280 = atoi(pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"idletime");
      if (pcVar2 != 0) {
        local_27c = atoi(pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"linktype");
      if (pcVar2 != 0) {
        local_278 = atoi(pcVar2);
      }
      if (local_3dc == 0) {
        pcVar8 = swChkL2tpCfg;
      }
      else {
        pcVar8 = swChkL2tpDomain;
      }
      sVar4 = (*pcVar8)(auStack_3e4);
      iVar1 = sVar4;
      pcVar2 = acStack_4ec;
      if (iVar1 == 0) {
        if (local_3dc != 0) {
          dhcpcSetDhcpcAutoDnsEnabled(7);
        }
        uVar7 = 1;
        swSetL2tpCfg(auStack_3e4);
        iVar1 = httpGetEnv(param_1,"Connect");
        if (iVar1 != 0) {
          pcVar8 = swL2tpLinkUpReq;
          goto LAB_0042d020;
        }
        goto LAB_0042d048;
      }
      sprintf(pcVar2,"../userRpm/L2TPCfgRpm.htm?wan=%d",0);
    }
  }
  sVar4 = HttpErrorPage(param_1,iVar1,pcVar2,0);
LAB_0042d9f0:
  return sVar4;
}

