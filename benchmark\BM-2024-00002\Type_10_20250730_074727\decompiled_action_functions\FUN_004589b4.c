
undefined4 FUN_004589b4(void)

{
  int iVar1;
  undefined4 uVar2;
  int iVar3;
  int iVar4;
  char *pcVar5;
  undefined4 uVar6;
  undefined **ppuVar7;
  void *__ptr;
  int local_98;
  int local_90;
  in_addr local_8c;
  in_addr local_88;
  in_addr local_84;
  char local_80;
  char acStack_68 [32];
  char acStack_48 [32];
  char acStack_28 [32];
  
  iVar1 = apmib_get(217,&local_90);
  if (iVar1 == 0) {
    fwrite("Get table entry error!\n",1,23,stderr);
    uVar2 = 0xffffffff;
  }
  else {
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      puts("xml is null");
      uVar2 = 0;
    }
    else {
      iVar3 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar3 == 0) {
        puts("soap_env is null");
        uVar2 = 0;
      }
      else {
        mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar3 = mxmlNewElement(iVar3,"soap:Body");
        if (iVar3 == 0) {
          puts("body is null");
          uVar2 = 0;
        }
        else {
          iVar3 = mxmlNewElement(iVar3,"GetStaticRouteSettingsResponse");
          if (iVar3 == 0) {
            puts("GetStaticRouteSettingsResponse is null");
            uVar2 = 0;
          }
          else {
            mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
            iVar4 = mxmlNewElement(iVar3,"GetStaticRouteSettingsResult");
            if (iVar4 == 0) {
              puts("GetStaticRouteSettingsResult is null");
              uVar2 = 0;
            }
            else {
              mxmlNewText(iVar4,0,"O");
              iVar3 = mxmlNewElement(iVar3,"StaticRouteClientInfoLists");
              if (iVar3 == 0) {
                puts("StaticRouteClientInfoLists is null");
                uVar2 = 0;
              }
              else {
                for (local_98 = 1; local_98 <= local_90; local_98 = local_98 + 1) {
                  local_8c.s_addr._0_1_ = local_98;
                  iVar4 = apmib_get(0x80da,&local_8c);
                  if (iVar4 == 0) {
                    return 0xffffffff;
                  }
                  pcVar5 = inet_ntoa(local_8c);
                  strcpy(acStack_68,pcVar5);
                  pcVar5 = inet_ntoa(local_88);
                  strcpy(acStack_48,pcVar5);
                  pcVar5 = inet_ntoa(local_84);
                  strcpy(acStack_28,pcVar5);
                  uVar2 = mxmlNewElement(iVar3,"ClientInfo");
                  uVar6 = mxmlNewElement(uVar2,"IPAddress");
                  mxmlNewText(uVar6,0,acStack_68);
                  uVar6 = mxmlNewElement(uVar2,"SubnetMask");
                  mxmlNewText(uVar6,0,acStack_48);
                  uVar6 = mxmlNewElement(uVar2,"Gateway");
                  mxmlNewText(uVar6,0,acStack_28);
                  uVar2 = mxmlNewElement(uVar2,"Interface");
                  if (local_80 == '\0') {
                    ppuVar7 = &PTR_0x004e414c;
                  }
                  else {
                    ppuVar7 = &PTR_0x004e4157;
                  }
                  mxmlNewText(uVar2,0,ppuVar7);
                }
                __ptr = mxmlSaveAllocString(iVar1,0);
                FUN_0041ed70("",200,__ptr,"");
                free(__ptr);
                uVar2 = mxmlDelete(iVar1);
              }
            }
          }
        }
      }
    }
  }
  return uVar2;
}

