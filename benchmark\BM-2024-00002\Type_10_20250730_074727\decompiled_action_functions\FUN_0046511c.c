
undefined4 FUN_0046511c(int param_1)

{
  undefined4 uVar1;
  int iVar2;
  int iVar3;
  char *pcVar4;
  int iVar5;
  void *__ptr;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetWPSSettings");
    uVar1 = 0;
  }
  else {
    iVar2 = mxmlLoadString(0,param_1,0);
    if (iVar2 == 0) {
      puts("tree=NULL");
      uVar1 = 0;
    }
    else {
      iVar3 = mxmlFindElement(iVar2,iVar2,"soap:Envelope",0,0,1);
      if ((iVar3 != 0) && (iVar3 = mxmlFindElement(iVar2,iVar2,"SetWPSSettings",0,0,1), iVar3 != 0))
      {
        iVar3 = mxmlFindElement(iVar3,iVar2,"WPSPBC",0,0,1);
        if (iVar3 == 0) {
          puts("state=NULL");
          mxmlDelete(iVar2);
          return 0;
        }
        pcVar4 = mxmlGetText(iVar3,0);
        if (pcVar4 == 0) {
          puts("value==NULL");
        }
        else {
          strncmp(pcVar4,"true",4);
        }
        iVar3 = mxmlFindElement(iVar3,iVar2,"WPSPIN",0,0,1);
        if (iVar3 == 0) {
          puts("state=NULL");
          mxmlDelete(iVar2);
          return 0;
        }
        pcVar4 = mxmlGetText(iVar3,0);
        if (pcVar4 == 0) {
          puts("value==NULL");
        }
        else {
          strncmp(pcVar4,"true",4);
        }
      }
      iVar3 = mxmlNewXML("1.0");
      if (iVar3 == 0) {
        puts("xml=NULL");
        mxmlDelete(iVar2);
        uVar1 = 0;
      }
      else {
        iVar5 = mxmlNewElement(iVar3,"soap:Envelope");
        if (iVar5 == 0) {
          mxmlDelete(iVar3);
          mxmlDelete(iVar2);
          puts("soap_env=NULL");
          uVar1 = 0;
        }
        else {
          mxmlElementSetAttr(iVar5,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
          mxmlElementSetAttr(iVar5,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
          mxmlElementSetAttr(iVar5,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
          iVar5 = mxmlNewElement(iVar5,"soap:Body");
          if (iVar5 == 0) {
            mxmlDelete(iVar3);
            mxmlDelete(iVar2);
            puts("body=NULL");
            uVar1 = 0;
          }
          else {
            iVar5 = mxmlNewElement(iVar5,"SetWPSSettingsResponse");
            if (iVar5 == 0) {
              mxmlDelete(iVar3);
              mxmlDelete(iVar2);
              puts("SetWPSSettingsResponse_xml=NULL");
              uVar1 = 0;
            }
            else {
              mxmlElementSetAttr(iVar5,"xmlns","http://purenetworks.com/HNAP1/");
              iVar5 = mxmlNewElement(iVar5,"SetWPSSettingsResult");
              if (iVar5 == 0) {
                mxmlDelete(iVar3);
                mxmlDelete(iVar2);
                puts("SetWPSSettingsResult_xml=NULL");
                uVar1 = 0;
              }
              else {
                mxmlNewText(iVar5,0,"O");
                if ("" == 0) {
                  apmib_update(4);
                  __ptr = mxmlSaveAllocString(iVar3,0);
                  if (__ptr == 0) {
                    puts("retstring=NULL");
                  }
                  else {
                    FUN_0041ed70("",200,__ptr,"");
                    free(__ptr);
                  }
                  mxmlDelete(iVar3);
                  uVar1 = mxmlDelete(iVar2);
                }
                else {
                  mxmlDelete(iVar3);
                  uVar1 = mxmlDelete(iVar2);
                }
              }
            }
          }
        }
      }
    }
  }
  return uVar1;
}

