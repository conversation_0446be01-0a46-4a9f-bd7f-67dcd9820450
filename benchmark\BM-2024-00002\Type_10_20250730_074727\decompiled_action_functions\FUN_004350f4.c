
undefined4 FUN_004350f4(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  int iVar4;
  int iVar5;
  FILE *__stream;
  undefined4 uVar6;
  void *__ptr;
  uint local_1e8;
  undefined **local_1dc;
  char local_1b4 [20];
  sysinfo local_1a0;
  undefined4 local_160;
  undefined4 local_15c;
  undefined4 local_158;
  undefined4 local_154;
  undefined4 local_150;
  undefined4 local_14c;
  undefined4 local_148;
  undefined4 local_144;
  undefined4 local_140;
  undefined4 local_13c;
  undefined4 local_138;
  undefined4 local_134;
  undefined4 local_130;
  undefined4 local_12c;
  undefined4 local_128;
  int local_124;
  char local_120 [12];
  undefined4 local_114;
  undefined4 local_110;
  undefined4 local_10c;
  char acStack_108 [200];
  undefined auStack_40 [4];
  undefined auStack_3c [4];
  undefined auStack_38 [4];
  undefined auStack_34 [4];
  uint local_30;
  uint local_2c;
  uint local_28;
  uint local_24;
  char local_20 [24];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetWanCurrentStatus");
  }
  else {
    local_1b4[0] = '\0';
    local_1b4[1] = '\0';
    local_1b4[2] = '\0';
    local_1b4[3] = '\0';
    local_1b4[4] = '\0';
    local_1b4[5] = '\0';
    local_1b4[6] = '\0';
    local_1b4[7] = '\0';
    local_1b4[8] = '\0';
    local_1b4[9] = '\0';
    local_1b4[10] = '\0';
    local_1b4[11] = '\0';
    local_1b4[12] = '\0';
    local_1b4[13] = '\0';
    local_1b4[14] = '\0';
    local_1b4[15] = '\0';
    local_1b4[16] = '\0';
    local_1b4[17] = '\0';
    local_1b4[18] = '\0';
    local_1b4[19] = '\0';
    local_160 = 0;
    local_15c = 0;
    local_158 = 0;
    local_154 = 0;
    local_150 = 0;
    local_14c = 0;
    local_148 = 0;
    local_144 = 0;
    local_140 = 0;
    local_13c = 0;
    local_138 = 0;
    local_134 = 0;
    local_130 = 0;
    local_12c = 0;
    local_128 = 0;
    local_124 = 0;
    local_120[0] = '\0';
    local_120[1] = '\0';
    local_120[2] = '\0';
    local_120[3] = '\0';
    local_120[4] = '\0';
    local_120[5] = '\0';
    local_120[6] = '\0';
    local_120[7] = '\0';
    local_120[8] = '\0';
    local_120[9] = '\0';
    local_120[10] = '\0';
    local_120[11] = '\0';
    local_114 = 0;
    local_110 = 0;
    local_10c = 0;
    memset(acStack_108,0,200);
    local_20[0] = '\0';
    local_20[1] = '\0';
    local_20[2] = '\0';
    local_20[3] = '\0';
    local_20[4] = '\0';
    local_20[5] = '\0';
    local_20[6] = '\0';
    local_20[7] = '\0';
    local_20[8] = '\0';
    local_20[9] = '\0';
    local_20[10] = '\0';
    local_20[11] = '\0';
    local_20[12] = '\0';
    local_20[13] = '\0';
    local_20[14] = '\0';
    local_20[15] = '\0';
    local_20[16] = '\0';
    local_20[17] = '\0';
    local_20[18] = '\0';
    local_20[19] = '\0';
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"SOAP-ENV:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:SOAP-ENV","http://schemas.xmlsoap.org/soap/envelope/");
        mxmlElementSetAttr(iVar2,"SOAP-ENV:encodingStyle",
                           "http://schemas.xmlsoap.org/soap/encoding/");
        iVar2 = mxmlNewElement(iVar2,"SOAP-ENV:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"GetWanCurrentStatusResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("GetWanCurrentStatusResponse_xml=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar3 = mxmlNewElement(iVar2,"GetWanCurrentStatusResult");
            if (iVar3 == 0) {
              mxmlDelete(iVar1);
              puts("GetWanCurrentStatusResult_xml=NULL");
            }
            else {
              mxmlNewText(iVar3,0,"O");
              iVar3 = mxmlNewElement(iVar2,"WanStatus");
              if (iVar3 == 0) {
                mxmlDelete(iVar1);
                puts("WanStatus_xml=NULL");
              }
              else {
                FUN_00426a2c("br0",&local_14c);
                FUN_00426b40("br0",&local_138);
                apmib_get("h",&local_124);
                if (local_124 == 3) {
                  FUN_00426a2c("ppp0",&local_160);
                }
                else {
                  FUN_00426a2c("eth1",&local_160);
                }
                FUN_00432b20("br0",2,auStack_40);
                FUN_00432b20("br0",4,auStack_3c);
                if (local_124 == 3) {
                  FUN_00432b20("ppp0",2,auStack_38);
                  FUN_00432b20("ppp0",4,auStack_34);
                  memset(acStack_108,0,200);
                  snprintf(acStack_108,200,"cat /proc/net/dev | grep ppp0 |  awk \'{ print $2}\'");
                  FUN_004263f0(acStack_108,local_120,11);
                  memset(acStack_108,0,200);
                  snprintf(acStack_108,200,"cat /proc/net/dev | grep ppp0 |  awk \'{ print $10}\'");
                  FUN_004263f0(acStack_108,&local_114,11);
                  iVar4 = atoi(&local_114);
                  iVar5 = atoi(local_120);
                  sysinfo(&local_1a0);
                  if (local_1a0.uptime == "") {
                    local_1e8 = 0;
                  }
                  else {
                    local_1e8 = (uint)((iVar4 - "") + (iVar5 - "")) /
                                (uint)(local_1a0.uptime - "");
                    if (local_1a0.uptime - "" == 0) {
                      trap(7);
                    }
                  }
                  "" = local_1a0.uptime;
                  "" = iVar4;
                  "" = iVar5;
                }
                else {
                  FUN_00432b20("eth1",2,auStack_38);
                  FUN_00432b20("eth1",4,auStack_34);
                  memset(acStack_108,0,200);
                  snprintf(acStack_108,200,"cat /proc/net/dev | grep eth1 |  awk \'{ print $2}\'");
                  FUN_004263f0(acStack_108,local_120,11);
                  memset(acStack_108,0,200);
                  snprintf(acStack_108,200,"cat /proc/net/dev | grep eth1 |  awk \'{ print $10}\'");
                  FUN_004263f0(acStack_108,&local_114,11);
                  iVar4 = atoi(&local_114);
                  iVar5 = atoi(local_120);
                  sysinfo(&local_1a0);
                  if (local_1a0.uptime == "") {
                    local_1e8 = 0;
                  }
                  else {
                    local_1e8 = (uint)((iVar4 - "") + (iVar5 - "")) /
                                (uint)(local_1a0.uptime - "");
                    if (local_1a0.uptime - "" == 0) {
                      trap(7);
                    }
                  }
                  "" = local_1a0.uptime;
                  "" = iVar4;
                  "" = iVar5;
                }
                memcpy(&local_30,auStack_40,4);
                memcpy(&local_2c,auStack_3c,4);
                memcpy(&local_28,auStack_38,4);
                memcpy(&local_24,auStack_34,4);
                iVar4 = FUN_00426c54();
                if (iVar4 == 0) {
                  if ((local_30 & local_2c) == (local_28 & local_24)) {
                    local_1dc = (undefined **)"Conflict";
                  }
                  else {
                    __stream = fopen("/tmp/InternetConnectStatus","r+");
                    if (__stream == 0) {
                      local_1dc = &PTR_0x004e4157;
                    }
                    else {
                      fread(local_20,1,19,__stream);
                      iVar4 = strncmp(local_20,"OK",2);
                      if (iVar4 == 0) {
                        local_1dc = (undefined **)"success";
                      }
                      else {
                        local_1dc = &PTR_0x004e4157;
                      }
                      fclose(__stream);
                    }
                  }
                }
                else {
                  local_1dc = (undefined **)"Cable";
                }
                mxmlNewText(iVar3,0,local_1dc);
                uVar6 = mxmlNewElement(iVar2,"Speed");
                snprintf(local_1b4,20,"%d",local_1e8);
                mxmlNewText(uVar6,0,local_1b4);
                __ptr = mxmlSaveAllocString(iVar1,0);
                if (__ptr != 0) {
                  FUN_0041ed70("",200,__ptr,"");
                  free(__ptr);
                }
                mxmlDelete(iVar1);
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

