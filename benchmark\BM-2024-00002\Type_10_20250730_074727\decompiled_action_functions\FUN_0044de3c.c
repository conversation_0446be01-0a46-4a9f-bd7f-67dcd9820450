
undefined4 FUN_0044de3c(int param_1)

{
  undefined4 uVar1;
  int iVar2;
  int iVar3;
  char *__s1;
  void *__ptr;
  undefined4 local_10 [2];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetWanSettings");
    uVar1 = 0;
  }
  else {
    local_10[0] = 0;
    iVar2 = mxmlLoadString(0,param_1,0);
    if (iVar2 == 0) {
      puts("tree=NULL");
    }
    iVar3 = mxmlFindElement(iVar2,iVar2,"Type",0,0,1);
    if (iVar3 == 0) {
      puts("state=NULL");
    }
    __s1 = mxmlGetText(iVar3,0);
    if (__s1 != 0) {
      iVar3 = strncmp(__s1,"DHCP",5);
      if (iVar3 == 0) {
        local_10[0] = 1;
        FUN_0044d094(iVar2);
      }
      else {
        iVar3 = strncmp(__s1,"Static",7);
        if (iVar3 == 0) {
          local_10[0] = 0;
          FUN_0044c93c(iVar2);
          iVar2 = FUN_00426c54();
          if (iVar2 == 0) {
            FUN_0042c320(3);
            FUN_0042c320(0);
          }
        }
        else {
          iVar3 = strncmp(__s1,"DHCPPPPoE",10);
          if (iVar3 == 0) {
            local_10[0] = 3;
            FUN_0044d628(iVar2);
          }
          else {
            puts("type is unknown!");
          }
        }
      }
    }
    iVar2 = apmib_set("h",local_10);
    if (iVar2 == 0) {
      puts("apmib set MIB_WAN_DHCP is error!");
    }
    iVar2 = mxmlNewXML("1.0");
    if (iVar2 == 0) {
      printf("Create new xml erro!!!");
      uVar1 = 0;
    }
    else {
      iVar3 = mxmlNewElement(iVar2,"soap:Envelope");
      if (iVar3 == 0) {
        mxmlDelete(iVar2);
        puts("soap_env=NULL");
        uVar1 = 0;
      }
      else {
        mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar3 = mxmlNewElement(iVar3,"soap:Body");
        if (iVar3 == 0) {
          mxmlDelete(iVar2);
          puts("body=NULL");
          uVar1 = 0;
        }
        else {
          iVar3 = mxmlNewElement(iVar3,"SetWanSettingsResponse");
          if (iVar3 == 0) {
            mxmlDelete(iVar2);
            puts("SetWanSettingsResponse=NULL");
            uVar1 = 0;
          }
          else {
            mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
            iVar3 = mxmlNewElement(iVar3,"SetWanSettingsResult");
            if (iVar3 == 0) {
              mxmlDelete(iVar2);
              puts("SetWanSettingsResult=NULL");
              uVar1 = 0;
            }
            else {
              mxmlNewText(iVar3,0,FUN_004ad49c);
              if ("" == 0) {
                __ptr = mxmlSaveAllocString(iVar2,0);
                if (__ptr != 0) {
                  FUN_0041ed70("",200,__ptr,"");
                  apmib_update(4);
                  system("killall dhcpd");
                  FUN_004263f0("init.sh gw wan",0,0);
                  free(__ptr);
                }
              }
              uVar1 = mxmlDelete(iVar2);
            }
          }
        }
      }
    }
  }
  return uVar1;
}

