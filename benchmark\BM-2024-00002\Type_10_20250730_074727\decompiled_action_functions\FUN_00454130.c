
void FUN_00454130(void)

{
  int iVar1;
  char *pcVar2;
  undefined4 uVar3;
  undefined4 uVar4;
  undefined4 uVar5;
  void *__ptr;
  char local_150 [32];
  undefined4 local_130;
  undefined4 local_12c;
  undefined4 local_128;
  undefined4 local_124;
  undefined4 local_120;
  undefined4 local_11c;
  undefined4 local_118;
  undefined4 local_114;
  char local_110 [32];
  undefined4 local_f0;
  undefined4 local_ec;
  undefined4 local_e8;
  undefined4 local_e4;
  undefined4 local_e0;
  undefined4 local_dc;
  undefined4 local_d8;
  undefined4 local_d4;
  char local_d0 [32];
  undefined4 local_b0;
  undefined4 local_ac;
  undefined4 local_a8;
  undefined4 local_a4;
  undefined4 local_a0;
  undefined4 local_9c;
  undefined4 local_98;
  undefined4 local_94;
  char local_90 [32];
  in_addr local_70;
  in_addr local_6c;
  in_addr local_68;
  in_addr local_64;
  undefined4 local_60;
  byte local_5c;
  byte local_5b;
  byte local_5a;
  byte local_59;
  byte local_58;
  byte local_57;
  int local_54 [9];
  undefined local_30;
  undefined4 local_2c;
  undefined4 local_28;
  undefined4 local_24;
  undefined4 local_20;
  undefined4 local_1c;
  undefined4 local_18;
  undefined4 local_14;
  undefined4 local_10;
  undefined local_c;
  
  local_150[0] = '\0';
  local_150[1] = '\0';
  local_150[2] = '\0';
  local_150[3] = '\0';
  local_150[4] = '\0';
  local_150[5] = '\0';
  local_150[6] = '\0';
  local_150[7] = '\0';
  local_150[8] = '\0';
  local_150[9] = '\0';
  local_150[10] = '\0';
  local_150[11] = '\0';
  local_150[12] = '\0';
  local_150[13] = '\0';
  local_150[14] = '\0';
  local_150[15] = '\0';
  local_150[16] = '\0';
  local_150[17] = '\0';
  local_150[18] = '\0';
  local_150[19] = '\0';
  local_150[20] = '\0';
  local_150[21] = '\0';
  local_150[22] = '\0';
  local_150[23] = '\0';
  local_150[24] = '\0';
  local_150[25] = '\0';
  local_150[26] = '\0';
  local_150[27] = '\0';
  local_150[28] = '\0';
  local_150[29] = '\0';
  local_150[30] = '\0';
  local_150[31] = '\0';
  local_130 = 0;
  local_12c = 0;
  local_128 = 0;
  local_124 = 0;
  local_120 = 0;
  local_11c = 0;
  local_118 = 0;
  local_114 = 0;
  local_110[0] = '\0';
  local_110[1] = '\0';
  local_110[2] = '\0';
  local_110[3] = '\0';
  local_110[4] = '\0';
  local_110[5] = '\0';
  local_110[6] = '\0';
  local_110[7] = '\0';
  local_110[8] = '\0';
  local_110[9] = '\0';
  local_110[10] = '\0';
  local_110[11] = '\0';
  local_110[12] = '\0';
  local_110[13] = '\0';
  local_110[14] = '\0';
  local_110[15] = '\0';
  local_110[16] = '\0';
  local_110[17] = '\0';
  local_110[18] = '\0';
  local_110[19] = '\0';
  local_110[20] = '\0';
  local_110[21] = '\0';
  local_110[22] = '\0';
  local_110[23] = '\0';
  local_110[24] = '\0';
  local_110[25] = '\0';
  local_110[26] = '\0';
  local_110[27] = '\0';
  local_110[28] = '\0';
  local_110[29] = '\0';
  local_110[30] = '\0';
  local_110[31] = '\0';
  local_f0 = 0;
  local_ec = 0;
  local_e8 = 0;
  local_e4 = 0;
  local_e0 = 0;
  local_dc = 0;
  local_d8 = 0;
  local_d4 = 0;
  local_d0[0] = '\0';
  local_d0[1] = '\0';
  local_d0[2] = '\0';
  local_d0[3] = '\0';
  local_d0[4] = '\0';
  local_d0[5] = '\0';
  local_d0[6] = '\0';
  local_d0[7] = '\0';
  local_d0[8] = '\0';
  local_d0[9] = '\0';
  local_d0[10] = '\0';
  local_d0[11] = '\0';
  local_d0[12] = '\0';
  local_d0[13] = '\0';
  local_d0[14] = '\0';
  local_d0[15] = '\0';
  local_d0[16] = '\0';
  local_d0[17] = '\0';
  local_d0[18] = '\0';
  local_d0[19] = '\0';
  local_d0[20] = '\0';
  local_d0[21] = '\0';
  local_d0[22] = '\0';
  local_d0[23] = '\0';
  local_d0[24] = '\0';
  local_d0[25] = '\0';
  local_d0[26] = '\0';
  local_d0[27] = '\0';
  local_d0[28] = '\0';
  local_d0[29] = '\0';
  local_d0[30] = '\0';
  local_d0[31] = '\0';
  local_b0 = 0;
  local_ac = 0;
  local_a8 = 0;
  local_a4 = 0;
  local_a0 = 0;
  local_9c = 0;
  local_98 = 0;
  local_94 = 0;
  local_90[0] = '\0';
  local_90[1] = '\0';
  local_90[2] = '\0';
  local_90[3] = '\0';
  local_90[4] = '\0';
  local_90[5] = '\0';
  local_90[6] = '\0';
  local_90[7] = '\0';
  local_90[8] = '\0';
  local_90[9] = '\0';
  local_90[10] = '\0';
  local_90[11] = '\0';
  local_90[12] = '\0';
  local_90[13] = '\0';
  local_90[14] = '\0';
  local_90[15] = '\0';
  local_90[16] = '\0';
  local_90[17] = '\0';
  local_90[18] = '\0';
  local_90[19] = '\0';
  local_90[20] = '\0';
  local_90[21] = '\0';
  local_90[22] = '\0';
  local_90[23] = '\0';
  local_90[24] = '\0';
  local_90[25] = '\0';
  local_90[26] = '\0';
  local_90[27] = '\0';
  local_90[28] = '\0';
  local_90[29] = '\0';
  local_90[30] = '\0';
  local_90[31] = '\0';
  local_60 = 0;
  local_54[0] = 255;
  local_54[1] = 0;
  local_54[2] = 0;
  local_54[3] = 0;
  local_54[4] = 0;
  local_54[5] = 0;
  local_54[6] = 0;
  local_54[7] = 0;
  local_54[8] = 0;
  local_30 = 0;
  local_2c = 0;
  local_28 = 0;
  local_24 = 0;
  local_20 = 0;
  local_1c = 0;
  local_18 = 0;
  local_14 = 0;
  local_10 = 0;
  local_c = 0;
  memset(&local_5c,0,6);
  apmib_get(201,&local_5c);
  sprintf(local_150,"%02x:%02x:%02x:%02x:%02x:%02x",local_5c,local_5b,local_5a,
          local_59,local_58,local_57);
  iVar1 = apmib_get(170,&local_70);
  if (iVar1 != 0) {
    pcVar2 = inet_ntoa(local_70);
    strncpy(&local_f0,pcVar2," ");
    iVar1 = apmib_get(173,local_54);
    if (iVar1 != 0) {
      if (local_54[0] == 0) {
        memcpy(&local_130,"false",5);
      }
      else if (local_54[0] == 2) {
        memcpy(&local_130,"true",4);
      }
      else {
        memcpy(&local_130,"error",5);
      }
      iVar1 = apmib_get(171,&local_6c);
      if (iVar1 != 0) {
        pcVar2 = inet_ntoa(local_6c);
        strncpy(local_90,pcVar2," ");
        iVar1 = apmib_get(174,&local_68);
        if (iVar1 != 0) {
          pcVar2 = inet_ntoa(local_68);
          strncpy(local_d0,pcVar2," ");
          iVar1 = apmib_get(175,&local_64);
          if (iVar1 != 0) {
            pcVar2 = inet_ntoa(local_64);
            strncpy(&local_b0,pcVar2," ");
            iVar1 = apmib_get(945,&local_60);
            if (iVar1 != 0) {
              snprintf(local_110," ","%um",local_60);
            }
          }
        }
      }
    }
  }
  uVar3 = mxmlNewXML("1.0");
  uVar4 = mxmlNewElement(uVar3,"soap:Envelope");
  mxmlElementSetAttr(uVar4,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
  mxmlElementSetAttr(uVar4,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
  mxmlElementSetAttr(uVar4,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
  uVar4 = mxmlNewElement(uVar4,"soap:Body");
  uVar4 = mxmlNewElement(uVar4,"GetNetworkSettingsResponse");
  mxmlElementSetAttr(uVar4,"xmlns","http://purenetworks.com/HNAP1/");
  uVar5 = mxmlNewElement(uVar4,"GetNetworkSettingsResult");
  mxmlNewText(uVar5,0,"O");
  uVar5 = mxmlNewElement(uVar4,"MACAddress");
  mxmlNewText(uVar5,0,local_150);
  uVar5 = mxmlNewElement(uVar4,"IPAddress");
  mxmlNewText(uVar5,0,&local_f0);
  uVar5 = mxmlNewElement(uVar4,"DHCPenable");
  mxmlNewText(uVar5,0,&local_130);
  uVar5 = mxmlNewElement(uVar4,"IPRangeStart");
  mxmlNewText(uVar5,0,local_d0);
  uVar5 = mxmlNewElement(uVar4,"IPRangeEnd");
  mxmlNewText(uVar5,0,&local_b0);
  uVar5 = mxmlNewElement(uVar4,"LeaseTime");
  mxmlNewText(uVar5,0,local_110);
  uVar4 = mxmlNewElement(uVar4,"SubnetMask");
  mxmlNewText(uVar4,0,local_90);
  __ptr = mxmlSaveAllocString(uVar3,0);
  FUN_0041ed70("",200,__ptr,"");
  free(__ptr);
  mxmlDelete(uVar3);
  return;
}

