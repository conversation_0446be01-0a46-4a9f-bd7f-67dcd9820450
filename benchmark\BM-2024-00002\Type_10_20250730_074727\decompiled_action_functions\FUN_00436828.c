
undefined * FUN_00436828(int param_1)

{
  undefined *puVar1;
  int iVar2;
  int iVar3;
  int iVar4;
  void *__ptr;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetMyDLinkSettings");
    puVar1 = 0;
  }
  else {
    iVar2 = mxmlNewXML("1.0");
    if (iVar2 == 0) {
      puts("xml=NULL");
      puVar1 = 0;
    }
    else {
      iVar3 = mxmlNewElement(iVar2,"soap:Envelope");
      if (iVar3 == 0) {
        mxmlDelete(iVar2);
        puts("soap_env=NULL");
        puVar1 = 0;
      }
      else {
        mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar3 = mxmlNewElement(iVar3,"soap:Body");
        if (iVar3 == 0) {
          mxmlDelete(iVar2);
          puts("body=NULL");
          puVar1 = 0;
        }
        else {
          iVar3 = mxmlNewElement(iVar3,"GetMyDLinkSettingsResponse");
          if (iVar3 == 0) {
            mxmlDelete(iVar2);
            puts("GetMyDLinkSettingsResponse_xml=NULL");
            puVar1 = 0;
          }
          else {
            mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
            iVar4 = mxmlNewElement(iVar3,"GetMyDLinkSettingsResult");
            if (iVar4 == 0) {
              mxmlDelete(iVar2);
              puts("GetMyDLinkSettingsResult_xml=NULL");
              puVar1 = 0;
            }
            else {
              mxmlNewText(iVar4,0,"O");
              iVar4 = mxmlNewElement(iVar3,"Enabled");
              if (iVar4 == 0) {
                mxmlDelete(iVar2);
                puts("Enabled_xml=NULL");
                puVar1 = 0;
              }
              else {
                mxmlNewText(iVar4,0,"false");
                iVar4 = mxmlNewElement(iVar3,"Email");
                if (iVar4 == 0) {
                  mxmlDelete(iVar2);
                  puts("Email_xml=NULL");
                  puVar1 = 0;
                }
                else {
                  mxmlNewText(iVar4,0,0);
                  iVar4 = mxmlNewElement(iVar3,"Fassword");
                  if (iVar4 == 0) {
                    mxmlDelete(iVar2);
                    puts("Fassword_xml=NULL");
                    puVar1 = 0;
                  }
                  else {
                    mxmlNewText(iVar4,0,0);
                    iVar4 = mxmlNewElement(iVar3,"LastName");
                    if (iVar4 == 0) {
                      mxmlDelete(iVar2);
                      puts("LastName_xml=NULL");
                      puVar1 = 0;
                    }
                    else {
                      mxmlNewText(iVar4,0,0);
                      iVar4 = mxmlNewElement(iVar3,"FirstName");
                      if (iVar4 == 0) {
                        mxmlDelete(iVar2);
                        puts("FirstName_xml=NULL");
                        puVar1 = 0;
                      }
                      else {
                        mxmlNewText(iVar4,0,0);
                        iVar3 = mxmlNewElement(iVar3,"AccountStatus");
                        if (iVar3 == 0) {
                          mxmlDelete(iVar2);
                          puts("AccountStatus_xml=NULL");
                          puVar1 = 0;
                        }
                        else {
                          mxmlNewText(iVar3,0,"false");
                          __ptr = mxmlSaveAllocString(iVar2,0);
                          if (__ptr == 0) {
                            puts("retstring=NULL");
                          }
                          else {
                            FUN_0041ed70("",200,__ptr,"");
                            free(__ptr);
                          }
                          mxmlDelete(iVar2);
                          puVar1 = "O";
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return puVar1;
}

