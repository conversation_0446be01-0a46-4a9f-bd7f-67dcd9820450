
int FUN_00431d88(undefined4 param_1)

{
  int iVar1;
  short sVar4;
  char *pcVar2;
  uint uVar3;
  undefined4 uVar5;
  int iVar6;
  uint uVar7;
  code *pcVar8;
  uint local_598;
  undefined auStack_594 [4];
  uint local_590;
  uint local_58c;
  undefined auStack_588 [8];
  uint local_580;
  in_addr_t local_57c;
  in_addr_t local_578;
  uint local_574;
  undefined auStack_570 [4];
  undefined auStack_56c [16];
  undefined auStack_55c [16];
  undefined auStack_54c [16];
  undefined auStack_53c [16];
  undefined auStack_52c [4];
  undefined auStack_528 [4];
  undefined auStack_524 [4];
  char ac<PERSON>tack_520 [120];
  undefined *local_4a8;
  undefined4 local_4a4;
  undefined *local_4a0;
  undefined4 local_49c;
  undefined *local_498;
  undefined4 local_494;
  undefined *local_490;
  undefined4 local_48c;
  undefined *local_488;
  undefined4 local_484;
  undefined *local_480;
  undefined4 local_47c;
  undefined *local_478;
  undefined4 local_474;
  undefined *local_470;
  undefined4 local_46c;
  undefined *local_468;
  undefined4 local_464;
  undefined *local_460;
  undefined4 local_45c;
  undefined *local_458;
  undefined4 local_454;
  undefined *local_450;
  undefined4 local_44c;
  undefined *local_448;
  undefined4 local_444;
  undefined *local_440;
  undefined4 local_43c;
  undefined *local_438;
  undefined4 local_434;
  undefined *local_430;
  undefined4 local_42c;
  undefined *local_428;
  undefined4 local_424;
  undefined *local_420;
  undefined4 local_41c;
  undefined *local_418;
  undefined4 local_414;
  undefined *local_410;
  undefined4 local_40c;
  undefined *local_408;
  undefined4 local_404;
  undefined *local_400;
  undefined4 local_3fc;
  undefined *local_3f8;
  undefined4 local_3f4;
  undefined *local_3f0;
  undefined4 local_3ec;
  undefined *local_3e8;
  undefined4 local_3e4;
  undefined *local_3e0;
  undefined4 local_3dc;
  undefined *local_3d8;
  undefined4 local_3d4;
  undefined *local_3d0;
  undefined4 local_3cc;
  undefined *local_3c8;
  undefined4 local_3c4;
  undefined *local_3c0;
  undefined4 local_3bc;
  undefined *local_3b8;
  undefined4 local_3b4;
  undefined *local_3b0;
  undefined4 local_3ac;
  undefined *local_3a8;
  undefined4 local_3a4;
  undefined *local_3a0;
  undefined4 local_39c;
  undefined *local_398;
  undefined4 local_394;
  undefined *local_390;
  undefined4 local_38c;
  undefined *local_388;
  undefined4 local_384;
  undefined *local_380;
  undefined4 local_37c;
  undefined *local_378;
  undefined4 local_374;
  undefined4 local_370;
  undefined auStack_368 [119];
  undefined local_2f1;
  undefined auStack_2f0 [119];
  undefined local_279;
  int local_224;
  int local_220;
  undefined4 local_21c;
  uint local_204;
  uint local_200;
  uint local_1fc;
  uint local_1f8;
  uint local_1f4;
  uint local_1f0;
  uint local_1e4;
  int local_1dc;
  undefined auStack_1d8 [4];
  undefined auStack_1d4 [4];
  undefined auStack_1d0 [4];
  undefined auStack_1cc [4];
  undefined auStack_1c8 [4];
  undefined auStack_1c4 [4];
  undefined auStack_1c0 [4];
  undefined auStack_1bc [120];
  undefined auStack_144 [120];
  undefined auStack_cc [4];
  undefined auStack_c8 [32];
  undefined auStack_a8 [32];
  undefined auStack_88 [4];
  undefined auStack_84 [16];
  undefined auStack_74 [4];
  undefined auStack_70 [4];
  undefined auStack_6c [16];
  undefined auStack_5c [16];
  undefined auStack_4c [4];
  undefined auStack_48 [4];
  undefined auStack_44 [4];
  undefined auStack_40 [4];
  undefined auStack_3c [4];
  undefined auStack_38 [4];
  undefined auStack_34 [4];
  undefined auStack_30 [4];
  undefined auStack_2c [4];
  undefined auStack_28 [4];
  undefined auStack_24 [4];
  undefined auStack_20 [4];
  undefined auStack_1c [4];
  
  local_4a0 = auStack_1d4;
  local_498 = auStack_1d0;
  local_490 = auStack_1cc;
  local_488 = auStack_1c8;
  local_480 = auStack_1c4;
  local_478 = auStack_1c0;
  local_470 = auStack_1bc;
  local_468 = auStack_144;
  local_460 = auStack_cc;
  local_458 = auStack_c8;
  local_450 = auStack_a8;
  local_448 = auStack_88;
  local_440 = auStack_84;
  local_438 = auStack_74;
  local_430 = auStack_70;
  local_428 = auStack_6c;
  local_420 = auStack_5c;
  local_418 = auStack_4c;
  local_410 = auStack_48;
  local_408 = auStack_44;
  local_400 = auStack_40;
  local_3f8 = auStack_3c;
  local_3f0 = auStack_38;
  local_3e8 = auStack_34;
  local_3e0 = auStack_30;
  local_3d8 = auStack_2c;
  local_3d0 = auStack_28;
  local_3c8 = auStack_24;
  local_3c0 = auStack_570;
  local_3b8 = auStack_56c;
  local_3b0 = auStack_55c;
  local_590 = 0;
  local_58c = 0;
  local_3a8 = auStack_54c;
  local_3a0 = auStack_53c;
  local_398 = auStack_52c;
  local_390 = auStack_528;
  local_388 = auStack_524;
  local_380 = auStack_20;
  local_378 = auStack_1c;
  local_43c = 16;
  local_424 = 16;
  local_41c = 16;
  local_464 = "x";
  local_44c = " ";
  local_46c = "x";
  local_454 = " ";
  local_370 = 0;
  local_4a4 = 0;
  local_49c = 0;
  local_494 = 0;
  local_48c = 0;
  local_484 = 0;
  local_47c = 0;
  local_474 = 0;
  local_45c = 0;
  local_444 = 0;
  local_434 = 0;
  local_42c = 0;
  local_414 = 0;
  local_40c = 0;
  local_404 = 0;
  local_3fc = 0;
  local_3f4 = 0;
  local_3ec = 0;
  local_3e4 = 0;
  local_3dc = 0;
  local_39c = 16;
  local_3b4 = 16;
  local_3ac = 16;
  local_3a4 = 16;
  local_3d4 = 0;
  local_3cc = 0;
  local_3c4 = 0;
  local_3bc = 0;
  local_394 = 0;
  local_38c = 0;
  local_384 = 0;
  local_37c = 0;
  local_374 = 0;
  local_4a8 = auStack_1d8;
  memset(auStack_368,0,400);
  local_574 = 0;
  local_580 = 0;
  local_57c = 0;
  local_578 = 0;
  memset(auStack_1d8,0,448);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar4 = HttpDenyPage(param_1);
    goto LAB_004330e8;
  }
  pcVar2 = httpGetEnv(param_1,"wan");
  uVar7 = 0;
  if (((pcVar2 != 0) && (uVar7 = atoi(pcVar2), uVar7 < 0)) ||
     (uVar3 = getMaxWanPortNumber(), uVar3 <= uVar7)) {
    uVar7 = 0;
  }
  "" = uVar7;
  swGetPppoeCfg(auStack_368);
  swGetPppoePlusCfg(&local_580);
  iVar1 = httpGetEnv(param_1,"Connect");
  if ((iVar1 == 0) && (iVar1 = httpGetEnv(param_1,"Save"), iVar1 == 0)) {
    iVar1 = httpGetEnv(param_1,"Disconnect");
    if (iVar1 == 0) {
      iVar1 = httpGetEnv(param_1,"RenewIp");
      if (iVar1 != 0) {
        swDhcpcStart(uVar7);
      }
      iVar1 = httpGetEnv(param_1,"ReleaseIp");
      if (iVar1 != 0) {
        pcVar8 = swDhcpcRelease;
        uVar3 = uVar7;
        goto LAB_00432744;
      }
    }
    else {
      swPppoeLinkDownReq();
      pcVar8 = taskDelay;
      uVar3 = "Z";
LAB_00432744:
      (*pcVar8)(uVar3);
    }
    swGetPppoeCfg(auStack_368);
    swPppoeLinkStateGet(auStack_594);
    swGetPppoePlusCfg(&local_580);
    if ((local_224 == 0) && (local_220 == 0)) {
      local_21c = 1;
    }
    local_598 = 0;
    pageParaSet(&local_4a8,&local_598,5);
    pageParaSet(&local_4a8,&local_598,9);
    pageParaSet(&local_4a8,&local_598,12);
    pageParaSet(&local_4a8,&local_598,14);
    pageParaSet(&local_4a8,&local_598,15);
    pageParaSet(&local_4a8,&local_598,18);
    pageParaSet(&local_4a8,&local_598,19);
    memset(acStack_520,0,"x");
    pageParaSet(&local_4a8,acStack_520,10);
    pageParaSet(&local_4a8,acStack_520,11);
    pageParaSet(&local_4a8,acStack_520,13);
    pageParaSet(&local_4a8,acStack_520,16);
    pageParaSet(&local_4a8,acStack_520,17);
    local_598 = 100;
    if (local_1dc != 1) {
      local_598 = local_1e4;
    }
    pageParaSet(&local_4a8,&local_598,27);
    local_598 = swGetSpecialDialNum();
    pageParaSet(&local_4a8,&local_598,28);
    local_598 = getMaxWanPortNumber();
    pageParaSet(&local_4a8,&local_598,0);
    local_598 = 0;
    pageParaSet(&local_4a8,&local_598,1);
    local_598 = 0;
    pageParaSet(&local_4a8,&local_598,3);
    local_598 = swGetWanType(uVar7);
    pageParaSet(&local_4a8,&local_598,4);
    local_598 = uVar7;
    pageParaSet(&local_4a8,&local_598,6);
    pageParaSet(&local_4a8,auStack_594,26);
    pageParaSet(&local_4a8,auStack_368,7);
    pageParaSet(&local_4a8,auStack_2f0,8);
    local_598 = local_204;
    pageParaSet(&local_4a8,&local_598,20);
    local_598 = local_200;
    pageParaSet(&local_4a8,&local_598,21);
    local_598 = 1;
    pageParaSet(&local_4a8,&local_598,2);
    local_598 = local_1fc;
    pageParaSet(&local_4a8,&local_598,22);
    local_598 = local_1f8;
    pageParaSet(&local_4a8,&local_598,23);
    local_598 = local_1f4;
    pageParaSet(&local_4a8,&local_598,24);
    local_598 = local_1f0;
    pageParaSet(&local_4a8,&local_598,25);
    httpGetDetectedWanType(uVar7);
    iVar1 = httpGetEnv(param_1,"detect");
    if (iVar1 != 0) {
      httpOnWanTypeDetect(uVar7);
    }
    httpPrintfWanTypeDetectInfo(param_1);
    pcVar2 = httpGetEnv(param_1,"SecType");
    if (pcVar2 != 0) {
      local_580 = atoi(pcVar2);
    }
    local_598 = local_580;
    pageParaSet(&local_4a8,&local_598,29);
    local_598 = local_57c;
    sprintf(acStack_520,"%d.%d.%d.%d",local_57c >> 24,local_57c >> 16 & 255,
            local_57c >> 8 & 255,local_57c & 255);
    pageParaSet(&local_4a8,acStack_520,30);
    local_598 = local_578;
    sprintf(acStack_520,"%d.%d.%d.%d",local_578 >> 24,local_578 >> 16 & 255,
            local_578 >> 8 & 255,local_578 & 255);
    pageParaSet(&local_4a8,acStack_520,31);
    iVar1 = swGetPppoeMode();
    if (iVar1 == 0) {
      local_590 = 0;
      local_58c = 0;
    }
    else {
      swGetDhcpcNetInfo(uVar7,&local_590,&local_58c,0,0,0);
    }
    sprintf(acStack_520,"%d.%d.%d.%d",local_590 >> 24,local_590 >> 16 & 255,
            local_590 >> 8 & 255,local_590 & 255);
    pageParaSet(&local_4a8,acStack_520," ");
    sprintf(acStack_520,"%d.%d.%d.%d",local_58c >> 24,local_58c >> 16 & 255,
            local_58c >> 8 & 255,local_58c & 255);
    pageParaSet(&local_4a8,acStack_520,"!");
    uVar3 = local_580;
    if (((local_580 == 1) && (iVar1 = swGetPppoeMode(), iVar1 != 0)) &&
       (iVar1 = swDhcpcStateIsStop(uVar7), iVar1 == 0)) {
      iVar1 = swDhcpcStateIsProcess(uVar7);
      if (iVar1 == 0) {
        iVar1 = swDhcpcStateIsSuspend(uVar7);
        if (iVar1 == 0) {
          local_598 = 0;
          pageParaSet(&local_4a8,&local_598,""");
          pageParaSet(&local_4a8,&local_598,"#");
          local_598 = uVar3;
          goto LAB_00432f98;
        }
        local_598 = uVar3;
        pageParaSet(&local_4a8,&local_598,"#");
        uVar5 = """;
      }
      else {
        local_598 = uVar3;
        pageParaSet(&local_4a8,&local_598,""");
        uVar5 = "#";
      }
      local_598 = 0;
      pageParaSet(&local_4a8,&local_598,uVar5);
    }
    else {
      local_598 = 0;
      pageParaSet(&local_4a8,&local_598,""");
      pageParaSet(&local_4a8,&local_598,"#");
    }
LAB_00432f98:
    pageParaSet(&local_4a8,&local_598,"$");
    swGetSystemMode(auStack_588);
    local_598 = 3;
    pageParaSet(&local_4a8,&local_598,"%");
    local_598 = swGetPPPoeFailReason(0);
    pageParaSet(&local_4a8,&local_598,"&");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "pppoeInf");
    iVar1 = 0;
    do {
      iVar6 = iVar1 + 1;
      pageDynParaPrintf(&local_4a8,iVar1,param_1);
      iVar1 = iVar6;
    } while (iVar6 != "'");
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintfWanTypeInfo(param_1);
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/PPPoECfgRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    iVar1 = 10;
    pcVar2 = 0;
  }
  else {
    iVar1 = httpGetEnv(param_1,"acc");
    if (iVar1 == 0) {
      pcVar8 = memset;
      iVar1 = 0;
      uVar5 = "x";
    }
    else {
      pcVar8 = strncpy;
      local_2f1 = 0;
      uVar5 = "w";
    }
    (*pcVar8)(auStack_368,iVar1,uVar5);
    iVar1 = httpGetEnv(param_1,"psw");
    if (iVar1 == 0) {
      pcVar8 = memset;
      iVar1 = 0;
      uVar5 = "x";
    }
    else {
      pcVar8 = strncpy;
      local_279 = 0;
      uVar5 = "w";
    }
    (*pcVar8)(auStack_2f0,iVar1,uVar5);
    pcVar2 = httpGetEnv(param_1,"specialDial");
    if (pcVar2 != 0) {
      uVar3 = atoi(pcVar2);
      if (uVar3 == 100) {
        local_1dc = 1;
      }
      else {
        local_1dc = 0;
        local_1e4 = uVar3;
      }
    }
    pcVar2 = httpGetEnv(param_1,"linktype");
    if (pcVar2 != 0) {
      local_204 = atoi(pcVar2);
    }
    if (local_204 != 2) {
      if (local_204 < 3) {
        if (local_204 == 1) {
          pcVar2 = httpGetEnv(param_1,"waittime");
          if (pcVar2 == 0) {
            local_200 = 0;
          }
          else {
            local_200 = atoi(pcVar2);
          }
        }
        else {
LAB_00432404:
          HttpErrorPage(param_1,0xffffffff,0,0);
        }
      }
      else if (local_204 == 3) {
        pcVar2 = httpGetEnv(param_1,"hour1");
        if (pcVar2 == 0) {
          local_1fc = 0;
        }
        else {
          local_1fc = atoi(pcVar2);
        }
        pcVar2 = httpGetEnv(param_1,"minute1");
        if (pcVar2 == 0) {
          local_1f8 = 0;
        }
        else {
          local_1f8 = atoi(pcVar2);
        }
        pcVar2 = httpGetEnv(param_1,"hour2");
        if (pcVar2 == 0) {
          local_1f4 = 0;
        }
        else {
          local_1f4 = atoi(pcVar2);
        }
        pcVar2 = httpGetEnv(param_1,"minute2");
        if (pcVar2 == 0) {
          local_1f0 = 0;
        }
        else {
          local_1f0 = atoi(pcVar2);
        }
      }
      else {
        if (local_204 != 4) goto LAB_00432404;
        pcVar2 = httpGetEnv(param_1,"waittime2");
        if (pcVar2 != 0) {
          local_200 = atoi(pcVar2);
        }
      }
    }
    pcVar2 = httpGetEnv(param_1,"SecType");
    if (pcVar2 == 0) {
      local_580 = 0;
    }
    else {
      local_580 = atoi(pcVar2);
    }
    if (local_580 == 2) {
      pcVar2 = httpGetEnv(param_1,"sta_ip");
      iVar1 = swChkDotIpAddr(pcVar2);
      if (iVar1 == 0) {
        pcVar2 = "";
        iVar1 = 0x138b;
      }
      else {
        local_57c = inet_addr(pcVar2);
        pcVar2 = httpGetEnv(param_1,"sta_mask");
        iVar1 = swChkDotIpAddr(pcVar2);
        if (iVar1 != 0) {
          local_578 = inet_addr(pcVar2);
          goto LAB_00432540;
        }
        pcVar2 = "";
        iVar1 = 0x138c;
      }
    }
    else {
LAB_00432540:
      if (local_580 != 0) {
        iVar1 = httpGetEnv(param_1,"mtu");
        if (iVar1 == 0) {
          local_574 = 0x5c8;
        }
        else {
          local_598 = getEnvToInt(param_1,"mtu",576,0x5dc);
          if (local_598 != 0) {
            local_574 = local_598;
          }
        }
      }
      iVar1 = swChkPppoeCfg(auStack_368);
      if (iVar1 == 0) {
        iVar1 = swChkPppoePlusCfg(&local_580);
        if (iVar1 == 0) {
          swSetPppoeCfg(auStack_368);
          swSetPppoePlusCfg(&local_580);
          iVar1 = httpGetEnv(param_1,"Connect");
          if (iVar1 != 0) {
            swPppoeLinkUpReq();
          }
          taskDelay(10);
          swSecLinkUpReq();
          pcVar8 = taskDelay;
          uVar3 = "<";
          goto LAB_00432744;
        }
      }
      pcVar2 = acStack_520;
      sprintf(pcVar2,"../userRpm/PPPoECfgRpm.htm?wan=%d",uVar7);
    }
  }
  sVar4 = HttpErrorPage(param_1,iVar1,pcVar2,0);
LAB_004330e8:
  return sVar4;
}

