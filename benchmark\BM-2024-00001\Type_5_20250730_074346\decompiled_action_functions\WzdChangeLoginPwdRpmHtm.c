
/* WARNING: Type propagation algorithm not settling */

int WzdChangeLoginPwdRpmHtm(undefined4 param_1)

{
  int iVar1;
  char *pcVar2;
  size_t sVar3;
  short sVar4;
  undefined1 *puVar5;
  int local_c8 [2];
  undefined auStack_c0 [16];
  undefined auStack_b0 [4];
  undefined auStack_ac [4];
  undefined auStack_a8 [4];
  undefined *local_a4;
  undefined4 local_a0;
  undefined *local_9c;
  undefined4 local_98;
  undefined *local_94;
  undefined4 local_90;
  undefined *local_8c;
  undefined4 local_88;
  undefined4 local_84;
  undefined auStack_7c [104];
  
  local_a4 = auStack_c0;
  local_9c = auStack_b0;
  local_94 = auStack_ac;
  local_8c = auStack_a8;
  local_a0 = 16;
  local_c8[0] = 0;
  local_84 = 0;
  local_98 = 0;
  local_90 = 0;
  local_88 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    iVar1 = HttpDenyPage(param_1);
    iVar1 = iVar1 << 16;
    goto LAB_0044f944;
  }
  memset(wzdLoginTmpUser,0,"b");
  iVar1 = httpGetEnv(param_1,"Next");
  if (iVar1 == 0) {
    iVar1 = httpGetEnv(param_1,"Return");
    if (iVar1 != 0) {
      iVar1 = wzdStepFindPrev(local_c8);
      if (iVar1 == 0) {
        return 2;
      }
      goto LAB_0044f92c;
    }
    memset(wzdLoginTmpUser,0,"b");
    swGetPasswordCfg(wzdLoginTmpUser);
    pageParaSet(&local_a4,0x5e49dd,0);
    local_c8[1] = 0;
    pageParaSet(&local_a4,local_c8 + 1,1);
    local_c8[1] = 1;
    pageParaSet(&local_a4,local_c8 + 1,2);
    iVar1 = HttpIsAccessFromLAN(param_1);
    if ((iVar1 == 0) && (iVar1 = getForbiddenWanUpgrade(), iVar1 != 0)) {
      local_c8[1] = 1;
    }
    else {
      local_c8[1] = 0;
    }
    pageParaSet(&local_a4,local_c8 + 1,3);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "LoginPwdInf");
    pageDynParaPrintf(&local_a4,0,param_1);
    pageDynParaPrintf(&local_a4,1,param_1);
    pageDynParaPrintf(&local_a4,2,param_1);
    pageDynParaPrintf(&local_a4,3,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/WzdChangeLoginPwdRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    iVar1 = 10;
    puVar5 = 0;
  }
  else {
    swGetPasswordCfg(auStack_7c);
    pcVar2 = httpGetEnv(param_1,"oldname");
    if (pcVar2 != 0) {
      wzdLoginTmpUser[15] = 0;
      strncpy(wzdLoginTmpUser,pcVar2,15);
    }
    pcVar2 = httpGetEnv(param_1,"newname");
    if (pcVar2 != 0) {
      wzdLoginTmpUser[64] = 0;
      strncpy(wzdLoginTmpUser + "1",pcVar2,15);
    }
    pcVar2 = httpGetEnv(param_1,"oldpassword");
    if (pcVar2 != 0) {
      sVar3 = strlen(pcVar2);
      b64_decode(0x5e49bc," ",pcVar2,sVar3);
    }
    pcVar2 = httpGetEnv(param_1,"newpassword");
    if (pcVar2 != 0) {
      sVar3 = strlen(pcVar2);
      b64_decode(0x5e49ed," ",pcVar2,sVar3);
    }
    sVar4 = swChkPasswordCfg(wzdLoginTmpUser);
    iVar1 = sVar4;
    if (iVar1 == 0) {
      iVar1 = wzdStepFindNext(local_c8);
      if (iVar1 == 0) {
        return 2;
      }
      printf("():Turn to next page id:%d!, tag:%d\r\n",*(undefined4 *)(local_c8[0] + 4),
             *(undefined4 *)(local_c8[0] + "P"));
LAB_0044f92c:
      iVar1 = GoUrl(param_1,local_c8[0] + 8);
      iVar1 = iVar1 << 16;
      goto LAB_0044f944;
    }
    puVar5 = "";
  }
  iVar1 = HttpErrorPage(param_1,iVar1,puVar5,0);
  iVar1 = iVar1 << 16;
LAB_0044f944:
  return iVar1 >> 16;
}

