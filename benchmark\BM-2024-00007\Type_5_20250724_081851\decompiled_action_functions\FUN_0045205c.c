
int FUN_0045205c(undefined4 param_1)

{
  in_addr_t *piVar1;
  ushort uVar2;
  ushort uVar3;
  int iVar4;
  short sVar12;
  char *pcVar5;
  byte *pbVar6;
  uint32_t uVar7;
  uint32_t uVar8;
  uint32_t uVar9;
  uint32_t uVar10;
  int iVar11;
  char *pcVar13;
  undefined1 *puVar14;
  undefined4 uVar15;
  byte *__nptr;
  uint uVar16;
  uint uVar17;
  uint local_140;
  int local_13c;
  int local_138;
  int local_130 [2];
  ushort local_126;
  char acStack_124 [16];
  undefined auStack_114 [4];
  undefined auStack_110 [4];
  undefined auStack_10c [4];
  undefined auStack_108 [4];
  undefined auStack_104 [4];
  in_addr_t local_100;
  undefined4 local_fc;
  ushort local_f8;
  ushort uStack_f6;
  uint local_f4;
  ushort local_f0;
  undefined2 uStack_ee;
  ushort local_ec;
  undefined2 local_ea;
  ushort local_e8;
  ushort local_e6;
  in_addr_t local_e4;
  ushort local_e0;
  uint local_dc;
  undefined auStack_d8 [4];
  undefined auStack_d4 [4];
  undefined auStack_d0 [4];
  undefined auStack_cc [4];
  undefined auStack_c8 [16];
  undefined auStack_b8 [4];
  undefined auStack_b4 [4];
  undefined *local_b0;
  undefined4 local_ac;
  undefined *local_a8;
  undefined4 local_a4;
  undefined *local_a0;
  undefined4 local_9c;
  undefined *local_98;
  undefined4 local_94;
  undefined *local_90;
  undefined4 local_8c;
  undefined4 local_88;
  undefined *local_80;
  undefined4 local_7c;
  undefined *local_78;
  undefined4 local_74;
  undefined *local_70;
  undefined4 local_6c;
  undefined *local_68;
  undefined4 local_64;
  undefined *local_60;
  undefined4 local_5c;
  undefined *local_58;
  undefined4 local_54;
  undefined *local_50;
  undefined4 local_4c;
  undefined4 local_48;
  int local_40;
  int local_3c;
  in_addr_t *local_38;
  ushort *local_34;
  char *local_30;
  
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar4 = HttpAccessPermit(param_1);
  if (iVar4 == 0) {
    sVar12 = HttpDenyPage(param_1);
LAB_00452ca8:
    iVar4 = sVar12;
  }
  else {
    iVar4 = httpGetEnv(param_1,"Add");
    if (iVar4 == 0) {
      iVar4 = httpGetEnv(param_1,"Modify");
      local_b0 = auStack_114;
      if (iVar4 == 0) {
        local_a8 = auStack_110;
        local_a0 = auStack_10c;
        local_98 = auStack_108;
        local_90 = auStack_104;
        local_80 = auStack_d8;
        local_78 = auStack_d4;
        local_70 = auStack_d0;
        local_68 = auStack_cc;
        local_60 = auStack_c8;
        local_58 = auStack_b8;
        local_50 = auStack_b4;
        local_5c = 16;
        local_88 = 0;
        local_ac = 0;
        local_a4 = 0;
        local_9c = 0;
        local_94 = 0;
        local_8c = 0;
        local_48 = 0;
        local_7c = 0;
        local_74 = 0;
        local_6c = 0;
        local_64 = 0;
        local_54 = 0;
        local_4c = 0;
        pcVar5 = httpGetEnv(param_1,"Page");
        uVar16 = 1;
        if (pcVar5 != 0) {
          uVar16 = atoi(pcVar5);
          iVar4 = getDefaultVSTblSize();
          if (((int)(((uint)(iVar4 >> 31) >> 29) + iVar4) >> 3 < uVar16) ||
             (uVar16 < 1)) {
            uVar16 = 1;
          }
        }
        local_100 = 0;
        local_fc = 0;
        uStack_f6 = 0;
        local_f8 = 0;
        local_f4 = 0;
        uStack_ee = 0;
        local_f0 = 0;
        pcVar5 = httpGetEnv(param_1,"Add");
        if (pcVar5 == 0) {
          pcVar5 = httpGetEnv(param_1,"doAll");
          if (pcVar5 != 0) {
            iVar4 = strcmp(pcVar5,"EnAll");
            uVar15 = 1;
            if (iVar4 != 0) {
              iVar4 = strcmp(pcVar5,"DisAll");
              uVar15 = 2;
              if (iVar4 != 0) {
                iVar4 = strcmp(pcVar5,"DelAll");
                uVar15 = 3;
                if (iVar4 != 0) goto LAB_004527b0;
              }
            }
            swSetVsTableAll(uVar15);
            goto LAB_004527b0;
          }
          pcVar5 = httpGetEnv(param_1,"Del");
          if (pcVar5 != 0) {
            iVar4 = atoi(pcVar5);
            swDelVsEntry(iVar4);
            goto LAB_004527b0;
          }
          iVar4 = httpGetEnv(param_1,"Save");
          if (iVar4 == 0) goto LAB_004527b0;
          uStack_ee = 0;
          local_f0 = 0;
          local_100 = 0;
          local_fc = 0;
          uStack_f6 = 0;
          local_f8 = 0;
          local_f4 = 0;
          pcVar5 = httpGetEnv(param_1,"ExPort");
          if (pcVar5 == 0) {
LAB_004524a8:
            pbVar6 = httpGetEnv(param_1,"InPort");
            if (pbVar6 == 0) {
LAB_004524f4:
              uStack_f6 = local_fc._2_2_;
              uVar2 = uStack_f6;
              uVar3 = (ushort)(local_fc >> 16);
LAB_00452550:
              local_f8 = uVar3;
              uStack_f6 = uVar2;
              pcVar5 = httpGetEnv(param_1,"Ip");
              if (pcVar5 != 0) {
                do {
                  pcVar13 = pcVar5;
                  pcVar5 = pcVar13 + 1;
                } while (*pcVar13 == ' ');
                if (pcVar13 != 0) {
                  iVar4 = swChkDotIpAddr(pcVar13);
                  if (iVar4 == 0) {
                    return -1;
                  }
                  local_100 = inet_addr(pcVar13);
                }
              }
              pcVar5 = httpGetEnv(param_1,"Protocol");
              if (pcVar5 != 0) {
                iVar4 = atoi(pcVar5);
                local_f0 = iVar4;
              }
              pcVar5 = httpGetEnv(param_1,"State");
              if (pcVar5 != 0) {
                iVar4 = atoi(pcVar5);
                local_f4 = (uint)(iVar4 == 1);
              }
              if (((local_f4 == 1) && (local_13c <= (int)local_126)) &&
                 (((int)local_126 <= local_138 && (local_130[0] == 1)))) {
                iVar4 = 0x36b7;
              }
              else {
                pcVar5 = httpGetEnv(param_1,"Changed");
                iVar4 = strcmp(pcVar5,"1");
                if (iVar4 == 0) {
                  pcVar5 = httpGetEnv(param_1,"SelIndex");
                  iVar4 = 0;
                  if (pcVar5 != 0) {
                    iVar4 = atoi(pcVar5);
                  }
                  local_ec = local_fc._0_2_;
                  local_dc = local_f4;
                  local_e8 = local_f8;
                  local_e6 = uStack_f6;
                  local_ea = local_fc._2_2_;
                  local_e4 = local_100;
                  local_e0 = local_f0;
                  iVar4 = swModifyVsEntry(iVar4,&local_ec);
                }
                else {
                  local_ec = local_fc._0_2_;
                  local_dc = local_f4;
                  local_e8 = local_f8;
                  local_ea = local_fc._2_2_;
                  local_e6 = uStack_f6;
                  local_e4 = local_100;
                  local_e0 = local_f0;
                  iVar4 = swAppendVsEntry(&local_ec);
                }
                if (iVar4 == 0) goto LAB_004527b0;
              }
            }
            else {
              do {
                __nptr = pbVar6;
                if (*__nptr == 0) goto LAB_004524f4;
                iVar4 = isspace((uint)*__nptr);
                pbVar6 = __nptr + 1;
              } while (iVar4 != 0);
              iVar11 = swChkLegalPort(__nptr);
              iVar4 = 0;
              if (iVar11 != 0) {
                iVar4 = atoi(__nptr);
              }
              uVar2 = iVar4;
              uVar3 = iVar4;
              if (iVar4 - 1U < -1) goto LAB_00452550;
LAB_00452538:
              iVar4 = 0x36b1;
            }
            goto LAB_00452c98;
          }
          do {
            pcVar13 = pcVar5;
            pcVar5 = pcVar13 + 1;
          } while (*pcVar13 == ' ');
          if (pcVar13 == 0) goto LAB_004524a8;
          iVar4 = swParsePort(&local_13c);
          if (iVar4 != 0) {
            swGetFirewallHttpCtrl(local_130);
            if (((local_13c - 1U < -1) && (local_138 < 0x10000)) && (0 < local_138)) {
              local_fc = CONCAT22(local_13c,local_138);
              goto LAB_004524a8;
            }
            goto LAB_00452538;
          }
          puVar14 = "";
          iVar4 = 0x36b2;
          uVar15 = 6;
        }
        else {
          iVar4 = strcmp("Add",pcVar5);
          if (iVar4 == 0) {
            local_f4 = 1;
          }
LAB_004527b0:
          httpPrintf(param_1,
                     "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n"
                     ,"virServerListPara");
          iVar4 = swGetVsEntry((uVar16 - 1) * 8,&local_ec);
          if ((iVar4 == 0) || (uVar17 = uVar16 - 1, uVar16 == 1)) {
            uVar17 = uVar16;
          }
          local_f4 = local_dc;
          local_100 = local_e4;
          local_38 = &local_100;
          local_3c = uVar17 << 3;
          local_40 = (uVar17 - 1) * 8;
          local_34 = &local_ec;
          local_30 = acStack_124;
          local_fc = CONCAT22(local_ec,local_ea);
          local_f8 = local_e8;
          uStack_f6 = local_e6;
          local_f0 = local_e0;
          iVar4 = 0;
          uVar16 = 0;
          while (iVar11 = getDefaultVSTblSize(), piVar1 = local_38, iVar4 < iVar11) {
            *local_38 = 0;
            piVar1[1] = 0;
            piVar1[2] = 0;
            piVar1[3] = 0;
            piVar1[4] = 0;
            iVar11 = swGetVsEntry(iVar4,local_34);
            if (iVar11 != 0) break;
            iVar4 = iVar4 + 1;
            local_f4 = local_dc;
            local_fc = CONCAT22(local_ec,local_ea);
            local_f8 = local_e8;
            uStack_f6 = local_e6;
            local_f8 = local_e8;
            local_100 = local_e4;
            local_f0 = local_e0;
            if (local_40 < iVar4) {
              if (local_3c < iVar4) break;
              local_140 = local_ec;
              pageParaSet(&local_80,&local_140,0);
              local_140 = local_fc & -1;
              pageParaSet(&local_80,&local_140,1);
              local_140 = local_f8;
              pageParaSet(&local_80,&local_140,2);
              local_140 = uStack_f6;
              pageParaSet(&local_80,&local_140,3);
              uVar16 = uVar16 + 1;
              uVar7 = ntohl(local_100);
              uVar8 = ntohl(local_100);
              uVar9 = ntohl(local_100);
              uVar10 = ntohl(local_100);
              sprintf(local_30,"%d.%d.%d.%d",uVar7 >> 24,(uVar8 & 0xff0000) >> 16,
                      (int)(uVar9 & -256) >> 8,uVar10 & 255);
              pageParaSet(&local_80,local_30,4);
              local_140 = local_f0;
              pageParaSet(&local_80,&local_140,5);
              local_140 = local_f4;
              pageParaSet(&local_80,&local_140,6);
              pageDynParaListPrintf(&local_80,param_1);
            }
          }
          httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
          local_140 = uVar17;
          pageParaSet(&local_b0,&local_140,0);
          local_140 = (uint)(local_3c < iVar4);
          pageParaSet(&local_b0,&local_140,1);
          local_140 = uVar16;
          pageParaSet(&local_b0,&local_140,2);
          local_140 = 7;
          pageParaSet(&local_b0,&local_140,3);
          local_140 = 8;
          pageParaSet(&local_b0,&local_140,4);
          httpPrintf(param_1,
                     "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n"
                     ,"virServerPara");
          pageDynParaPrintf(&local_b0,0,param_1);
          pageDynParaPrintf(&local_b0,1,param_1);
          pageDynParaPrintf(&local_b0,2,param_1);
          pageDynParaPrintf(&local_b0,3,param_1);
          pageDynParaPrintf(&local_b0,4,param_1);
          httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
          HttpWebV4Head(param_1,0,1);
          iVar4 = httpRpmFsA(param_1,"/userRpm/VirtualServerRpm.htm");
          if (iVar4 == 2) {
            return 2;
          }
          iVar4 = 10;
LAB_00452c98:
          puVar14 = 0;
          uVar15 = 0;
        }
        sVar12 = HttpErrorPage(param_1,iVar4,puVar14,uVar15);
        goto LAB_00452ca8;
      }
    }
    iVar4 = FUN_00452d0c(param_1);
  }
  return iVar4;
}

