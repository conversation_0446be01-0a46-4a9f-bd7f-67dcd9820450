
int FUN_0042d740(undefined4 param_1)

{
  int iVar1;
  short sVar3;
  char *pcVar2;
  int iVar4;
  code *pcVar5;
  int local_2a0;
  int local_29c;
  int local_298;
  undefined auStack_294 [8];
  undefined *local_28c;
  undefined4 local_288;
  undefined *local_284;
  undefined4 local_280;
  undefined *local_27c;
  undefined4 local_278;
  undefined *local_274;
  undefined4 local_270;
  undefined *local_26c;
  undefined4 local_268;
  undefined *local_264;
  undefined4 local_260;
  undefined *local_25c;
  undefined4 local_258;
  undefined *local_254;
  undefined4 local_250;
  undefined *local_24c;
  undefined4 local_248;
  undefined *local_244;
  undefined4 local_240;
  undefined *local_23c;
  undefined4 local_238;
  undefined *local_234;
  undefined4 local_230;
  undefined *local_22c;
  undefined4 local_228;
  undefined *local_224;
  undefined4 local_220;
  undefined *local_21c;
  undefined4 local_218;
  undefined *local_214;
  undefined4 local_210;
  undefined4 local_20c;
  char local_204 [24];
  undefined local_1ec;
  char local_1eb [24];
  undefined local_1d3;
  char local_1d2 [79];
  undefined local_183;
  char local_182 [79];
  undefined local_133;
  int local_130;
  int local_12c;
  int local_128;
  undefined auStack_120 [4];
  undefined auStack_11c [4];
  undefined auStack_118 [4];
  undefined auStack_114 [25];
  undefined auStack_fb [25];
  undefined auStack_e2 [80];
  undefined auStack_92 [82];
  undefined auStack_40 [4];
  undefined auStack_3c [4];
  undefined auStack_38 [4];
  undefined auStack_34 [4];
  undefined auStack_30 [4];
  undefined auStack_2c [4];
  undefined auStack_28 [4];
  undefined auStack_24 [4];
  undefined auStack_20 [12];
  
  local_28c = auStack_120;
  local_284 = auStack_11c;
  local_27c = auStack_118;
  local_274 = auStack_114;
  local_26c = auStack_fb;
  local_264 = auStack_e2;
  local_25c = auStack_92;
  local_254 = auStack_40;
  local_24c = auStack_3c;
  local_244 = auStack_38;
  local_23c = auStack_34;
  local_234 = auStack_30;
  local_22c = auStack_2c;
  local_224 = auStack_28;
  local_21c = auStack_24;
  local_214 = auStack_20;
  local_268 = 25;
  local_270 = 25;
  local_258 = "P";
  local_260 = "P";
  local_2a0 = 0;
  local_29c = 0;
  local_20c = 0;
  local_288 = 0;
  local_280 = 0;
  local_278 = 0;
  local_250 = 0;
  local_248 = 0;
  local_240 = 0;
  local_238 = 0;
  local_230 = 0;
  local_228 = 0;
  local_220 = 0;
  local_218 = 0;
  local_210 = 0;
  memset(local_204,0,228);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar3 = HttpDenyPage(param_1);
    goto LAB_0042df7c;
  }
  swGetBpaCfg(local_204);
  iVar1 = httpGetEnv(param_1,"Save");
  if ((iVar1 == 0) && (iVar1 = httpGetEnv(param_1,"Connect"), iVar1 == 0)) {
    iVar1 = httpGetEnv(param_1,"Disconnect");
    if (iVar1 != 0) {
      pcVar5 = swBpaLinkDownReq;
LAB_0042dc2c:
      (*pcVar5)(0);
    }
  }
  else {
    pcVar2 = httpGetEnv(param_1,"usr");
    if (pcVar2 == 0) {
      local_204[0] = '\0';
    }
    else {
      local_1ec = 0;
      strncpy(local_204,pcVar2,24);
    }
    pcVar2 = httpGetEnv(param_1,"pwd");
    if (pcVar2 == 0) {
      local_1eb[0] = '\0';
    }
    else {
      local_1d3 = 0;
      strncpy(local_1eb,pcVar2,24);
    }
    pcVar2 = httpGetEnv(param_1,"AuthSrv");
    if (pcVar2 == 0) {
      local_1d2[0] = '\0';
    }
    else {
      local_183 = 0;
      strncpy(local_1d2,pcVar2,"O");
    }
    pcVar2 = httpGetEnv(param_1,"AuthDomain");
    if (pcVar2 == 0) {
      local_182[0] = '\0';
    }
    else {
      local_133 = 0;
      strncpy(local_182,pcVar2,"O");
    }
    iVar1 = getMaxWanPortNumber();
    if (1 < iVar1) {
      pcVar2 = httpGetEnv(param_1,"tag_ingress");
      if (pcVar2 != 0) {
        local_298 = atoi(pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"tag_egress");
      if (pcVar2 != 0) {
        local_298 = atoi(pcVar2);
      }
    }
    pcVar2 = httpGetEnv(param_1,"mtu");
    if (pcVar2 == 0) {
      local_130 = 0;
    }
    else {
      local_130 = atoi(pcVar2);
    }
    pcVar2 = httpGetEnv(param_1,"linktype");
    if (pcVar2 == 0) {
      local_12c = 3;
    }
    else {
      local_12c = atoi(pcVar2);
    }
    if (local_12c == 1) {
      pcVar2 = httpGetEnv(param_1,"waittime");
      if (pcVar2 == 0) {
LAB_0042dbbc:
        local_128 = 15;
      }
      else {
        local_128 = atoi(pcVar2);
      }
    }
    else if (local_12c == 3) {
      pcVar2 = httpGetEnv(param_1,"waittime2");
      if (pcVar2 == 0) goto LAB_0042dbbc;
      local_128 = atoi(pcVar2);
    }
    swSetBpaCfg(local_204);
    iVar1 = httpGetEnv(param_1,"Connect");
    if (iVar1 != 0) {
      pcVar5 = swBpaLinkUpReq;
      goto LAB_0042dc2c;
    }
  }
  swGetBpaCfg(local_204);
  swBpaLinkStateGet(&local_2a0);
  local_298 = getMaxWanPortNumber();
  pageParaSet(&local_28c,&local_298,0);
  local_298 = 0;
  pageParaSet(&local_28c,&local_298,1);
  local_298 = 5;
  pageParaSet(&local_28c,&local_298,2);
  pageParaSet(&local_28c,local_204,3);
  pageParaSet(&local_28c,local_1eb,4);
  pageParaSet(&local_28c,local_1d2,5);
  pageParaSet(&local_28c,local_182,6);
  local_298 = local_130;
  pageParaSet(&local_28c,&local_298,7);
  local_298 = local_12c;
  pageParaSet(&local_28c,&local_298,8);
  local_298 = local_128;
  pageParaSet(&local_28c,&local_298,9);
  local_298 = local_2a0;
  pageParaSet(&local_28c,&local_298,11);
  if ((local_2a0 == 1) || ((local_2a0 != 0 && (local_2a0 == 2)))) {
    local_29c = local_2a0;
  }
  else {
    local_29c = 0;
  }
  pageParaSet(&local_28c,&local_29c,10);
  local_298 = 1;
  pageParaSet(&local_28c,&local_298,12);
  pageParaSet(&local_28c,&local_298,13);
  local_298 = 1;
  pageParaSet(&local_28c,&local_298,14);
  swGetSystemMode(auStack_294);
  local_298 = 3;
  pageParaSet(&local_28c,&local_298,15);
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "bpaData");
  iVar1 = 0;
  do {
    iVar4 = iVar1 + 1;
    pageDynParaPrintf(&local_28c,iVar1,param_1);
    iVar1 = iVar4;
  } while (iVar4 != 16);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  httpPrintfWanTypeInfo(param_1);
  HttpWebV4Head(param_1,0,1);
  iVar1 = httpRpmFsA(param_1,"/userRpm/BPACfgRpm.htm");
  if (iVar1 == 2) {
    return 2;
  }
  sVar3 = HttpErrorPage(param_1,10,0,0);
LAB_0042df7c:
  return sVar3;
}

