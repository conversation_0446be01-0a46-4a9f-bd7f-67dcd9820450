
int FUN_0044bb60(undefined4 param_1)

{
  int iVar1;
  short sVar2;
  undefined auStack_28 [4];
  uint local_24;
  undefined *local_20;
  undefined4 local_1c;
  undefined4 local_18;
  
  local_20 = auStack_28;
  local_24 = 0;
  local_18 = 0;
  local_1c = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar2 = HttpDenyPage(param_1);
  }
  else {
    local_24 = swGetIPv6Enable();
    iVar1 = httpGetEnv(param_1,"Save");
    if (iVar1 != 0) {
      iVar1 = httpGetEnv(param_1,"ipv6Enable");
      local_24 = (uint)(iVar1 != 0);
      swSetIPv6Enable(local_24);
    }
    pageParaSet(&local_20,&local_24,0);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "wanIpv6Enable");
    pageDynParaPrintf(&local_20,0,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintfWanIpv6TypeInfo(param_1);
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/WanIpv6EnableRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar2 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar2;
}

