
/* WARNING: Type propagation algorithm not settling */

int FUN_004588ac(undefined4 param_1)

{
  int iVar1;
  short sVar2;
  FILE *__stream;
  int iVar3;
  int iVar4;
  int local_b0 [2];
  int *local_a8;
  undefined4 local_a4;
  int *local_a0;
  undefined4 local_9c;
  undefined4 local_98;
  undefined *local_90;
  undefined4 local_8c;
  undefined *local_88;
  undefined4 local_84;
  undefined *local_80;
  undefined4 local_7c;
  undefined *local_78;
  undefined4 local_74;
  undefined4 local_70;
  undefined auStack_68 [16];
  undefined auStack_58 [16];
  undefined auStack_48 [16];
  undefined auStack_38 [20];
  
  local_b0[0] = 0;
  local_b0[1] = 0;
  memset(auStack_68,0,"@");
  local_a0 = local_b0 + 1;
  local_88 = auStack_58;
  local_80 = auStack_48;
  local_78 = auStack_38;
  local_a8 = local_b0;
  local_74 = 16;
  local_8c = 16;
  local_84 = 16;
  local_7c = 16;
  local_98 = 0;
  local_a4 = 0;
  local_9c = 0;
  local_70 = 0;
  local_90 = auStack_68;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar2 = HttpDenyPage(param_1);
  }
  else {
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "SysRouteTable");
    __stream = fopen("/proc/net/route","r");
    iVar1 = fscanf(__stream,"%*[^\n]\n");
    iVar1 = iVar1 >> 31;
    iVar3 = 0;
    iVar4 = 0;
    do {
      iVar3 = iVar3 + 1;
      if (iVar1 < 0) break;
      iVar1 = swGetProcSysRouteInfo(__stream,auStack_68);
      if (iVar1 == 0) {
        pageDynParaListPrintf(&local_90,param_1);
        iVar4 = iVar4 + 1;
      }
    } while (iVar3 != "@");
    fclose(__stream);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    local_b0[1] = 4;
    local_b0[0] = iVar4;
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "SysRouteTablePara");
    pageDynParaListPrintf(&local_a8,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/SysRouteTableRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar2 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar2;
}

