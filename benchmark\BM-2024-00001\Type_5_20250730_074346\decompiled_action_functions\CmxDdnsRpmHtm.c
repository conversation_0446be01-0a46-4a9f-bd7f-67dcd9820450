
int CmxDdnsRpmHtm(undefined4 param_1)

{
  char *pcVar1;
  int iVar2;
  char *pcVar3;
  short sVar4;
  char *__src;
  int iVar5;
  uint uVar6;
  uint local_6d8;
  char local_6d4 [12];
  undefined *local_6c8;
  undefined4 local_6c4;
  undefined *local_6c0;
  undefined4 local_6bc;
  undefined *local_6b8;
  undefined4 local_6b4;
  undefined *local_6b0;
  undefined4 local_6ac;
  undefined *local_6a8;
  undefined4 local_6a4;
  undefined *local_6a0;
  undefined4 local_69c;
  undefined *local_698;
  undefined4 local_694;
  undefined *local_690;
  undefined4 local_68c;
  undefined *local_688;
  undefined4 local_684;
  undefined *local_680;
  undefined4 local_67c;
  undefined *local_678;
  undefined4 local_674;
  undefined *local_670;
  undefined4 local_66c;
  undefined *local_668;
  undefined4 local_664;
  undefined *local_660;
  undefined4 local_65c;
  undefined4 local_658;
  uint local_648;
  char acStack_644 [63];
  undefined local_605;
  char acStack_604 [63];
  undefined local_5c5;
  char acStack_5c4 [640];
  char acStack_344 [4];
  uint local_340;
  undefined auStack_33c [4];
  undefined auStack_338 [64];
  undefined auStack_2f8 [64];
  undefined auStack_2b8 [4];
  undefined auStack_2b4 [4];
  undefined auStack_2b0 [4];
  undefined auStack_2ac [4];
  undefined auStack_2a8 [128];
  undefined auStack_228 [128];
  undefined auStack_1a8 [128];
  undefined auStack_128 [128];
  undefined auStack_a8 [128];
  undefined auStack_28 [4];
  undefined auStack_24 [4];
  
  local_6c8 = auStack_33c;
  local_6d4[8] = '\0';
  local_6d4[9] = '\0';
  local_6c0 = auStack_338;
  local_6b8 = auStack_2f8;
  local_6b0 = auStack_2b8;
  local_6a8 = auStack_2b4;
  local_6a0 = auStack_2b0;
  local_698 = auStack_2ac;
  local_690 = auStack_2a8;
  local_688 = auStack_228;
  local_680 = auStack_1a8;
  local_678 = auStack_128;
  local_670 = auStack_a8;
  local_668 = auStack_28;
  local_660 = auStack_24;
  local_66c = 128;
  local_6b4 = "@";
  local_6d4[0] = '\0';
  local_6d4[1] = '\0';
  local_6d4[2] = '\0';
  local_6d4[3] = '\0';
  local_6d4[4] = '\0';
  local_6d4[5] = '\0';
  local_6d4[6] = '\0';
  local_6d4[7] = '\0';
  local_68c = 128;
  local_684 = 128;
  local_67c = 128;
  local_674 = 128;
  local_6bc = "@";
  local_658 = 0;
  local_6c4 = 0;
  local_6ac = 0;
  local_6a4 = 0;
  local_69c = 0;
  local_694 = 0;
  local_664 = 0;
  local_65c = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  memset(&local_648,0,780);
  pcVar1 = httpGetEnv(param_1,"wan");
  if (pcVar1 == 0) {
    local_340 = 0;
  }
  else {
    local_340 = atoi(pcVar1);
  }
  uVar6 = local_340;
  iVar2 = httpGetEnv(param_1,"Save");
  if ((iVar2 == 0) && (iVar2 = httpGetEnv(param_1,"Login"), iVar2 == 0)) {
    iVar2 = httpGetEnv(param_1,"Logout");
    if (iVar2 == 0) goto LAB_004469b4;
    pcVar1 = httpGetEnv(param_1,"wan");
    uVar6 = 0;
    if (pcVar1 != 0) {
      uVar6 = atoi(pcVar1);
    }
  }
  else {
    pcVar1 = httpGetEnv(param_1,"username");
    if (pcVar1 != 0) {
      do {
        pcVar3 = pcVar1;
        pcVar1 = pcVar3 + 1;
      } while (*pcVar3 == ' ');
      local_605 = 0;
      strncpy(acStack_644,pcVar3,"?");
    }
    pcVar1 = httpGetEnv(param_1,"pwd");
    if (pcVar1 != 0) {
      do {
        pcVar3 = pcVar1;
        pcVar1 = pcVar3 + 1;
      } while (*pcVar3 == ' ');
      local_5c5 = 0;
      strncpy(acStack_604,pcVar3,"?");
    }
    pcVar1 = httpGetEnv(param_1,"EnDdns");
    if (pcVar1 == 0) {
      local_648 = 0;
    }
    else {
      iVar2 = atoi(pcVar1);
      local_648 = (uint)(iVar2 == 2);
    }
    pcVar1 = acStack_5c4;
    iVar2 = 0;
    do {
      sprintf(local_6d4,"cliUrl%d",iVar2);
      pcVar3 = httpGetEnv(param_1,local_6d4);
      if (pcVar3 != 0) {
        do {
          __src = pcVar3;
          pcVar3 = __src + 1;
        } while (*__src == ' ');
        pcVar1[127] = '\0';
        strncpy(pcVar1,__src,127);
      }
      uVar6 = local_340;
      iVar2 = iVar2 + 1;
      pcVar1 = pcVar1 + 128;
    } while (iVar2 != 5);
    swSetCmxDdnsCfg(local_340,&local_648);
    if (local_648 != 0) {
      swDdnsStart(uVar6,1);
      taskDelay(5);
      goto LAB_004469b4;
    }
  }
  swDdnsStop(uVar6,1);
LAB_004469b4:
  swGetCmxDdnsCfg(uVar6,&local_648);
  local_6d8 = 1;
  pageParaSet(&local_6c8,&local_6d8,0);
  iVar5 = 1;
  pageParaSet(&local_6c8,acStack_644,1);
  pageParaSet(&local_6c8,acStack_604,2);
  local_6d8 = local_648;
  pageParaSet(&local_6c8,&local_6d8,3);
  iVar2 = swCmxDdnsIsConnecting(uVar6);
  if (iVar2 == 0) {
    iVar5 = 2;
    iVar2 = swCmxDdnsIsConnected(uVar6);
    if (iVar2 == 0) {
      iVar2 = swCmxDdnsIsDisconnected(uVar6);
      iVar5 = 0;
      if (iVar2 != 0) {
        iVar5 = 3;
        iVar2 = swGetCmxDdnsErrCode(uVar6);
        if ((iVar2 != 4) && (iVar5 = 4, iVar2 != 9)) {
          iVar5 = 0;
        }
      }
    }
  }
  local_6d8 = ("")[iVar5];
  pageParaSet(&local_6c8,&local_6d8,4);
  local_6d8 = (uint)(iVar5 - 1U < 2);
  pageParaSet(&local_6c8,&local_6d8,5);
  local_6d8 = 5;
  pageParaSet(&local_6c8,&local_6d8,6);
  iVar2 = 7;
  pcVar1 = acStack_5c4;
  do {
    pcVar3 = pcVar1 + 128;
    pageParaSet(&local_6c8,pcVar1,iVar2);
    iVar2 = iVar2 + 1;
    pcVar1 = pcVar3;
  } while (pcVar3 != acStack_344);
  local_6d8 = uVar6;
  pageParaSet(&local_6c8,&local_6d8,12);
  local_6d8 = getMaxWanPortNumber();
  pageParaSet(&local_6c8,&local_6d8,13);
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "serInf");
  iVar2 = 0;
  do {
    iVar5 = iVar2 + 1;
    pageDynParaPrintf(&local_6c8,iVar2,param_1);
    iVar2 = iVar5;
  } while (iVar5 != 14);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  httpPrintfDdnsTypeInfo(param_1);
  HttpWebV4Head(param_1,0,1);
  iVar2 = httpRpmFsA(param_1,"/userRpm/CmxDdnsRpm.htm");
  iVar5 = 2;
  if (iVar2 != 2) {
    sVar4 = HttpErrorPage(param_1,10,0,0);
    iVar5 = sVar4;
  }
  return iVar5;
}

