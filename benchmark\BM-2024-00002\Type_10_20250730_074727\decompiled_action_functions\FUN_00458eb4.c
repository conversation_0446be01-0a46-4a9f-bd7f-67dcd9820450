
/* WARNING: Removing unreachable block (ram,FUN_00458eb4) */
/* WARNING: Type propagation algorithm not settling */
/* WARNING: Restarted to delay deadcode elimination for space: stack */

undefined4 FUN_00458eb4(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  undefined4 uVar3;
  int iVar4;
  uint32_t uVar5;
  undefined *puVar6;
  char *pcVar7;
  undefined4 uVar8;
  void *__ptr;
  int local_21c;
  int local_1c4;
  char acStack_194 [32];
  char acStack_174 [32];
  char acStack_154 [32];
  char acStack_134 [100];
  char acStack_d0 [32];
  char acStack_b0 [32];
  char acStack_90 [32];
  int local_70 [2];
  in_addr local_68;
  in_addr local_64;
  in_addr local_60;
  char local_5c;
  undefined4 local_5b;
  in_addr local_44;
  in_addr local_40 [8];
  undefined4 local_20;
  int local_1c [2];
  uint local_14;
  uint32_t local_10 [2];
  
  memset(acStack_134,0,100);
  local_20 = 0xffffffff;
  iVar2 = mxmlLoadString(0,param_1,0);
  if (iVar2 != 0) {
    uVar3 = mxmlFindElement(iVar2,iVar2,"soap:Envelope",0,0,1);
    uVar3 = mxmlFindElement(uVar3,iVar2,"SetStaticRouteSettings",0,0,1);
    uVar3 = mxmlFindElement(uVar3,iVar2,"StaticRouteClientInfoLists",0,0,1);
    apmib_get("h",local_1c);
    apmib_get(192,&local_20);
    for (local_21c = mxmlFindElement(uVar3,iVar2,"ClientInfo",0,0,1); local_21c != 0;
        local_21c = mxmlFindElement(local_21c,iVar2,"ClientInfo",0,0,1)) {
    }
    local_70[1] = 1;
    iVar4 = apmib_set(216,local_70 + 1);
    if (iVar4 == 0) {
      printf("Set enabled flag error!");
    }
    else {
      for (local_21c = mxmlFindElement(uVar3,iVar2,"ClientInfo",0,0,1); local_21c != 0;
          local_21c = mxmlFindElement(local_21c,iVar2,"ClientInfo",0,0,1)) {
        memset(&local_68,0,"$");
        uVar8 = mxmlFindElement(local_21c,iVar2,"IPAddress",0,0,1);
        pcVar7 = mxmlGetText(uVar8,0);
        if (pcVar7 != 0) {
          memset(acStack_194,0," ");
          strcpy(acStack_194,pcVar7);
          inet_aton(pcVar7,&local_68);
        }
        uVar8 = mxmlFindElement(local_21c,iVar2,"SubnetMask",0,0,1);
        pcVar7 = mxmlGetText(uVar8,0);
        if (pcVar7 != 0) {
          memset(acStack_174,0," ");
          strcpy(acStack_174,pcVar7);
          inet_aton(pcVar7,&local_64);
        }
        uVar8 = mxmlFindElement(local_21c,iVar2,"Gateway",0,0,1);
        pcVar7 = mxmlGetText(uVar8,0);
        if (pcVar7 != 0) {
          memset(acStack_154,0," ");
          strcpy(acStack_154,pcVar7);
          inet_aton(pcVar7,&local_60);
        }
        uVar8 = mxmlFindElement(local_21c,iVar2,"Interface",0,0,1);
        pcVar7 = mxmlGetText(uVar8,0);
        if (pcVar7 != 0) {
          iVar4 = strncmp(pcVar7,"wan",3);
          if (iVar4 == 0) {
            local_5c = '\x01';
          }
          else {
            local_5c = '\0';
          }
        }
        local_5b = 0;
        memcpy(&local_14,&local_68,4);
        memcpy(local_10,&local_64,4);
        uVar5 = ntohl(local_10[0]);
        local_10[0] = ~uVar5;
        if ((local_10[0] + 1 & local_10[0]) != 0) {
          printf("Invalid Netmask: %s ",acStack_174);
          goto LAB_00459e8c;
        }
        memcpy(local_10,&local_64,4);
        if ((~local_10[0] & local_14) != 0) {
          printf("Netmask doesn\'t match route address!");
          goto LAB_00459e8c;
        }
        iVar4 = apmib_get(217,local_70);
        if (iVar4 == 0) {
          printf("Get entry number error!");
          goto LAB_00459e8c;
        }
        if (10 < local_70[0] + 1) {
          printf("Cannot add new entry because table is full!");
          goto LAB_00459e8c;
        }
        for (local_1c4 = 1; local_1c4 <= local_70[0]; local_1c4 = local_1c4 + 1) {
          local_44.s_addr._0_1_ = local_1c4;
          iVar4 = apmib_get(0x80da,&local_44);
          if (iVar4 == 0) {
            printf("get entry error!");
            goto LAB_00459e8c;
          }
          if ((local_68.s_addr & local_64.s_addr) == (local_44.s_addr & local_40[0].s_addr)) {
            printf("Duplicate with entry %d!\n",local_1c4);
            break;
          }
        }
        if (local_70[0] < local_1c4) {
          apmib_set(0x200dc,&local_68);
          iVar4 = apmib_set(0x100db,&local_68);
          if (iVar4 == 0) {
            printf("Add table entry error!");
            goto LAB_00459e8c;
          }
          memset(acStack_134,0,100);
          if (local_5c == '\x01') {
            puVar6 = "eth1";
          }
          else {
            puVar6 = "br0";
          }
          sprintf(acStack_134,"route add -net %s netmask %s gw %s dev %s metric %d",acStack_194,
                  acStack_174,acStack_154,puVar6,local_5b);
          iVar4 = system(acStack_134);
          if (iVar4 != 0) {
            memcpy(acStack_134,"Set Route error\n",17);
            goto LAB_00459e8c;
          }
        }
      }
      iVar4 = apmib_get(217,local_70);
      if (iVar4 == 0) {
        printf("Get entry number error!");
      }
      else {
        for (local_1c4 = 1; local_1c4 <= local_70[0]; local_1c4 = local_1c4 + 1) {
          memset(&local_68,0,"$");
          local_68.s_addr._0_1_ = local_1c4;
          iVar4 = apmib_get(0x80da,&local_68);
          if (iVar4 == 0) {
            printf("Get table entry error!");
            goto LAB_00459e8c;
          }
          pcVar7 = inet_ntoa(local_68);
          strcpy(acStack_d0,pcVar7);
          pcVar7 = inet_ntoa(local_64);
          strcpy(acStack_b0,pcVar7);
          pcVar7 = inet_ntoa(local_60);
          strcpy(acStack_90,pcVar7);
          printf("destaddr=%s \n",acStack_d0);
          bVar1 = false;
          for (local_21c = mxmlFindElement(uVar3,iVar2,"ClientInfo",0,0,1); local_21c != 0;
              local_21c = mxmlFindElement(local_21c,iVar2,"ClientInfo",0,0,1)) {
            uVar8 = mxmlFindElement(local_21c,iVar2,"IPAddress",0,0,1);
            pcVar7 = mxmlGetText(uVar8,0);
            if (pcVar7 == 0) {
              bVar1 = false;
              break;
            }
            inet_aton(pcVar7,&local_44);
            uVar8 = mxmlFindElement(local_21c,iVar2,"SubnetMask",0,0,1);
            pcVar7 = mxmlGetText(uVar8,0);
            if (pcVar7 != 0) {
              inet_aton(pcVar7,local_40);
            }
            if ((local_68.s_addr & local_64.s_addr) == (local_44.s_addr & local_40[0].s_addr)) {
              bVar1 = true;
              break;
            }
          }
          if (!bVar1) {
            iVar4 = FUN_00458338(&local_68);
            if (iVar4 != 0) {
              if (((local_1c[0] == 3) || (local_1c[0] == 4)) || (local_1c[0] == 6)) {
                memset(acStack_134,0,100);
                if (local_5c == '\x01') {
                  sprintf(acStack_134,"route del -net %s netmask %s dev  metric %d",acStack_d0,
                          acStack_b0,local_5b);
                }
                else {
                  sprintf(acStack_134,"route del -net %s netmask %s gw %s metric %d",acStack_d0,
                          acStack_b0,acStack_90,local_5b);
                }
              }
              else {
                sprintf(acStack_134,"route del -net %s netmask %s gw %s metric %d",acStack_d0,
                        acStack_b0,acStack_90,local_5b);
              }
              system(acStack_134);
            }
            iVar4 = apmib_set(0x200dc,&local_68);
            if (iVar4 == 0) {
              printf("Delete table entry error!");
              goto LAB_00459e8c;
            }
            local_1c4 = local_1c4 + -1;
            iVar4 = apmib_get(217,local_70);
            if (iVar4 == 0) {
              printf("Get entry number error!");
              goto LAB_00459e8c;
            }
          }
        }
        iVar2 = mxmlNewXML("1.0");
        if (iVar2 == 0) {
          puts("xml is null");
          return 0;
        }
        iVar4 = mxmlNewElement(iVar2,"soap:Envelope");
        if (iVar4 == 0) {
          puts("xml is null");
          return 0;
        }
        mxmlElementSetAttr(iVar4,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar4,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar4,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar4 = mxmlNewElement(iVar4,"soap:Body");
        if (iVar4 == 0) {
          puts("body is null");
          return 0;
        }
        iVar4 = mxmlNewElement(iVar4,"SetStaticRouteSettingsResponse");
        if (iVar4 == 0) {
          puts("SetStaticRouteSettingsResponse is null");
          return 0;
        }
        mxmlElementSetAttr(iVar4,"xmlns","http://purenetworks.com/HNAP1/");
        iVar4 = mxmlNewElement(iVar4,"SetStaticRouteSettingsResult");
        if (iVar4 == 0) {
          puts("SetStaticRouteSettingsResponse is null");
          return 0;
        }
        mxmlNewText(iVar4,0,"O");
        __ptr = mxmlSaveAllocString(iVar2,0);
        printf(" retstring =%s \n",__ptr);
        FUN_0041ed70("",200,__ptr,"");
        free(__ptr);
        mxmlDelete(iVar2);
      }
    }
  }
LAB_00459e8c:
  uVar3 = apmib_update(4);
  return uVar3;
}

