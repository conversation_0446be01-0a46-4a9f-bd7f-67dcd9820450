
int FUN_004358e8(undefined4 param_1)

{
  bool bVar1;
  undefined4 uVar2;
  int iVar3;
  uint32_t uVar4;
  uint32_t uVar5;
  uint32_t uVar6;
  uint32_t uVar7;
  undefined4 *puVar8;
  code *pcVar9;
  int local_c8;
  undefined4 local_c4;
  undefined4 local_c0;
  undefined4 local_bc;
  undefined4 local_b8;
  char acStack_b4 [16];
  char acStack_a4 [16];
  undefined auStack_94 [8];
  undefined4 auStack_8c [2];
  undefined auStack_84 [100];
  
  local_b8 = 1;
  local_c8 = 0;
  local_c4 = 0;
  local_c0 = 0;
  local_bc = 0;
  swGetLanCfg(&httpLanCfgQuickSetup);
  swGetDhcpsCfg(httpLanDhcpServerQuickSetup);
  swGetPasswordCfg(auStack_84);
  memcpy(userQuickSetup,auStack_84,"b");
  iVar3 = getProductId();
  if (((iVar3 == 0x8020001) || (iVar3 = getProductId(), iVar3 == 0x8020002)) &&
     (iVar3 = httpGetEnv(param_1,"Next"), iVar3 == 0)) {
    "" = 0xffffff00;
    local_bc = 1;
    httpLanCfgQuickSetup = 0xc0a800fe;
  }
  show_rootap = 1;
  uVar4 = ntohl(httpLanCfgQuickSetup);
  uVar5 = ntohl(httpLanCfgQuickSetup);
  uVar6 = ntohl(httpLanCfgQuickSetup);
  uVar7 = ntohl(httpLanCfgQuickSetup);
  sprintf(acStack_b4,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
          (int)(uVar6 & -256) >> 8,uVar7 & 255);
  uVar4 = ntohl("");
  uVar5 = ntohl("");
  uVar6 = ntohl("");
  uVar7 = ntohl("");
  sprintf(acStack_a4,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
          (int)(uVar6 & -256) >> 8,uVar7 & 255);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar3 = HttpAccessPermit(param_1);
  if (iVar3 == 0) {
    iVar3 = HttpDenyPage(param_1);
    iVar3 = iVar3 << 16;
LAB_00436018:
    iVar3 = iVar3 >> 16;
  }
  else {
    iVar3 = httpGetEnv(param_1,"Next");
    bVar1 = false;
    if (iVar3 == 0) {
LAB_00435bec:
      iVar3 = httpGetEnv(param_1,"Return");
      if (iVar3 == 0) {
        if (!bVar1) {
          local_c0 = swIsMultiSystemMode();
          httpPrintf(param_1,
                     "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n"
                     ,"wzdLanPara");
          iVar3 = getProductId();
          if (((iVar3 == 0x8020001) || (iVar3 = getProductId(), iVar3 == 0x8020002)) ||
             (iVar3 = getProductId(), iVar3 == 0x8100002)) {
            puVar8 = &local_b8;
          }
          else {
            iVar3 = getProductId();
            if (((iVar3 == 0x8100001) || (iVar3 = getProductId(), iVar3 == 0x8411003)) ||
               ((iVar3 = getProductId(), iVar3 == 0x8411004 ||
                ((iVar3 = getProductId(), iVar3 == 0x8430002 ||
                 (iVar3 = getProductId(), iVar3 == 0x9411001)))))) {
              swGetLanCfg(auStack_94);
              puVar8 = auStack_8c;
            }
            else {
              puVar8 = &local_bc;
            }
          }
          writePageParamSet(param_1,"%d,",puVar8,0);
          writePageParamSet(param_1,""%s",",acStack_b4,1);
          iVar3 = strcmp(acStack_a4,"*********");
          if (iVar3 == 0) {
            local_c4 = 0;
            uVar2 = local_c4;
          }
          else {
            iVar3 = strcmp(acStack_a4,"255.255.0.0");
            uVar2 = 1;
            if (iVar3 != 0) {
              iVar3 = strcmp(acStack_a4,"255.255.255.0");
              uVar2 = 2;
              if (iVar3 != 0) {
                uVar2 = 3;
              }
            }
          }
          local_c4 = uVar2;
          writePageParamSet(param_1,"%d,",&local_c4,2);
          writePageParamSet(param_1,""%s",",acStack_a4,3);
          iVar3 = getProductId();
          if ((iVar3 == 0x8020001) || (iVar3 = getProductId(), iVar3 == 0x8020002)) {
            writePageParamSet(param_1,"%d,",&local_bc,0);
          }
          httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
          httpPrintf(param_1,
                     "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n"
                     ,"sysModePara");
          writePageParamSet(param_1,"%d,",&local_c0,0);
          httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
          httpWizardPrintStepInfo(param_1);
          HttpWebV4Head(param_1,0,0);
          iVar3 = httpRpmFsA(param_1,"/userRpm/WzdNetworkRpm.htm");
          if (iVar3 != 2) {
            iVar3 = HttpErrorPage(param_1,10,0,0);
            iVar3 = iVar3 << 16;
            goto LAB_00436018;
          }
          goto LAB_00436024;
        }
        pcVar9 = wzdStepFindNext;
      }
      else {
        pcVar9 = wzdStepFindPrev;
      }
      iVar3 = (*pcVar9)(&local_c8);
      if (iVar3 != 0) {
        iVar3 = GoUrl(param_1,local_c8 + 8);
        iVar3 = iVar3 << 16;
        goto LAB_00436018;
      }
    }
    else {
      iVar3 = FUN_004350e8(param_1,&httpLanCfgQuickSetup,httpLanDhcpServerQuickSetup,auStack_84,
                           userQuickSetup);
      bVar1 = true;
      if (iVar3 != 2) goto LAB_00435bec;
    }
LAB_00436024:
    iVar3 = 2;
  }
  return iVar3;
}

