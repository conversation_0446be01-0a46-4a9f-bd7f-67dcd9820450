
/* WARNING: Type propagation algorithm not settling */

undefined4 FUN_00466160(int param_1)

{
  undefined4 uVar1;
  int iVar2;
  int iVar3;
  char *pcVar4;
  int iVar5;
  void *__ptr;
  int local_10 [2];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetGuestWLanSettings");
    uVar1 = 0;
  }
  else {
    local_10[0] = 0;
    local_10[1] = 0;
    printf("%s========%d\n","SetGuestWLanSettings",0x1177);
    iVar2 = mxmlLoadString(0,param_1,0);
    if (iVar2 == 0) {
      uVar1 = 0;
    }
    else {
      iVar3 = mxmlFindElement(iVar2,iVar2,"SetGuestWLanSettings",0,0,1);
      if (iVar3 != 0) {
        uVar1 = mxmlFindElement(iVar2,iVar2,"LocalAccess",0,0,1);
        pcVar4 = mxmlGetText(uVar1,0);
        if (pcVar4 == 0) {
          puts("error, LocalAccess is NULL");
        }
        else {
          iVar3 = strcmp(pcVar4,"true");
          if (iVar3 == 0) {
            local_10[1] = 1;
            apmib_set(0x1b77,local_10 + 1);
          }
          else {
            local_10[1] = 0;
            apmib_set(0x1b77,local_10 + 1);
          }
        }
        uVar1 = mxmlFindElement(iVar2,iVar2,"LocalAccessTimeout",0,0,1);
        pcVar4 = mxmlGetText(uVar1,0);
        if (pcVar4 == 0) {
          puts("error, LocalAccessTimeout is NULL");
        }
        else {
          local_10[0] = atoi(pcVar4);
          apmib_set(0x1b78,local_10);
        }
      }
      iVar3 = mxmlNewXML("1.0");
      if (iVar3 == 0) {
        puts("xml=NULL");
        uVar1 = 0;
      }
      else {
        iVar5 = mxmlNewElement(iVar3,"soap:Envelope");
        if (iVar5 == 0) {
          mxmlDelete(iVar3);
          puts("soap_env=NULL");
          uVar1 = 0;
        }
        else {
          mxmlElementSetAttr(iVar5,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
          mxmlElementSetAttr(iVar5,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
          mxmlElementSetAttr(iVar5,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
          iVar5 = mxmlNewElement(iVar5,"soap:Body");
          if (iVar5 == 0) {
            mxmlDelete(iVar3);
            puts("body=NULL");
            uVar1 = 0;
          }
          else {
            iVar5 = mxmlNewElement(iVar5,"SetGuestWLanSettingsResponse");
            if (iVar5 == 0) {
              mxmlDelete(iVar3);
              puts("SetGuestWLanSettingsResponse_xml=NULL");
              uVar1 = 0;
            }
            else {
              mxmlElementSetAttr(iVar5,"xmlns","http://purenetworks.com/HNAP1/");
              iVar5 = mxmlNewElement(iVar5,"SetGuestWLanSettingsResult");
              if (iVar5 == 0) {
                mxmlDelete(iVar3);
                puts("SetGuestWLanSettingsResult_xml=NULL");
                uVar1 = 0;
              }
              else {
                mxmlNewText(iVar5,0,"O");
                __ptr = mxmlSaveAllocString(iVar3,0);
                if ("" == 0) {
                  if (__ptr == 0) {
                    puts("retstring=NULL");
                  }
                  else {
                    FUN_0041ed70("",200,__ptr,"");
                    free(__ptr);
                    mxmlDelete(iVar2);
                  }
                }
                else {
                  free(__ptr);
                  mxmlDelete(iVar2);
                }
                uVar1 = mxmlDelete(iVar3);
              }
            }
          }
        }
      }
    }
  }
  return uVar1;
}

