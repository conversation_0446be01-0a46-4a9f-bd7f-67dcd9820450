
int FUN_004327c0(undefined4 param_1)

{
  int iVar1;
  char *pcVar2;
  long lVar3;
  int iVar4;
  char *__src;
  undefined1 *puVar5;
  code *pcVar6;
  int local_c8;
  undefined4 local_c4 [2];
  undefined auStack_bc [12];
  char acStack_b0 [17];
  undefined local_9f;
  int *local_9c;
  undefined4 local_98;
  undefined *local_94;
  undefined4 local_90;
  undefined *local_8c;
  undefined4 local_88;
  undefined *local_84;
  undefined4 local_80;
  undefined4 *local_7c;
  undefined4 local_78;
  undefined4 *local_74;
  undefined4 local_70;
  undefined4 *local_6c;
  undefined4 local_68;
  undefined4 local_64;
  int local_5c;
  undefined auStack_58 [18];
  undefined auStack_46 [18];
  undefined auStack_34 [20];
  undefined4 local_20;
  undefined4 local_1c;
  undefined4 local_18 [2];
  
  local_9c = &local_5c;
  local_94 = auStack_58;
  local_8c = auStack_46;
  local_84 = auStack_34;
  local_7c = &local_20;
  local_74 = &local_1c;
  local_6c = local_18;
  local_80 = 18;
  local_90 = 18;
  local_88 = 18;
  local_c8 = 0;
  local_64 = 0;
  local_98 = 0;
  local_78 = 0;
  local_70 = 0;
  local_68 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    iVar1 = HttpDenyPage(param_1);
    iVar1 = iVar1 << 16;
    goto LAB_00432a70;
  }
  pcVar2 = httpGetEnv(param_1,"ClientId");
  iVar1 = 0;
  if (pcVar2 != 0) {
    lVar3 = atol(pcVar2);
    iVar1 = (int)lVar3;
  }
  iVar4 = httpGetEnv(param_1,"Next");
  if (iVar4 == 0) {
    iVar4 = httpGetEnv(param_1,"Return");
    if (iVar4 == 0) {
      local_5c = iVar1;
      swGetWanMac(auStack_bc);
      swMac2Str(auStack_bc,auStack_58,0);
      HttpClientMacGet(param_1,auStack_46);
      swGetWanDefaultMac(auStack_bc);
      swMac2Str(auStack_bc,auStack_34,0);
      local_20 = swIsMultiSystemMode();
      swGetSystemMode(local_c4);
      local_1c = local_c4[0];
      local_18[0] = 3;
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "wzdMacClone");
      pageDynParaListPrintf(&local_9c,param_1);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      httpWizardPrintStepInfo(param_1);
      HttpWebV4Head(param_1,0,0);
      iVar4 = httpRpmFsA(param_1,"/userRpm/WzdWanMacRpm.htm");
      iVar1 = 10;
      if (iVar4 == 2) {
        return 2;
      }
      puVar5 = 0;
      goto LAB_00432a88;
    }
    pcVar6 = wzdStepFindPrev;
  }
  else {
    swGetWanMac(auStack_bc);
    pcVar2 = httpGetEnv(param_1,"wan_mac");
    if (pcVar2 != 0) {
      do {
        __src = pcVar2;
        pcVar2 = __src + 1;
      } while (*__src == ' ');
      if ((__src != 0) && (*__src != '\0')) {
        local_9f = 0;
        strncpy(acStack_b0,__src,17);
        iVar1 = swChkStrMacAddr(acStack_b0);
        if (iVar1 == 0) {
          puVar5 = "";
          iVar1 = 0x1399;
        }
        else {
          swMacStr2Eth(acStack_b0,auStack_bc);
          iVar1 = swChkWanMac(auStack_bc);
          if (iVar1 == 0) {
            swCloneWanMac(0,auStack_bc);
            goto LAB_00432a3c;
          }
          puVar5 = "";
        }
LAB_00432a88:
        iVar1 = HttpErrorPage(param_1,iVar1,puVar5,0);
        iVar1 = iVar1 << 16;
        goto LAB_00432a70;
      }
    }
LAB_00432a3c:
    pcVar6 = wzdStepFindNext;
  }
  iVar1 = (*pcVar6)(&local_c8);
  if (iVar1 == 0) {
    return 2;
  }
  iVar1 = GoUrl(param_1,local_c8 + 8);
  iVar1 = iVar1 << 16;
LAB_00432a70:
  return iVar1 >> 16;
}

