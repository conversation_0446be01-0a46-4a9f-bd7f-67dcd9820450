
int FUN_0045723c(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  short sVar4;
  char *pcVar3;
  int iVar5;
  uint uVar6;
  uint uVar7;
  uint local_108;
  ushort local_104;
  byte local_102;
  undefined auStack_101 [64];
  byte local_c1;
  uint local_c0;
  undefined *local_bc;
  undefined4 local_b8;
  undefined4 *local_b4;
  undefined4 local_b0;
  undefined *local_ac;
  undefined4 local_a8;
  undefined4 *local_a4;
  undefined4 local_a0;
  undefined4 *local_9c;
  undefined4 local_98;
  undefined *local_94;
  undefined4 local_90;
  undefined *local_8c;
  undefined4 local_88;
  uint *local_84;
  undefined4 local_80;
  undefined4 local_7c;
  undefined auStack_74 [4];
  undefined4 local_70;
  undefined auStack_6c [64];
  undefined4 local_2c;
  undefined4 local_28;
  undefined auStack_24 [4];
  undefined auStack_20 [4];
  uint local_1c;
  
  local_bc = auStack_74;
  local_b4 = &local_70;
  local_ac = auStack_6c;
  local_a4 = &local_2c;
  local_9c = &local_28;
  local_94 = auStack_24;
  local_8c = auStack_20;
  local_84 = &local_1c;
  local_a8 = "@";
  local_7c = 0;
  local_b8 = 0;
  local_b0 = 0;
  local_a0 = 0;
  local_98 = 0;
  local_90 = 0;
  local_88 = 0;
  local_80 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    sVar4 = HttpDenyPage(param_1);
    goto LAB_0045766c;
  }
  memset(&local_104,0,"H");
  pcVar3 = httpGetEnv(param_1,"Page");
  uVar7 = 1;
  if (pcVar3 != 0) {
    uVar7 = atoi(pcVar3);
    iVar2 = getDefaultPortTriggerTblSize();
    if (((int)(((uint)(iVar2 >> 31) >> 29) + iVar2) >> 3 < uVar7) || (uVar7 < 1)) {
      uVar7 = 1;
    }
  }
  iVar2 = httpGetEnv(param_1,"Add");
  uVar6 = 0;
  if (iVar2 == 0) {
    pcVar3 = httpGetEnv(param_1,"Modify");
    if (pcVar3 == 0) {
      iVar2 = FUN_004568fc(param_1);
      return iVar2;
    }
    uVar6 = atoi(pcVar3);
    if (-1 < uVar6) {
      bVar1 = true;
      iVar2 = getDefaultPortTriggerTblSize();
      if (uVar6 < iVar2) goto LAB_00457464;
    }
    uVar6 = 0;
    bVar1 = true;
  }
  else {
    bVar1 = false;
  }
LAB_00457464:
  memset(auStack_74,0,"\");
  if (bVar1) {
    swGetPtEntry(uVar6,&local_104);
    local_108 = local_104;
    pageParaSet(&local_bc,&local_108,0);
    local_108 = local_102;
    pageParaSet(&local_bc,&local_108,1);
    pageParaSet(&local_bc,auStack_101,2);
    local_108 = local_c1;
    pageParaSet(&local_bc,&local_108,3);
    local_108 = local_c0;
    pageParaSet(&local_bc,&local_108,4);
    local_108 = 1;
    pageParaSet(&local_bc,&local_108,5);
    local_108 = uVar6;
    pageParaSet(&local_bc,&local_108,6);
    local_108 = uVar7;
    pageParaSet(&local_bc,&local_108,7);
  }
  else {
    local_70 = 1;
    local_28 = 1;
    local_2c = 1;
    local_1c = uVar7;
  }
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "specappEditInf");
  iVar2 = 0;
  do {
    iVar5 = iVar2 + 1;
    pageDynParaPrintf(&local_bc,iVar2,param_1);
    iVar2 = iVar5;
  } while (iVar5 != 8);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  HttpWebV4Head(param_1,0,1);
  iVar2 = httpRpmFsA(param_1,"/userRpm/SpecialAppAdvRpm.htm");
  if (iVar2 == 2) {
    return 2;
  }
  sVar4 = HttpErrorPage(param_1,10,0,0);
LAB_0045766c:
  return sVar4;
}

