
int FUN_00430f60(undefined4 param_1)

{
  uint uVar1;
  int iVar2;
  short sVar8;
  char *pcVar3;
  uint32_t uVar4;
  uint32_t uVar5;
  uint32_t uVar6;
  uint32_t uVar7;
  uint local_5a8;
  undefined auStack_5a4 [8];
  char acStack_59c [16];
  undefined auStack_58c [4];
  undefined auStack_588 [16];
  undefined auStack_578 [16];
  undefined auStack_568 [16];
  undefined auStack_558 [16];
  undefined auStack_548 [4];
  undefined auStack_544 [4];
  undefined auStack_540 [4];
  char acStack_53c [120];
  undefined *local_4c4;
  undefined4 local_4c0;
  undefined *local_4bc;
  undefined4 local_4b8;
  undefined *local_4b4;
  undefined4 local_4b0;
  undefined *local_4ac;
  undefined4 local_4a8;
  undefined *local_4a4;
  undefined4 local_4a0;
  undefined *local_49c;
  undefined4 local_498;
  undefined *local_494;
  undefined4 local_490;
  undefined *local_48c;
  undefined4 local_488;
  undefined *local_484;
  undefined4 local_480;
  undefined *local_47c;
  undefined4 local_478;
  undefined *local_474;
  undefined4 local_470;
  undefined *local_46c;
  undefined4 local_468;
  undefined *local_464;
  undefined4 local_460;
  undefined *local_45c;
  undefined4 local_458;
  undefined *local_454;
  undefined4 local_450;
  undefined *local_44c;
  undefined4 local_448;
  undefined *local_444;
  undefined4 local_440;
  undefined *local_43c;
  undefined4 local_438;
  undefined *local_434;
  undefined4 local_430;
  undefined *local_42c;
  undefined4 local_428;
  undefined *local_424;
  undefined4 local_420;
  undefined *local_41c;
  undefined4 local_418;
  undefined *local_414;
  undefined4 local_410;
  undefined *local_40c;
  undefined4 local_408;
  undefined *local_404;
  undefined4 local_400;
  undefined *local_3fc;
  undefined4 local_3f8;
  undefined *local_3f4;
  undefined4 local_3f0;
  undefined *local_3ec;
  undefined4 local_3e8;
  undefined *local_3e4;
  undefined4 local_3e0;
  undefined *local_3dc;
  undefined4 local_3d8;
  undefined *local_3d4;
  undefined4 local_3d0;
  undefined *local_3cc;
  undefined4 local_3c8;
  undefined *local_3c4;
  undefined4 local_3c0;
  undefined *local_3bc;
  undefined4 local_3b8;
  undefined *local_3b4;
  undefined4 local_3b0;
  undefined *local_3ac;
  undefined4 local_3a8;
  undefined *local_3a4;
  undefined4 local_3a0;
  undefined *local_39c;
  undefined4 local_398;
  undefined4 local_394;
  undefined auStack_384 [240];
  char local_294 [31];
  undefined local_275;
  char local_274 [31];
  undefined local_255;
  uint local_254;
  in_addr_t local_250;
  in_addr_t local_240;
  in_addr_t local_23c;
  uint local_238;
  uint local_234;
  uint local_230;
  uint local_208;
  uint local_204;
  undefined auStack_1f4 [4];
  undefined auStack_1f0 [4];
  undefined auStack_1ec [4];
  undefined auStack_1e8 [4];
  undefined auStack_1e4 [4];
  undefined auStack_1e0 [4];
  undefined auStack_1dc [4];
  undefined auStack_1d8 [120];
  undefined auStack_160 [120];
  undefined auStack_e8 [4];
  undefined auStack_e4 [32];
  undefined auStack_c4 [32];
  undefined auStack_a4 [4];
  undefined auStack_a0 [16];
  undefined auStack_90 [4];
  undefined auStack_8c [4];
  undefined auStack_88 [16];
  undefined auStack_78 [16];
  undefined auStack_68 [4];
  undefined auStack_64 [4];
  undefined auStack_60 [4];
  undefined auStack_5c [4];
  undefined auStack_58 [4];
  undefined auStack_54 [4];
  undefined auStack_50 [4];
  undefined auStack_4c [4];
  undefined auStack_48 [4];
  undefined auStack_44 [4];
  undefined auStack_40 [4];
  undefined auStack_3c [12];
  undefined **local_30;
  
  local_4c4 = auStack_1f4;
  local_4bc = auStack_1f0;
  local_4b4 = auStack_1ec;
  local_4ac = auStack_1e8;
  local_4a4 = auStack_1e4;
  local_49c = auStack_1e0;
  local_494 = auStack_1dc;
  local_48c = auStack_1d8;
  local_484 = auStack_160;
  local_47c = auStack_e8;
  local_474 = auStack_e4;
  local_46c = auStack_c4;
  local_464 = auStack_a4;
  local_45c = auStack_a0;
  local_454 = auStack_90;
  local_44c = auStack_8c;
  local_444 = auStack_88;
  local_43c = auStack_78;
  local_434 = auStack_68;
  local_42c = auStack_64;
  local_424 = auStack_60;
  local_41c = auStack_5c;
  local_414 = auStack_58;
  local_40c = auStack_54;
  local_404 = auStack_50;
  local_3fc = auStack_4c;
  local_3f4 = auStack_48;
  local_3ec = auStack_44;
  local_3e4 = auStack_40;
  local_3d4 = auStack_588;
  local_3cc = auStack_578;
  local_3c4 = auStack_568;
  local_3dc = auStack_58c;
  local_3bc = auStack_558;
  local_3b4 = auStack_548;
  local_3ac = auStack_544;
  local_3a4 = auStack_540;
  local_39c = auStack_3c;
  local_458 = 16;
  local_440 = 16;
  local_438 = 16;
  local_480 = "x";
  local_468 = " ";
  local_488 = "x";
  local_470 = " ";
  local_394 = 0;
  local_4c0 = 0;
  local_4b8 = 0;
  local_4b0 = 0;
  local_4a8 = 0;
  local_4a0 = 0;
  local_498 = 0;
  local_490 = 0;
  local_478 = 0;
  local_460 = 0;
  local_450 = 0;
  local_448 = 0;
  local_430 = 0;
  local_428 = 0;
  local_420 = 0;
  local_418 = 0;
  local_410 = 0;
  local_408 = 0;
  local_400 = 0;
  local_3f8 = 0;
  local_3f0 = 0;
  local_3e8 = 0;
  local_3e0 = 0;
  local_3b8 = 16;
  local_3d0 = 16;
  local_3c8 = 16;
  local_3c0 = 16;
  local_3d8 = 0;
  local_3b0 = 0;
  local_3a8 = 0;
  local_3a0 = 0;
  local_398 = 0;
  memset(local_3dc,0,"P");
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  uVar1 = "";
  if (iVar2 == 0) {
    sVar8 = HttpDenyPage(param_1);
    goto LAB_00431d48;
  }
  memset(auStack_384,0,400);
  swGetPppoeCfg(auStack_384);
  iVar2 = httpGetEnv(param_1,"Save");
  if (iVar2 == 0) {
LAB_004316dc:
    swGetPppoeCfg(auStack_384);
    if ((local_240 == 0) && (local_23c == 0)) {
      local_238 = 1;
    }
    local_5a8 = 0;
    pageParaSet(&local_4c4,&local_5a8,5);
    pageParaSet(&local_4c4,&local_5a8,20);
    pageParaSet(&local_4c4,&local_5a8,21);
    pageParaSet(&local_4c4,&local_5a8,22);
    iVar2 = 0;
    pageParaSet(&local_4c4,&local_5a8,23);
    pageParaSet(&local_4c4,&local_5a8,24);
    pageParaSet(&local_4c4,&local_5a8,25);
    pageParaSet(&local_4c4,&local_5a8,26);
    pageParaSet(&local_4c4,&local_5a8,27);
    pageParaSet(&local_4c4,&local_5a8,28);
    memset(acStack_53c,0,"x");
    pageParaSet(&local_4c4,acStack_53c,7);
    pageParaSet(&local_4c4,acStack_53c,8);
    local_5a8 = getMaxWanPortNumber();
    pageParaSet(&local_4c4,&local_5a8,0);
    local_5a8 = 0;
    pageParaSet(&local_4c4,&local_5a8,1);
    local_5a8 = 0;
    pageParaSet(&local_4c4,&local_5a8,2);
    local_5a8 = 1;
    pageParaSet(&local_4c4,&local_5a8,3);
    local_5a8 = 0;
    pageParaSet(&local_4c4,&local_5a8,4);
    local_5a8 = uVar1;
    pageParaSet(&local_4c4,&local_5a8,6);
    local_5a8 = local_208;
    pageParaSet(&local_4c4,&local_5a8,18);
    local_5a8 = local_204;
    pageParaSet(&local_4c4,&local_5a8,19);
    local_5a8 = local_230;
    pageParaSet(&local_4c4,&local_5a8,9);
    pageParaSet(&local_4c4,local_294,10);
    pageParaSet(&local_4c4,local_274,11);
    local_5a8 = local_254;
    pageParaSet(&local_4c4,&local_5a8,12);
    local_30 = &local_4c4;
    uVar4 = ntohl(local_250);
    uVar5 = ntohl(local_250);
    uVar6 = ntohl(local_250);
    uVar7 = ntohl(local_250);
    sprintf(acStack_59c,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
            (int)(uVar6 & -256) >> 8,uVar7 & 255);
    pageParaSet(&local_4c4,acStack_59c,13);
    local_5a8 = local_234;
    pageParaSet(&local_4c4,&local_5a8,14);
    local_5a8 = (uint)(local_238 == 0);
    pageParaSet(&local_4c4,&local_5a8,15);
    uVar4 = ntohl(local_240);
    uVar5 = ntohl(local_240);
    uVar6 = ntohl(local_240);
    uVar7 = ntohl(local_240);
    sprintf(acStack_59c,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
            (int)(uVar6 & -256) >> 8,uVar7 & 255);
    pageParaSet(&local_4c4,acStack_59c,16);
    uVar4 = ntohl(local_23c);
    uVar5 = ntohl(local_23c);
    uVar6 = ntohl(local_23c);
    uVar7 = ntohl(local_23c);
    sprintf(acStack_59c,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
            (int)(uVar6 & -256) >> 8,uVar7 & 255);
    pageParaSet(&local_4c4,acStack_59c,17);
    swGetSystemMode(auStack_5a4);
    local_5a8 = 3;
    pageParaSet(&local_4c4,&local_5a8,"%");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "pppoeInf");
    do {
      pageDynParaPrintf(local_30,iVar2,param_1);
      iVar2 = iVar2 + 1;
    } while (iVar2 != "'");
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar2 = httpRpmFsA(param_1,"/userRpm/PPPoECfgAdvRpm.htm");
    if (iVar2 == 2) {
      return 2;
    }
    iVar2 = 10;
LAB_00431d3c:
    pcVar3 = 0;
  }
  else {
    pcVar3 = httpGetEnv(param_1,"ServiceName");
    if (pcVar3 == 0) {
      local_294[0] = '\0';
    }
    else {
      local_275 = 0;
      strncpy(local_294,pcVar3,31);
    }
    pcVar3 = httpGetEnv(param_1,"AcName");
    if (pcVar3 == 0) {
      local_274[0] = '\0';
    }
    else {
      local_255 = 0;
      strncpy(local_274,pcVar3,31);
    }
    pcVar3 = httpGetEnv(param_1,"fixedIpEn");
    if (pcVar3 == 0) {
      local_254 = 0;
    }
    else {
      iVar2 = atoi(pcVar3);
      local_254 = (uint)(iVar2 == 2);
    }
    pcVar3 = httpGetEnv(param_1,"fixedIp");
    if (pcVar3 != 0) {
      iVar2 = swChkDotIpAddr(pcVar3);
      if (iVar2 != 0) {
        local_250 = inet_addr(pcVar3);
        goto LAB_00431384;
      }
      iVar2 = 1000;
      goto LAB_00431d3c;
    }
    local_250 = 0;
LAB_00431384:
    pcVar3 = httpGetEnv(param_1,"lcpMru");
    if (pcVar3 == 0) {
      local_230 = 0x5d4;
    }
    else {
      local_230 = atoi(pcVar3);
    }
    pcVar3 = httpGetEnv(param_1,"EchoReq");
    if (pcVar3 == 0) {
      local_234 = 0;
    }
    else {
      local_234 = atoi(pcVar3);
      printf("Detect Online Interval is %d\r\n",local_234);
      if ("x" < local_234) {
        puts("return pppoeData.ulEchoReq Error\r");
        iVar2 = 0x3ef;
        goto LAB_00431d3c;
      }
    }
    pcVar3 = httpGetEnv(param_1,"manual");
    if (pcVar3 == 0) {
      local_238 = 1;
    }
    else {
      iVar2 = atoi(pcVar3);
      local_238 = (uint)(iVar2 != 2);
    }
    if (local_238 != 0) {
LAB_004315bc:
      pcVar3 = httpGetEnv(param_1,"downBandwidth");
      if ((pcVar3 == 0) || (local_208 = atoi(pcVar3), local_208 < 0x186a1)) {
        pcVar3 = httpGetEnv(param_1,"upBandwidth");
        if ((pcVar3 == 0) || (local_204 = atoi(pcVar3), local_208 < 0x186a1)) {
          iVar2 = swChkPppoeCfg(auStack_384);
          if (iVar2 == 0) {
            swSetPppoeCfg(auStack_384);
            goto LAB_004316dc;
          }
          pcVar3 = acStack_53c;
          sprintf(pcVar3,"../userRpm/PPPoECfgAdvRpm.htm?wan=%d","");
          goto LAB_00431d40;
        }
        iVar2 = 0xfb0;
      }
      else {
        iVar2 = 0xfaf;
      }
      goto LAB_00431d3c;
    }
    pcVar3 = httpGetEnv(param_1,"dnsserver");
    if (pcVar3 == 0) {
LAB_0043152c:
      pcVar3 = httpGetEnv(param_1,"dnsserver2");
      if (pcVar3 == 0) {
        local_23c = 0;
      }
      else {
        iVar2 = swChkDotIpAddr(pcVar3);
        if (iVar2 == 0) {
          pcVar3 = acStack_53c;
          sprintf(pcVar3,"../userRpm/PPPoECfgAdvRpm.htm?wan=%d","");
          iVar2 = 0x138e;
          goto LAB_00431d40;
        }
        local_23c = inet_addr(pcVar3);
      }
      goto LAB_004315bc;
    }
    iVar2 = swChkDotIpAddr(pcVar3);
    if (iVar2 != 0) {
      local_240 = inet_addr(pcVar3);
      goto LAB_0043152c;
    }
    pcVar3 = acStack_53c;
    sprintf(pcVar3,"../userRpm/PPPoECfgAdvRpm.htm?wan=%d","");
    iVar2 = 0x138d;
  }
LAB_00431d40:
  sVar8 = HttpErrorPage(param_1,iVar2,pcVar3,0);
LAB_00431d48:
  return sVar8;
}

