
int FUN_00423dc4(undefined4 param_1)

{
  int iVar1;
  short sVar6;
  uint uVar2;
  undefined4 uVar3;
  char *pcVar4;
  uint uVar5;
  uint uVar7;
  uint uVar8;
  uint local_158;
  char local_154 [12];
  uint local_148;
  char local_144 [20];
  undefined auStack_130 [4];
  undefined auStack_12c [4];
  undefined auStack_128 [4];
  undefined auStack_124 [4];
  undefined auStack_120 [4];
  undefined *local_11c;
  undefined4 local_118;
  undefined *local_114;
  undefined4 local_110;
  undefined *local_10c;
  undefined4 local_108;
  undefined *local_104;
  undefined4 local_100;
  undefined *local_fc;
  undefined4 local_f8;
  undefined4 local_f4;
  int *local_ec;
  undefined4 local_e8;
  undefined *local_e4;
  undefined4 local_e0;
  undefined *local_dc;
  undefined4 local_d8;
  undefined *local_d4;
  undefined4 local_d0;
  undefined *local_cc;
  undefined4 local_c8;
  undefined4 local_c4;
  undefined4 local_bc;
  int local_b8;
  char acStack_b4 [24];
  undefined local_9c;
  in_addr_t local_98;
  in_addr_t local_94;
  byte local_90;
  byte local_8f;
  byte local_8e;
  byte local_8d;
  byte local_8c;
  byte local_8b;
  int local_88;
  undefined auStack_84 [25];
  undefined auStack_6b [16];
  undefined auStack_5b [16];
  undefined auStack_4b [19];
  int local_38;
  undefined4 *local_34;
  char *local_30;
  
  local_158 = 0;
  swGetLanHostsTableSize();
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar6 = HttpDenyPage(param_1);
    goto LAB_004247d8;
  }
  iVar1 = httpGetEnv(param_1,"Add");
  if ((iVar1 != 0) || (iVar1 = httpGetEnv(param_1,"Modify"), iVar1 != 0)) {
    iVar1 = FUN_0042386c(param_1);
    return iVar1;
  }
  memset(&local_bc,0,"4");
  local_11c = auStack_130;
  local_114 = auStack_12c;
  local_10c = auStack_128;
  local_104 = auStack_124;
  local_fc = auStack_120;
  local_ec = &local_88;
  local_e4 = auStack_84;
  local_dc = auStack_6b;
  local_d4 = auStack_5b;
  local_cc = auStack_4b;
  local_e0 = 25;
  local_d0 = 16;
  local_d8 = 16;
  local_c8 = 18;
  local_f4 = 0;
  local_118 = 0;
  local_110 = 0;
  local_108 = 0;
  local_100 = 0;
  local_f8 = 0;
  local_c4 = 0;
  local_e8 = 0;
  uVar2 = getEnvToInt(param_1,"Page",1,0x7fffffff);
  uVar7 = 1;
  if (uVar2 != 0xffffff80) {
    uVar7 = uVar2;
  }
  uVar3 = swGetLanHostsTableSize();
  getEnvToInt(param_1,"EntryIndex",0,uVar3);
  pcVar4 = httpGetEnv(param_1,"doAll");
  if (pcVar4 == 0) {
    pcVar4 = httpGetEnv(param_1,"Del");
    if (pcVar4 != 0) {
      iVar1 = atoi(pcVar4);
      uVar3 = swDelFilterEntry(0,(uVar7 - 1) * 8 + iVar1,1);
      goto LAB_00424344;
    }
    iVar1 = httpGetEnv(param_1,"Save");
    if (iVar1 == 0) goto LAB_00424340;
    memset(&local_bc,0,"4");
    local_bc = 1;
    iVar1 = getEnvToInt(param_1,"addr_type",0,1);
    local_b8 = iVar1;
    pcVar4 = httpGetEnv(param_1,"hosts_lists_name");
    if (pcVar4 != 0) {
      local_9c = 0;
      strncpy(acStack_b4,pcVar4,24);
    }
    if (iVar1 != 1) {
      if (iVar1 == 0) {
        uVar3 = httpGetEnv(param_1,"mac_addr");
        swMacStr2Eth(uVar3,&local_90);
      }
LAB_0042429c:
      pcVar4 = httpGetEnv(param_1,"Changed");
      iVar1 = strcmp(pcVar4,"1");
      if (iVar1 == 0) {
        pcVar4 = httpGetEnv(param_1,"SelIndex");
        iVar1 = 5;
        if (pcVar4 != 0) {
          iVar1 = atoi(pcVar4);
        }
        uVar3 = 1;
      }
      else {
        iVar1 = 0;
        uVar3 = 0;
      }
      uVar3 = swSetLanHostsEntry(&local_bc,iVar1,uVar3,1);
      goto LAB_00424344;
    }
    pcVar4 = httpGetEnv(param_1,"src_ip_start");
    if (pcVar4 == 0) {
LAB_004241a0:
      pcVar4 = httpGetEnv(param_1,"src_ip_end");
      if (pcVar4 != 0) {
        iVar1 = swChkDotIpAddr(pcVar4);
        if (iVar1 == 0) goto LAB_004241dc;
        local_154[0] = '\0';
        local_154[1] = '\0';
        local_154[2] = '\0';
        local_154[3] = '\0';
        local_148 = local_148 & 0xffffff00;
        strncpy(local_154,pcVar4,15);
        local_94 = inet_addr(local_154);
      }
      if ((local_98 == 0) || (local_94 == 0)) {
        local_98 = local_98 + local_94;
        local_94 = local_98;
      }
      goto LAB_0042429c;
    }
    iVar1 = swChkDotIpAddr(pcVar4);
    if (iVar1 != 0) {
      local_154[0] = '\0';
      local_154[1] = '\0';
      local_154[2] = '\0';
      local_154[3] = '\0';
      local_148 = local_148 & 0xffffff00;
      strncpy(local_154,pcVar4,15);
      local_98 = inet_addr(local_154);
      goto LAB_004241a0;
    }
LAB_004241dc:
    iVar1 = 0x1f43;
LAB_004247cc:
    pcVar4 = 0;
  }
  else {
    iVar1 = strcmp(pcVar4,"DelAll");
    if (iVar1 == 0) {
      uVar3 = swDelAllFilterEntry(0);
    }
    else {
LAB_00424340:
      uVar3 = 0;
    }
LAB_00424344:
    iVar1 = swFilterFindErrorNum(uVar3);
    if (iVar1 == 0) {
      memset(&local_bc,0,"4");
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "hosts_lists_data_param");
      iVar1 = swGetLanHostsEntry((uVar7 - 1) * 8,&local_bc);
      if ((iVar1 != 0) || (local_38 = uVar7 - 2, uVar2 = uVar7 - 1, uVar7 < 2)) {
        local_38 = uVar7 - 1;
        uVar2 = uVar7;
      }
      local_38 = local_38 << 3;
      local_30 = acStack_b4;
      local_34 = &local_bc;
      uVar7 = 0;
      uVar8 = 0;
      while (uVar5 = swGetLanHostsTableSize(), uVar7 < uVar5) {
        memset(local_34,0,"4");
        iVar1 = swGetLanHostsEntry(uVar7,local_34);
        if (iVar1 == 0) break;
        uVar7 = uVar7 + 1;
        if (local_38 < uVar7) {
          if ((int)(uVar2 << 3) < uVar7) break;
          local_88 = local_b8;
          pageParaSet(&local_ec,local_30,1);
          if (local_b8 == 1) {
            local_154[0] = '\0';
            local_154[1] = '\0';
            local_154[2] = '\0';
            local_154[3] = '\0';
            local_154[4] = '\0';
            local_154[5] = '\0';
            local_154[6] = '\0';
            local_154[7] = '\0';
            local_154[8] = '\0';
            local_154[9] = '\0';
            local_154[10] = '\0';
            local_154[11] = '\0';
            local_148 = 0;
            if (local_98 == 0) {
              local_154[0] = '\0';
              local_154[1] = '\0';
              local_154[2] = '\0';
              local_154[3] = '\0';
            }
            else {
              inet_ntoa_b(local_98,local_154);
            }
            pageParaSet(&local_ec,local_154,2);
            local_154[0] = '\0';
            local_154[1] = '\0';
            local_154[2] = '\0';
            local_154[3] = '\0';
            local_154[4] = '\0';
            local_154[5] = '\0';
            local_154[6] = '\0';
            local_154[7] = '\0';
            local_154[8] = '\0';
            local_154[9] = '\0';
            local_154[10] = '\0';
            local_154[11] = '\0';
            local_148 = 0;
            if (local_94 == 0) {
              local_154[0] = '\0';
              local_154[1] = '\0';
              local_154[2] = '\0';
              local_154[3] = '\0';
            }
            else {
              inet_ntoa_b(local_94,local_154);
            }
            pageParaSet(&local_ec,local_154,3);
            pcVar4 = "";
          }
          else {
            pageParaSet(&local_ec,"",2);
            pageParaSet(&local_ec,"",3);
            local_144[0] = '\0';
            local_144[1] = '\0';
            local_144[2] = '\0';
            local_144[3] = '\0';
            local_144[4] = '\0';
            local_144[5] = '\0';
            local_144[6] = '\0';
            local_144[7] = '\0';
            local_144[8] = '\0';
            local_144[9] = '\0';
            local_144[10] = '\0';
            local_144[11] = '\0';
            local_144[12] = '\0';
            local_144[13] = '\0';
            local_144[14] = '\0';
            local_144[15] = '\0';
            local_144[16] = '\0';
            local_144[17] = '\0';
            sprintf(local_144,"%02X-%02X-%02X-%02X-%02X-%02X",local_90,local_8f,
                    local_8e,local_8d,local_8c,local_8b);
            pcVar4 = local_144;
          }
          pageParaSet(&local_ec,pcVar4,4);
          uVar8 = uVar8 + 1;
          pageDynParaListPrintf(&local_ec,param_1);
        }
      }
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      local_158 = uVar2;
      pageParaSet(&local_11c,&local_158,0);
      local_158 = (uint)((int)(uVar2 << 3) < uVar7);
      pageParaSet(&local_11c,&local_158,1);
      local_158 = uVar8;
      pageParaSet(&local_11c,&local_158,2);
      local_158 = 5;
      pageParaSet(&local_11c,&local_158,3);
      local_158 = swGetFilterEntryNumCfg(0);
      pageParaSet(&local_11c,&local_158,4);
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "hosts_lists_page_param");
      pageDynParaListPrintf(&local_11c,param_1);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      HttpWebV4Head(param_1,0,1);
      iVar1 = httpRpmFsA(param_1,"/userRpm/AccessCtrlHostsListsRpm.htm");
      if (iVar1 == 2) {
        return 2;
      }
      iVar1 = 10;
      goto LAB_004247cc;
    }
    if (iVar1 != 0x714f) goto LAB_004247cc;
    pcVar4 = "../userRpm/AccessCtrlHostsListsRpm.htm";
    iVar1 = 0x714f;
  }
  sVar6 = HttpErrorPage(param_1,iVar1,pcVar4,0);
LAB_004247d8:
  return sVar6;
}

