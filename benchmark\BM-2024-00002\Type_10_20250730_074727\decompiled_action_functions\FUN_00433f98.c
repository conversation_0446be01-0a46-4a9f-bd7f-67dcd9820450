
undefined4 FUN_00433f98(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  int iVar4;
  void *__ptr;
  char *local_4c;
  undefined4 local_20;
  undefined4 local_1c;
  undefined4 local_18;
  undefined4 local_14;
  undefined4 local_10;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetCurrentInternetStatus");
  }
  else {
    local_20 = 0;
    local_1c = 0;
    local_18 = 0;
    local_14 = 0;
    local_10 = 0;
    iVar1 = mxmlLoadString(0,param_1,0);
    if (iVar1 == 0) {
      puts("ERROR!  tree is NULL");
    }
    else {
      iVar2 = mxmlFindElement(iVar1,iVar1,"soap:Envelope",0,0,1);
      if (iVar2 != 0) {
        mxmlFindElement(iVar2,iVar1,"InternetStatus",0,0,1);
        iVar2 = mxmlNewXML("1.0");
        if (iVar2 == 0) {
          printf("Create new xml erro!!!");
        }
        else {
          iVar3 = mxmlNewElement(iVar2,"SOAP-ENV:Envelope");
          if (iVar3 == 0) {
            mxmlDelete(iVar2);
            puts("soap_env=NULL");
          }
          else {
            mxmlElementSetAttr(iVar3,"xmlns:SOAP-ENV","http://schemas.xmlsoap.org/soap/envelope/");
            mxmlElementSetAttr(iVar3,"SOAP-ENV:encodingStyle",
                               "http://schemas.xmlsoap.org/soap/encoding/");
            iVar3 = mxmlNewElement(iVar3,"SOAP-ENV:Body");
            if (iVar3 == 0) {
              mxmlDelete(iVar2);
              puts("body=NULL");
            }
            else {
              iVar3 = mxmlNewElement(iVar3,"GetCurrentInternetStatusResponse");
              if (iVar3 == 0) {
                mxmlDelete(iVar2);
                puts("GetCurrentInternetStatusResponse_xml=NULL");
              }
              else {
                mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
                iVar3 = mxmlNewElement(iVar3,"GetCurrentInternetStatusResult");
                if (iVar3 == 0) {
                  mxmlDelete(iVar2);
                  puts("GetCurrentInternetStatusResult_xml=NULL");
                }
                else {
                  FUN_00426a2c("eth1",&local_20);
                  iVar4 = FUN_00426c54();
                  if ((iVar4 == 0) && (local_20 != '\0')) {
                    local_4c = "OK_CONNECTED";
                  }
                  else {
                    local_4c = "OK_NOTCONNECTED";
                  }
                  mxmlNewText(iVar3,0,local_4c);
                  __ptr = mxmlSaveAllocString(iVar2,0);
                  if (__ptr != 0) {
                    FUN_0041ed70("",200,__ptr,"");
                    free(__ptr);
                  }
                  mxmlDelete(iVar2);
                  mxmlDelete(iVar1);
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

