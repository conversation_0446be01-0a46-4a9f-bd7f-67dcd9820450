
int FUN_0045118c(undefined4 param_1)

{
  bool bVar1;
  undefined **ppuVar2;
  uint uVar3;
  uint uVar4;
  char *pcVar5;
  int iVar6;
  short sVar7;
  undefined4 uVar8;
  undefined1 *puVar9;
  short *psVar10;
  uint unaff_s2;
  uint local_1f0;
  uint local_1ec;
  uint local_1e8;
  uint local_1e4;
  uint local_1e0;
  uint local_1dc;
  uint local_1d8;
  uint local_1d4;
  uint local_1d0;
  undefined auStack_1cc [16];
  char acStack_1bc [20];
  undefined auStack_1a8 [4];
  undefined auStack_1a4 [4];
  undefined auStack_1a0 [4];
  undefined auStack_19c [4];
  undefined auStack_198 [4];
  undefined auStack_194 [4];
  undefined auStack_190 [4];
  undefined auStack_18c [4];
  undefined auStack_188 [4];
  undefined auStack_184 [4];
  undefined auStack_180 [4];
  undefined auStack_17c [4];
  ushort local_178;
  undefined *local_154;
  undefined4 local_150;
  undefined *local_14c;
  undefined4 local_148;
  undefined *local_144;
  undefined4 local_140;
  undefined *local_13c;
  undefined4 local_138;
  undefined *local_134;
  undefined4 local_130;
  undefined4 local_12c;
  undefined *local_124;
  undefined4 local_120;
  undefined *local_11c;
  undefined4 local_118;
  undefined *local_114;
  undefined4 local_110;
  undefined *local_10c;
  undefined4 local_108;
  undefined *local_104;
  undefined4 local_100;
  undefined *local_fc;
  undefined4 local_f8;
  undefined4 local_f4;
  undefined auStack_ec [4];
  undefined auStack_e8 [16];
  undefined auStack_d8 [20];
  undefined auStack_c4 [4];
  undefined auStack_c0 [4];
  undefined auStack_bc [4];
  undefined auStack_b8 [4];
  undefined auStack_b4 [4];
  undefined auStack_b0 [4];
  undefined auStack_ac [4];
  undefined auStack_a8 [4];
  undefined auStack_a4 [4];
  undefined auStack_a0 [4];
  undefined *local_9c;
  undefined4 local_98;
  undefined *local_94;
  undefined4 local_90;
  undefined *local_8c;
  undefined4 local_88;
  undefined *local_84;
  undefined *local_80 [22];
  
  local_1ec = 5;
  local_124 = auStack_194;
  local_11c = auStack_190;
  local_114 = auStack_18c;
  local_10c = auStack_188;
  local_104 = auStack_184;
  local_fc = auStack_180;
  local_154 = auStack_1a8;
  local_14c = auStack_1a4;
  local_144 = auStack_1a0;
  local_13c = auStack_19c;
  local_134 = auStack_198;
  local_9c = auStack_ec;
  local_94 = auStack_e8;
  local_8c = auStack_d8;
  local_84 = auStack_c4;
  local_80[1] = auStack_c0;
  local_80[3] = auStack_bc;
  local_1e4 = 1;
  local_1e8 = 1;
  local_1e0 = 0;
  local_f4 = 0;
  local_120 = 0;
  local_118 = 0;
  local_110 = 0;
  local_108 = 0;
  local_100 = 0;
  local_f8 = 0;
  local_12c = 0;
  local_150 = 0;
  local_148 = 0;
  local_140 = 0;
  local_138 = 0;
  local_130 = 0;
  local_80[5] = auStack_b8;
  local_80[7] = auStack_b4;
  local_80[9] = auStack_b0;
  local_80[11] = auStack_ac;
  local_80[13] = auStack_a8;
  local_80[15] = auStack_a4;
  local_80[17] = auStack_a0;
  local_90 = 16;
  local_88 = 18;
  local_80[19] = 0;
  local_98 = 0;
  ppuVar2 = local_80;
  do {
    *ppuVar2 = 0;
    ppuVar2 = ppuVar2 + 2;
  } while (ppuVar2 != local_80 + 20);
  memset(auStack_17c,0,"(");
  local_1dc = 0;
  local_1d8 = 0;
  local_1d4 = 0;
  local_1d0 = 0;
  swGetSysStatCfg(&local_1dc);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  uVar3 = getEnvToInt(param_1,"sortType",1,6);
  if (uVar3 == 0xffffff80) {
    uVar3 = 1;
    bVar1 = false;
  }
  else {
    if (5 < uVar3 - 1) {
      uVar3 = 1;
    }
    bVar1 = true;
    if (uVar3 != local_1d0) {
      local_1d0 = uVar3;
      swSetSysStatCfg(&local_1dc);
      bVar1 = true;
    }
  }
  uVar4 = getEnvToInt(param_1,"Num_per_page",5,100);
  if ((uVar4 != 0xffffff80) && (local_1ec = uVar4, "_" < uVar4 - 5)) {
    local_1ec = 5;
  }
  pcVar5 = httpGetEnv(param_1,"Goto_page");
  if (pcVar5 != 0) {
    local_1e8 = atoi(pcVar5);
  }
  iVar6 = httpGetEnv(param_1,"PrevPage");
  if (iVar6 != 0) {
    pcVar5 = httpGetEnv(param_1,"Goto_page");
    if (pcVar5 != 0) {
      iVar6 = atoi(pcVar5);
      local_1e8 = iVar6 - 1;
      iVar6 = atoi(pcVar5);
      if (0 < iVar6) goto LAB_004514d8;
    }
    local_1e8 = 1;
  }
LAB_004514d8:
  iVar6 = httpGetEnv(param_1,"NextPage");
  if (iVar6 != 0) {
    pcVar5 = httpGetEnv(param_1,"Goto_page");
    iVar6 = atoi(pcVar5);
    local_1e8 = iVar6 + 1;
  }
  iVar6 = getEnvToInt(param_1,"autoRefresh",1,2);
  if ((bVar1) && ((iVar6 != -128) != local_1d4)) {
    local_1d4 = (uint)(iVar6 != -128);
    swSetSysStatCfg(&local_1dc);
  }
  swGetFirewallDosCfg(auStack_17c);
  uVar4 = getEnvToInt(param_1,"interval",0x80000000,0x7fffffff);
  if (uVar4 != 0xffffff80) {
    if (uVar4 != local_1d8) {
      local_1d8 = uVar4;
      swSetSysStatCfg(&local_1dc);
    }
    unaff_s2 = uVar4;
    if (uVar4 != local_178) {
      if ("7" < uVar4 - 5) {
        puVar9 = "";
        uVar8 = 0xffffffff;
        goto LAB_00451f1c;
      }
      local_178 = uVar4;
      swSetFirewallDosCfg(auStack_17c);
    }
  }
  pcVar5 = httpGetEnv(param_1,"resetone");
  if (pcVar5 != 0) {
    iVar6 = atoi(pcVar5);
    sysStatResetOne(iVar6);
  }
  iVar6 = httpGetEnv(param_1,"ResetAll");
  if (iVar6 != 0) {
    sysStatResetAll();
  }
  pcVar5 = httpGetEnv(param_1,"delone");
  if (pcVar5 != 0) {
    iVar6 = atoi(pcVar5);
    sysStatDelOne(iVar6);
  }
  iVar6 = httpGetEnv(param_1,"DeleteAll");
  if (iVar6 != 0) {
    sysStatDelAll();
  }
  iVar6 = httpGetEnv(param_1,"statOn");
  if ((iVar6 != 0) && (local_1dc != 1)) {
    local_1dc = 1;
    swSetSysStatCfg(&local_1dc);
  }
  iVar6 = httpGetEnv(param_1,"statOff");
  if ((iVar6 != 0) && (local_1dc != 0)) {
    local_1dc = 0;
    swSetSysStatCfg(&local_1dc);
  }
  local_1f0 = local_1dc;
  pageParaSet(&local_124,&local_1f0,0);
  local_1f0 = local_1d8;
  pageParaSet(&local_124,&local_1f0,1);
  local_1f0 = local_1d4;
  pageParaSet(&local_124,&local_1f0,2);
  local_1f0 = local_1d0;
  pageParaSet(&local_124,&local_1f0,3);
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "statList");
  if (uVar3 == 6) {
    uVar3 = 5;
  }
  uVar3 = sysStatGetAndSort(uVar3);
  if (local_1ec <= uVar3) {
    if (local_1ec == 0) {
      trap(0x1c00);
    }
    local_1e4 = uVar3 / local_1ec + 1;
    if (uVar3 % local_1ec == 0) {
      local_1e4 = uVar3 / local_1ec;
    }
    if (local_1e4 < local_1e8) {
      local_1e8 = local_1e8 - 1;
    }
  }
  if (uVar3 < (local_1e8 - 1) * local_1ec) {
    local_1e8 = 1;
  }
  uVar4 = (local_1e8 - 1) * local_1ec;
  psVar10 = "" + uVar4 * "(";
  for (; uVar4 < uVar3; uVar4 = uVar4 + 1) {
    if (*psVar10 == 1) {
      local_1e0 = local_1e0 + 1;
      local_1f0 = uVar4;
      pageParaSet(&local_9c,&local_1f0,0);
      swIpAddr2Str(*(undefined4 *)(psVar10 + -2),auStack_1cc);
      pageParaSet(&local_9c,auStack_1cc,1);
      sprintf(acStack_1bc,"%02X-%02X-%02X-%02X-%02X-%02X",(uint)*(byte *)(psVar10 + 1),
              (uint)*(byte *)(psVar10 + 3),(uint)*(byte *)(psVar10 + 2),
              (uint)*(byte *)(psVar10 + 5),(uint)*(byte *)(psVar10 + 3),
              (uint)*(byte *)(psVar10 + 7));
      pageParaSet(&local_9c,acStack_1bc,2);
      local_1f0 = *(uint *)(psVar10 + 4);
      pageParaSet(&local_9c,&local_1f0,3);
      local_1f0 = *(uint *)(psVar10 + 6);
      pageParaSet(&local_9c,&local_1f0,4);
      local_1f0 = *(uint *)(psVar10 + 16) / unaff_s2;
      if (unaff_s2 == 0) {
        trap(0x1c00);
      }
      pageParaSet(&local_9c,&local_1f0,5);
      local_1f0 = *(uint *)(psVar10 + 18) / unaff_s2;
      if (unaff_s2 == 0) {
        trap(0x1c00);
      }
      pageParaSet(&local_9c,&local_1f0,6);
      local_1f0 = *(uint *)(psVar10 + 20) / unaff_s2;
      if (unaff_s2 == 0) {
        trap(0x1c00);
      }
      pageParaSet(&local_9c,&local_1f0,7);
      local_1f0 = *(uint *)(psVar10 + " ") / unaff_s2;
      if (unaff_s2 == 0) {
        trap(0x1c00);
      }
      pageParaSet(&local_9c,&local_1f0,8);
      local_1f0 = *(uint *)(psVar10 + 22) / unaff_s2;
      if (unaff_s2 == 0) {
        trap(0x1c00);
      }
      pageParaSet(&local_9c,&local_1f0,9);
      local_1f0 = *(uint *)(psVar10 + """) / unaff_s2;
      if (unaff_s2 == 0) {
        trap(0x1c00);
      }
      pageParaSet(&local_9c,&local_1f0,10);
      local_1f0 = *(uint *)(psVar10 + 24) / unaff_s2;
      if (unaff_s2 == 0) {
        trap(0x1c00);
      }
      pageParaSet(&local_9c,&local_1f0,11);
      local_1f0 = *(uint *)(psVar10 + "$") / unaff_s2;
      if (unaff_s2 == 0) {
        trap(0x1c00);
      }
      pageParaSet(&local_9c,&local_1f0,12);
      pageDynParaListPrintf(&local_9c,param_1);
      if (local_1ec <= local_1e0) break;
    }
    psVar10 = psVar10 + "(";
  }
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  pageParaSet(&local_124,&local_1e0,4);
  local_1f0 = 13;
  pageParaSet(&local_124,&local_1f0,5);
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "StatRulePara");
  pageDynParaPrintf(&local_124,0,param_1);
  pageDynParaPrintf(&local_124,1,param_1);
  pageDynParaPrintf(&local_124,2,param_1);
  pageDynParaPrintf(&local_124,3,param_1);
  pageDynParaPrintf(&local_124,4,param_1);
  pageDynParaPrintf(&local_124,5,param_1);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  pageParaSet(&local_154,&local_1e4,0);
  pageParaSet(&local_154,&local_1e8,1);
  pageParaSet(&local_154,&local_1ec,2);
  local_1f0 = local_1e0;
  pageParaSet(&local_154,&local_1f0,3);
  local_1f0 = (uint)(local_1e8 == local_1e4);
  pageParaSet(&local_154,&local_1f0,4);
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "PageListPara");
  pageDynParaPrintf(&local_154,0,param_1);
  pageDynParaPrintf(&local_154,1,param_1);
  pageDynParaPrintf(&local_154,2,param_1);
  pageDynParaPrintf(&local_154,3,param_1);
  pageDynParaPrintf(&local_154,4,param_1);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  HttpWebV4Head(param_1,0,1);
  iVar6 = httpRpmFsA(param_1,"/userRpm/SystemStatisticRpm.htm");
  if (iVar6 == 2) {
    return 2;
  }
  uVar8 = 10;
  puVar9 = 0;
LAB_00451f1c:
  sVar7 = HttpErrorPage(param_1,uVar8,puVar9,0);
  return sVar7;
}

