
int FUN_004275dc(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  short sVar5;
  char *pcVar3;
  undefined4 uVar4;
  char *__dest;
  size_t __n;
  long lVar6;
  char acStack_100 [12];
  uint uStack_f4;
  char acStack_f0 [20];
  undefined4 uStack_dc;
  int iStack_d8;
  char acStack_d4 [24];
  undefined uStack_bc;
  in_addr_t iStack_b8;
  in_addr_t iStack_b4;
  undefined auStack_b0 [8];
  undefined4 *puStack_a8;
  undefined4 uStack_a4;
  char *pcStack_a0;
  undefined4 uStack_9c;
  char *pcStack_98;
  undefined4 uStack_94;
  char *pcStack_90;
  undefined4 uStack_8c;
  char *pcStack_88;
  undefined4 uStack_84;
  long *plStack_80;
  undefined4 uStack_7c;
  undefined4 uStack_78;
  undefined4 uStack_70;
  char acStack_6c [24];
  undefined uStack_54;
  char acStack_53 [15];
  undefined uStack_44;
  char acStack_43 [15];
  undefined uStack_34;
  char acStack_33 [17];
  undefined uStack_22;
  long alStack_20 [3];
  
  puStack_a8 = &uStack_70;
  pcStack_a0 = acStack_6c;
  pcStack_98 = acStack_53;
  pcStack_90 = acStack_43;
  pcStack_88 = acStack_33;
  plStack_80 = alStack_20;
  uStack_9c = 25;
  uStack_8c = 16;
  uStack_94 = 16;
  uStack_84 = 18;
  uStack_78 = 0;
  uStack_a4 = 0;
  uStack_7c = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    sVar5 = HttpDenyPage(param_1);
    goto LAB_00427cd8;
  }
  pcVar3 = httpGetEnv(param_1,"ClientId");
  lVar6 = 0;
  if (pcVar3 != 0) {
    lVar6 = atol(pcVar3);
  }
  memset(&uStack_dc,0,"4");
  memset(&uStack_70,0,"T");
  if ("" == 0) {
    memset(l_lanHostEntry,0,"4");
  }
  iVar2 = httpGetEnv(param_1,"Return");
  if (iVar2 == 0) {
    iVar2 = httpGetEnv(param_1,"Next");
    if (iVar2 == 0) {
      iVar2 = swChkEntryName(0,0x5c4438);
      if (iVar2 == 0) {
        "" = 0;
      }
      if ("" == 1) {
        uStack_54 = 0;
        uStack_70 = l_lanHostEntry._4_4_;
        strncpy(acStack_6c,l_lanHostEntry + 8,24);
        pcVar3 = acStack_f0;
        if (l_lanHostEntry._4_4_ == 1) {
          pcVar3 = acStack_100;
          acStack_100[0] = '\0';
          acStack_100[1] = '\0';
          acStack_100[2] = '\0';
          acStack_100[3] = '\0';
          acStack_100[4] = '\0';
          acStack_100[5] = '\0';
          acStack_100[6] = '\0';
          acStack_100[7] = '\0';
          acStack_100[8] = '\0';
          acStack_100[9] = '\0';
          acStack_100[10] = '\0';
          acStack_100[11] = '\0';
          uStack_f4 = 0;
          inet_ntoa_b(l_lanHostEntry._36_4_,pcVar3);
          uStack_44 = 0;
          strncpy(acStack_53,pcVar3,15);
          acStack_100[0] = '\0';
          acStack_100[1] = '\0';
          acStack_100[2] = '\0';
          acStack_100[3] = '\0';
          acStack_100[4] = '\0';
          acStack_100[5] = '\0';
          acStack_100[6] = '\0';
          acStack_100[7] = '\0';
          acStack_100[8] = '\0';
          acStack_100[9] = '\0';
          acStack_100[10] = '\0';
          acStack_100[11] = '\0';
          uStack_f4 = 0;
          inet_ntoa_b(l_lanHostEntry._40_4_,pcVar3);
          uStack_34 = 0;
          __dest = acStack_43;
          __n = 15;
        }
        else {
          acStack_f0[0] = '\0';
          acStack_f0[1] = '\0';
          acStack_f0[2] = '\0';
          acStack_f0[3] = '\0';
          acStack_f0[4] = '\0';
          acStack_f0[5] = '\0';
          acStack_f0[6] = '\0';
          acStack_f0[7] = '\0';
          acStack_f0[8] = '\0';
          acStack_f0[9] = '\0';
          acStack_f0[10] = '\0';
          acStack_f0[11] = '\0';
          acStack_f0[12] = '\0';
          acStack_f0[13] = '\0';
          acStack_f0[14] = '\0';
          acStack_f0[15] = '\0';
          acStack_f0[16] = '\0';
          acStack_f0[17] = '\0';
          sprintf(pcVar3,"%02X-%02X-%02X-%02X-%02X-%02X",l_lanHostEntry[44],
                  l_lanHostEntry[45],l_lanHostEntry[46],l_lanHostEntry[47],
                  l_lanHostEntry[48],l_lanHostEntry[49]);
          uStack_22 = 0;
          __dest = acStack_33;
          __n = 17;
        }
        strncpy(__dest,pcVar3,__n);
      }
      else {
        uStack_70 = 1;
      }
      alStack_20[0] = lVar6;
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "wzdHostInf");
      pageDynParaPrintf(&puStack_a8,0,param_1);
      pageDynParaPrintf(&puStack_a8,1,param_1);
      pageDynParaPrintf(&puStack_a8,2,param_1);
      pageDynParaPrintf(&puStack_a8,3,param_1);
      pageDynParaPrintf(&puStack_a8,4,param_1);
      pageDynParaPrintf(&puStack_a8,5,param_1);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      HttpWebV4Head(param_1,0,1);
      iVar2 = httpRpmFsA(param_1,"/userRpm/WzdAccessCtrlHostAddRpm.htm");
      if (iVar2 == 2) {
        return 2;
      }
      iVar2 = 10;
    }
    else {
      uStack_dc = 1;
      pcVar3 = httpGetEnv(param_1,"hosts_lists_name");
      if (pcVar3 != 0) {
        uStack_bc = 0;
        strncpy(acStack_d4,pcVar3,24);
      }
      iStack_d8 = getEnvToInt(param_1,"addr_type",0,1);
      if (iStack_d8 == 1) {
        pcVar3 = httpGetEnv(param_1,"src_ip_start");
        if (pcVar3 != 0) {
          iVar2 = swChkDotIpAddr(pcVar3);
          if (iVar2 != 0) {
            acStack_100[0] = '\0';
            acStack_100[1] = '\0';
            acStack_100[2] = '\0';
            acStack_100[3] = '\0';
            uStack_f4 = uStack_f4 & 0xffffff00;
            strncpy(acStack_100,pcVar3,15);
            iStack_b8 = inet_addr(acStack_100);
            goto LAB_00427870;
          }
LAB_004278ac:
          iVar2 = 0x1f43;
          goto LAB_00427ccc;
        }
LAB_00427870:
        pcVar3 = httpGetEnv(param_1,"src_ip_end");
        if (pcVar3 != 0) {
          iVar2 = swChkDotIpAddr(pcVar3);
          if (iVar2 == 0) goto LAB_004278ac;
          acStack_100[0] = '\0';
          acStack_100[1] = '\0';
          acStack_100[2] = '\0';
          acStack_100[3] = '\0';
          uStack_f4 = uStack_f4 & 0xffffff00;
          strncpy(acStack_100,pcVar3,15);
          iStack_b4 = inet_addr(acStack_100);
        }
        if ((iStack_b8 == 0) || (iStack_b4 == 0)) {
          iStack_b8 = iStack_b8 + iStack_b4;
          iStack_b4 = iStack_b8;
        }
      }
      else {
        uVar4 = httpGetEnv(param_1,"mac_addr");
        swMacStr2Eth(uVar4,auStack_b0);
      }
      bVar1 = "" != 0;
      if (bVar1) {
        iVar2 = swGetFilterEntryNumCfg(0);
        iVar2 = iVar2 + -1;
      }
      else {
        iVar2 = 0;
      }
      uVar4 = swSetLanHostsEntry(&uStack_dc,iVar2,bVar1,1);
      iVar2 = swFilterFindErrorNum(uVar4);
      if (iVar2 == 0) {
        memcpy(l_lanHostEntry,&uStack_dc,"4");
        "" = 1;
        pcVar3 = "../userRpm/WzdAccessCtrlTargetAddRpm.htm";
        goto LAB_00427a38;
      }
    }
LAB_00427ccc:
    sVar5 = HttpErrorPage(param_1,iVar2,0,0);
  }
  else {
    FUN_00425a48();
    pcVar3 = "../userRpm/AccessCtrlAccessRulesRpm.htm";
LAB_00427a38:
    sVar5 = GoUrl(param_1,pcVar3);
  }
LAB_00427cd8:
  return sVar5;
}

