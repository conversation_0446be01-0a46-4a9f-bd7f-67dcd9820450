
undefined4 FUN_0043cd78(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  void *__ptr;
  char *local_324;
  char local_30c [256];
  char acStack_20c [256];
  char acStack_10c [260];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","CheckPasswdSettings");
  }
  else {
    memset(local_30c,0,256);
    local_324 = 0;
    memset(acStack_20c,0,256);
    iVar1 = mxmlLoadString(0,param_1,0);
    if (iVar1 == 0) {
      puts("ERROR!  tree is NULL");
    }
    else {
      iVar2 = mxmlFindElement(iVar1,iVar1,"CurrentPassword",0,0,1);
      if (iVar2 == 0) {
        puts("CheckPasswdSettings  state == NULL;");
      }
      else {
        iVar2 = mxmlGetText(iVar2,0);
        if (iVar2 == 0) {
          puts("strCurrentPassword is NULL!");
          local_324 = "ERROR";
        }
        else {
          iVar3 = apmib_get(183,local_30c);
          if (iVar3 == 0) {
            puts("get MIB_USER_PASSWORD is error!");
            local_324 = "ERROR";
          }
          else {
            iVar2 = FUN_0045ca40(iVar2);
            if (iVar2 == 0) {
              if (local_30c[0] == '\0') {
                local_324 = "OK";
              }
              else {
                local_324 = "ERROR";
              }
            }
            else {
              memset(acStack_10c,0,256);
              FUN_0043c100(local_30c,acStack_10c,256);
              FUN_00426654(iVar2,acStack_20c);
              iVar2 = strcmp(acStack_20c,acStack_10c);
              if (iVar2 == 0) {
                local_324 = "OK";
              }
              else {
                local_324 = "ERROR";
              }
            }
          }
        }
      }
      iVar2 = mxmlNewXML("1.0");
      if (iVar2 == 0) {
        printf("Create new xml erro!!!");
      }
      else {
        iVar3 = mxmlNewElement(iVar2,"soap:Envelope");
        if (iVar3 == 0) {
          mxmlDelete(iVar2);
          puts("soap_env=NULL");
        }
        else {
          mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
          mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
          mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
          iVar3 = mxmlNewElement(iVar3,"soap:Body");
          if (iVar3 == 0) {
            mxmlDelete(iVar2);
            puts("body=NULL");
          }
          else {
            iVar3 = mxmlNewElement(iVar3,"CheckPasswdSettingsResponse");
            if (iVar3 == 0) {
              mxmlDelete(iVar2);
              puts("CheckPasswdSettingsResponse=NULL");
            }
            else {
              mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
              iVar3 = mxmlNewElement(iVar3,"CheckPasswdSettingsResult");
              if (iVar3 == 0) {
                mxmlDelete(iVar2);
                puts("CheckPasswdSettingsResult=NULL");
              }
              else {
                mxmlNewText(iVar3,0,local_324);
                if (("" == 0) &&
                   (__ptr = mxmlSaveAllocString(iVar2,0), __ptr != 0)) {
                  FUN_0041ed70("",200,__ptr,"");
                  free(__ptr);
                }
                mxmlDelete(iVar1);
                mxmlDelete(iVar2);
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

