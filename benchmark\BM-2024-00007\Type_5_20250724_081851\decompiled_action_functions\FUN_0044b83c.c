
int FUN_0044b83c(undefined4 param_1)

{
  int iVar1;
  short sVar2;
  undefined4 local_28;
  undefined4 *local_24;
  undefined4 local_20;
  undefined4 local_1c;
  
  local_24 = &local_28;
  local_28 = 0;
  local_1c = 0;
  local_20 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar2 = HttpDenyPage(param_1);
  }
  else {
    iVar1 = httpGetEnv(param_1,"RestoreFactory");
    if (iVar1 != 0) {
      swResetUsrconf();
      sVar2 = HttpRestartRpmHtm(param_1,1);
      swReboot(2);
      return sVar2;
    }
    iVar1 = HttpIsAccessFromLAN(param_1);
    if ((iVar1 == 0) && (iVar1 = getForbiddenWanUpgrade(), iVar1 != 0)) {
      local_28 = 1;
    }
    else {
      local_28 = 0;
    }
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "restoreinfo");
    pageDynParaPrintf(&local_24,0,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/RestoreDefaultCfgRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar2 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar2;
}

