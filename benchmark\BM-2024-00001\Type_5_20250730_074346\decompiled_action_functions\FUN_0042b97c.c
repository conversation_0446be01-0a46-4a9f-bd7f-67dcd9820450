
int FUN_0042b97c(undefined4 param_1)

{
  in_addr_t iVar1;
  in_addr_t iVar2;
  int iVar3;
  char *pcVar4;
  uint32_t uVar5;
  uint32_t uVar6;
  uint32_t uVar7;
  uint32_t uVar8;
  in_addr_t iVar9;
  int iVar10;
  short sVar11;
  uint uVar12;
  undefined1 *puVar13;
  int local_2a8;
  undefined4 local_2a4;
  undefined4 local_2a0;
  byte local_29c;
  byte local_29b;
  byte local_29a;
  byte local_299;
  byte local_298;
  byte local_297;
  uint local_294 [2];
  in_addr_t local_28c;
  in_addr_t local_288;
  int local_284;
  in_addr_t local_280;
  char local_27c [36];
  undefined *local_258;
  undefined4 local_254;
  undefined *local_250;
  undefined4 local_24c;
  undefined *local_248;
  undefined4 local_244;
  undefined *local_240;
  undefined4 local_23c;
  undefined *local_238;
  undefined4 local_234;
  undefined *local_230;
  undefined4 local_22c;
  undefined *local_228;
  undefined4 local_224;
  undefined *local_220;
  undefined4 local_21c;
  undefined *local_218;
  undefined4 local_214;
  undefined4 *local_210;
  undefined4 local_20c;
  undefined4 local_208;
  undefined auStack_200 [18];
  undefined auStack_1ee [18];
  undefined auStack_1dc [4];
  undefined auStack_1d8 [16];
  undefined auStack_1c8 [4];
  undefined auStack_1c4 [16];
  undefined auStack_1b4 [4];
  undefined auStack_1b0 [4];
  undefined auStack_1ac [4];
  undefined4 local_1a8;
  undefined auStack_1a4 [4];
  undefined auStack_1a0 [376];
  
  local_2a8 = 0;
  local_2a4 = 0;
  local_2a0 = 0;
  swGetSystemMode(local_294);
  uVar12 = local_294[0];
  swWlanBasicCfgGet(0,auStack_1a4);
  local_258 = auStack_200;
  local_250 = auStack_1ee;
  local_248 = auStack_1dc;
  local_240 = auStack_1d8;
  local_238 = auStack_1c8;
  local_230 = auStack_1c4;
  local_228 = auStack_1b4;
  local_220 = auStack_1b0;
  local_218 = auStack_1ac;
  local_210 = &local_1a8;
  local_22c = 16;
  local_24c = 16;
  local_23c = 16;
  local_254 = 18;
  local_208 = 0;
  local_244 = 0;
  local_234 = 0;
  local_224 = 0;
  local_21c = 0;
  local_214 = 0;
  local_20c = 0;
  local_288 = 0;
  local_284 = 0;
  local_280 = 0;
  local_28c = 0;
  swGetLanCfg(&local_28c);
  iVar2 = local_280;
  iVar10 = local_284;
  iVar1 = local_288;
  iVar9 = local_28c;
  iVar3 = httpGetEnv(param_1,"igmpChanged");
  if (iVar3 != 0) {
    pcVar4 = httpGetEnv(param_1,"igmpEn");
    if (pcVar4 != 0) {
      iVar3 = atoi(pcVar4);
      if (iVar3 == 0) {
        local_2a0 = 0;
      }
      else {
        local_2a0 = 1;
      }
    }
    swSetIgmpCfg(&local_2a0);
  }
  iVar3 = httpGetEnv(param_1,"Save");
  if (iVar3 == 0) {
LAB_0042beb8:
    swGetLanCfg(&local_28c);
    swGetLanMac(&local_29c);
    sprintf(local_27c + 16,"%02X-%02X-%02X-%02X-%02X-%02X",local_29c,local_29b,
            local_29a,local_299,local_298,local_297);
    pageParaSet(&local_258,local_27c + 16,0);
    uVar5 = ntohl(local_28c);
    uVar6 = ntohl(local_28c);
    uVar7 = ntohl(local_28c);
    uVar8 = ntohl(local_28c);
    sprintf(local_27c,"%d.%d.%d.%d",uVar5 >> 24,uVar6 >> 16 & 255,(int)(uVar7 & -256) >> 8,
            uVar8 & 255);
    pageParaSet(&local_258,local_27c,1);
    uVar12 = 0;
    iVar9 = local_288;
    do {
      if ((iVar9 & 1) != 0) break;
      uVar12 = uVar12 + 1;
      iVar9 = local_288 >> (uVar12 & 31);
    } while (uVar12 != " ");
    uVar12 = " " - uVar12;
    local_2a8 = 3;
    if (7 < uVar12) {
      if ((uVar12 & 7) == 0) {
        local_2a8 = (uVar12 >> 3) + -1;
      }
      else {
        local_2a8 = 3;
      }
    }
    pageParaSet(&local_258,&local_2a8,2);
    local_27c[0] = '\0';
    local_27c[1] = '\0';
    local_27c[2] = '\0';
    local_27c[3] = '\0';
    local_27c[4] = '\0';
    local_27c[5] = '\0';
    local_27c[6] = '\0';
    local_27c[7] = '\0';
    local_27c[8] = '\0';
    local_27c[9] = '\0';
    local_27c[10] = '\0';
    local_27c[11] = '\0';
    local_27c[12] = '\0';
    local_27c[13] = '\0';
    local_27c[14] = '\0';
    local_27c[15] = '\0';
    uVar5 = ntohl(local_288);
    uVar6 = ntohl(local_288);
    uVar7 = ntohl(local_288);
    uVar8 = ntohl(local_288);
    sprintf(local_27c,"%d.%d.%d.%d",uVar5 >> 24,(uVar6 & 0xff0000) >> 16,
            (int)(uVar7 & -256) >> 8,uVar8 & 255);
    pageParaSet(&local_258,local_27c,3);
    pageParaSet(&local_258,&local_284,4);
    local_27c[0] = '\0';
    local_27c[1] = '\0';
    local_27c[2] = '\0';
    local_27c[3] = '\0';
    local_27c[4] = '\0';
    local_27c[5] = '\0';
    local_27c[6] = '\0';
    local_27c[7] = '\0';
    local_27c[8] = '\0';
    local_27c[9] = '\0';
    local_27c[10] = '\0';
    local_27c[11] = '\0';
    local_27c[12] = '\0';
    local_27c[13] = '\0';
    local_27c[14] = '\0';
    local_27c[15] = '\0';
    uVar5 = ntohl(local_280);
    uVar6 = ntohl(local_280);
    uVar7 = ntohl(local_280);
    uVar8 = ntohl(local_280);
    sprintf(local_27c,"%d.%d.%d.%d",uVar5 >> 24,(uVar6 & 0xff0000) >> 16,
            (int)(uVar7 & -256) >> 8,uVar8 & 255);
    pageParaSet(&local_258,local_27c,5);
    pageParaSet(&local_258,local_294,6);
    pageParaSet(&local_258,auStack_1a0,7);
    local_2a4 = swLanCfgIschange();
    pageParaSet(&local_258,&local_2a4,8);
    local_1a8 = swGetIgmpStatus();
    iVar10 = getProductId();
    if (iVar10 == 0x8100002) {
      pageParaSet(&local_258,&local_1a8,9);
    }
    httpStatusSet(param_1,0);
    httpHeaderGenerate(param_1);
    iVar10 = HttpAccessPermit(param_1);
    if (iVar10 == 0) {
      sVar11 = HttpDenyPage(param_1);
      goto LAB_0042c3f8;
    }
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "lanPara");
    iVar10 = 0;
    do {
      iVar3 = iVar10 + 1;
      pageDynParaPrintf(&local_258,iVar10,param_1);
      iVar10 = iVar3;
    } while (iVar3 != 10);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    if ((local_294[0] < 9) && ((1 << (local_294[0] & 31) & 0x1c8U) != 0)) {
      pcVar4 = "/userRpm/NetworkCfgRpm_AP.htm";
    }
    else {
      pcVar4 = "/userRpm/NetworkCfgRpm.htm";
    }
    iVar10 = httpRpmFsA(param_1,pcVar4);
    if (iVar10 == 2) {
      return 2;
    }
    iVar3 = 10;
    puVar13 = 0;
  }
  else {
    pcVar4 = httpGetEnv(param_1,"lantype");
    if ((pcVar4 != 0) && (local_284 = atoi(pcVar4), local_284 == 1)) {
      local_28c = getDefaultAPLanIpAddr();
      local_288 = inet_addr("*************");
LAB_0042be14:
      swSetLanCfg(&local_28c);
      if (((iVar9 != local_28c) || (iVar1 != local_288)) ||
         (((local_294[0] == 8 || (((uVar12 == 7 || (uVar12 == 3)) || (uVar12 == 6)))) &&
          ((iVar10 != local_284 || (iVar2 != local_280)))))) {
        HttpRestartRpmHtm(param_1,6);
        swReboot(2);
        return 2;
      }
      goto LAB_0042beb8;
    }
    local_284 = 0;
    pcVar4 = httpGetEnv(param_1,"langw");
    if ((pcVar4 == 0) || (iVar3 = strcmp(pcVar4,"0.0.0.0"), iVar3 == 0)) {
      local_280 = 0;
LAB_0042bc54:
      pcVar4 = httpGetEnv(param_1,"lanip");
      if ((pcVar4 == 0) || (iVar3 = swChkDotIpAddr(pcVar4), iVar3 == 0)) {
        puVar13 = "";
        iVar3 = 0x1389;
      }
      else {
        local_28c = inet_addr(pcVar4);
        pcVar4 = httpGetEnv(param_1,"lanmask");
        if (pcVar4 == 0) {
LAB_0042bdc0:
          puVar13 = "";
          iVar3 = 0x138a;
        }
        else {
          local_2a8 = atoi(pcVar4);
          if (local_2a8 == 1) {
            pcVar4 = "***********";
LAB_0042bdac:
            local_288 = inet_addr(pcVar4);
          }
          else {
            if (1 < local_2a8) {
              if (local_2a8 == 2) {
                pcVar4 = "*************";
              }
              else {
                if ((local_2a8 != 3) ||
                   (pcVar4 = httpGetEnv(param_1,"inputMask"), pcVar4 == 0))
                goto LAB_0042bdd4;
                iVar3 = swChkDotIpAddr(pcVar4);
                if (iVar3 == 0) goto LAB_0042bdc0;
              }
              goto LAB_0042bdac;
            }
            if (local_2a8 == 0) {
              pcVar4 = "*********";
              goto LAB_0042bdac;
            }
          }
LAB_0042bdd4:
          sVar11 = swChkLanCfg(&local_28c,1);
          iVar3 = sVar11;
          if (iVar3 == 0) goto LAB_0042be14;
          puVar13 = "";
        }
      }
    }
    else {
      iVar3 = swChkDotIpAddr(pcVar4);
      if (iVar3 != 0) {
        local_280 = inet_addr(pcVar4);
        goto LAB_0042bc54;
      }
      puVar13 = "";
      iVar3 = 0x7d1;
    }
  }
  sVar11 = HttpErrorPage(param_1,iVar3,puVar13,0);
LAB_0042c3f8:
  return sVar11;
}

