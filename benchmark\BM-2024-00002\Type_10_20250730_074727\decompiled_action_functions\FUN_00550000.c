
/* WARNING: Control flow encountered bad instruction data */

void FUN_00550000(void)

{
  undefined8 uVar1;
  undefined4 in_v0;
  undefined4 in_t6;
  undefined4 unaff_s1;
  int unaff_s6;
  int unaff_s8;
  
  uVar1 = getCopReg(2,in_v0);
  *(undefined8 *)(unaff_s8 + -0x45c) = uVar1;
  getCopReg(2,unaff_s1);
  uVar1 = getCopReg(2,in_t6);
  *(undefined8 *)(unaff_s6 + -415) = uVar1;
                    /* WARNING: Bad instruction - Truncating control flow here */
  halt_baddata();
}

