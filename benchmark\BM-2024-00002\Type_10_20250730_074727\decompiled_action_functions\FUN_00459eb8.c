
undefined4 FUN_00459eb8(void)

{
  int iVar1;
  undefined4 uVar2;
  undefined4 uVar3;
  undefined4 uVar4;
  char *pcVar5;
  undefined4 uVar6;
  void *__ptr;
  int local_98;
  int local_90;
  in_addr local_8c;
  in_addr local_88;
  in_addr local_84;
  char acStack_68 [32];
  char acStack_48 [32];
  char acStack_28 [32];
  
  iVar1 = apmib_get(217,&local_90);
  if (iVar1 == 0) {
    fwrite("Get table entry error!\n",1,23,stderr);
    uVar2 = 0xffffffff;
  }
  else {
    local_90 = 0;
    uVar2 = mxmlNewXML("1.0");
    uVar3 = mxmlNewElement(uVar2,"soap:Envelope");
    mxmlElementSetAttr(uVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
    mxmlElementSetAttr(uVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
    mxmlElementSetAttr(uVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
    uVar3 = mxmlNewElement(uVar3,"soap:Body");
    uVar3 = mxmlNewElement(uVar3,"GetStaticRouteSettingsResponse");
    mxmlElementSetAttr(uVar3,"xmlns","http://purenetworks.com/HNAP1/");
    uVar4 = mxmlNewElement(uVar3,"GetStaticRouteSettingsResult");
    mxmlNewText(uVar4,0,"O");
    uVar3 = mxmlNewElement(uVar3,"StaticRouteClientInfoLists");
    for (local_98 = 1; local_98 <= local_90; local_98 = local_98 + 1) {
      local_8c.s_addr._0_1_ = local_98;
      iVar1 = apmib_get(0x80da,&local_8c);
      if (iVar1 == 0) {
        return 0xffffffff;
      }
      pcVar5 = inet_ntoa(local_8c);
      strcpy(acStack_68,pcVar5);
      pcVar5 = inet_ntoa(local_88);
      strcpy(acStack_48,pcVar5);
      pcVar5 = inet_ntoa(local_84);
      strcpy(acStack_28,pcVar5);
      uVar4 = mxmlNewElement(uVar3,"ClientInfo");
      uVar6 = mxmlNewElement(uVar4,"IPAddress");
      mxmlNewText(uVar6,0,acStack_68);
      uVar6 = mxmlNewElement(uVar4,"SubnetMask");
      mxmlNewText(uVar6,0,acStack_48);
      uVar4 = mxmlNewElement(uVar4,"Gateway");
      mxmlNewText(uVar4,0,acStack_28);
    }
    __ptr = mxmlSaveAllocString(uVar2,0);
    FUN_0041ed70("",200,__ptr,"");
    free(__ptr);
    uVar2 = mxmlDelete(uVar2);
  }
  return uVar2;
}

