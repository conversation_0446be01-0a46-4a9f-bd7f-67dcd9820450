
undefined4 FUN_00439224(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  int iVar3;
  char *pcVar4;
  uint uVar5;
  int local_9c;
  int local_78;
  in_addr iStack_64;
  undefined2 local_60;
  undefined2 local_5e;
  undefined local_5c;
  char acStack_3c [20];
  uint local_28;
  uint local_24;
  undefined local_1f;
  undefined4 local_1c;
  undefined4 local_18;
  undefined4 local_14;
  undefined4 local_10;
  undefined4 local_c;
  
  bVar1 = true;
  local_9c = 0;
  local_1c = 0;
  local_18 = 0;
  local_14 = 0;
  local_10 = 0;
  local_c = 0;
  iVar2 = mxmlLoadString(0,param_1,0);
  if (iVar2 != 0) {
    iVar3 = mxmlFindElement(iVar2,iVar2,"soap:Envelope",0,0,1);
    if ((iVar3 != 0) &&
       (local_78 = mxmlFindElement(iVar3,iVar2,"SetVirtualServerSettings",0,0,1), local_78 != 0)) {
      apmib_set(0x40073,&iStack_64);
      do {
        memset(&iStack_64,0,"F");
        local_78 = mxmlFindElement(local_78,iVar2,"VirtualServerInfo",0,0,1);
        if (local_78 != 0) {
          local_9c = local_9c + 1;
          if (24 < local_9c) {
            bVar1 = false;
            memcpy(&local_18,"ERROR",6);
            goto LAB_00439ab0;
          }
          local_1f = 1;
          iVar3 = mxmlFindElement(local_78,iVar2,"Enabled",0,0,1);
          if ((iVar3 != 0) && (pcVar4 = mxmlGetText(iVar3,0), pcVar4 != 0)) {
            iVar3 = strcmp(pcVar4,"false");
            if (iVar3 == 0) {
              local_1f = 0;
            }
            else {
              local_1f = 1;
            }
          }
          iVar3 = mxmlFindElement(local_78,iVar2,"VirtualServerDescription",0,0,1);
          if ((iVar3 != 0) && (pcVar4 = mxmlGetText(iVar3,0), pcVar4 != 0)) {
            strncpy(acStack_3c,pcVar4,20);
          }
          iVar3 = mxmlFindElement(local_78,iVar2,"ExternalPort",0,0,1);
          if ((iVar3 != 0) && (pcVar4 = mxmlGetText(iVar3,0), pcVar4 != 0)) {
            iVar3 = FUN_0042e5cc(pcVar4);
            if (iVar3 == -1) {
              puts("ExternalPort data error");
              bVar1 = false;
              goto LAB_00439ab0;
            }
            pcVar4 = strtok(pcVar4,"-");
            if (pcVar4 == 0) {
              puts("ExternalPort data error");
              bVar1 = false;
              goto LAB_00439ab0;
            }
            uVar5 = atoi(pcVar4);
            local_28 = local_28 & 0xff0000ff | (uVar5 & -1) << 8;
            pcVar4 = strtok(0,"-");
            if (pcVar4 == 0) {
              uVar5 = local_28 << 8;
              local_28 = local_28 & 0xffffff | (uVar5 >> 16) << 24;
              local_24 = local_24 & 0xffffff00 | uVar5 >> 24;
            }
            else {
              uVar5 = atoi(pcVar4);
              local_28 = local_28 & 0xffffff | uVar5 << 24;
              local_24 = local_24 & 0xffffff00 | (uVar5 & -1) >> 8;
            }
            iVar3 = FUN_0042e6b4((local_28 << 8) >> 16);
            if ((iVar3 == -1) || (iVar3 = FUN_0042e6b4((local_28 << 8) >> 16), iVar3 == -1)) {
              puts("ExternalPort data error");
              bVar1 = false;
              goto LAB_00439ab0;
            }
          }
          iVar3 = mxmlFindElement(local_78,iVar2,"InternalPort",0,0,1);
          if ((iVar3 != 0) && (pcVar4 = mxmlGetText(iVar3,0), pcVar4 != 0)) {
            iVar3 = FUN_0042e5cc(pcVar4);
            if (iVar3 == -1) {
              puts("InternalPort data error");
              bVar1 = false;
              goto LAB_00439ab0;
            }
            pcVar4 = strtok(pcVar4,"-");
            if (pcVar4 == 0) {
              puts("InternalPort data error");
              bVar1 = false;
              goto LAB_00439ab0;
            }
            iVar3 = atoi(pcVar4);
            local_60 = iVar3;
            pcVar4 = strtok(0,"-");
            if (pcVar4 == 0) {
              local_5e = local_60;
            }
            else {
              iVar3 = atoi(pcVar4);
              local_5e = iVar3;
            }
            iVar3 = FUN_0042e6b4(local_60);
            if ((iVar3 == -1) || (iVar3 = FUN_0042e6b4(local_5e), iVar3 == -1)) {
              puts("InternalPort data error");
              bVar1 = false;
              goto LAB_00439ab0;
            }
          }
          iVar3 = mxmlFindElement(local_78,iVar2,"ProtocolType",0,0,1);
          if ((iVar3 != 0) && (pcVar4 = mxmlGetText(iVar3,0), pcVar4 != 0)) {
            iVar3 = strcmp(pcVar4,&PTR_0x00504354);
            if (iVar3 == 0) {
              local_5c = 1;
            }
            else {
              iVar3 = strcmp(pcVar4,&PTR_0x00504455);
              if (iVar3 == 0) {
                local_5c = 2;
              }
              else {
                iVar3 = strcmp(pcVar4,"BOTH");
                if (iVar3 != 0) {
                  puts("ProtocolType data error");
                  bVar1 = false;
                  goto LAB_00439ab0;
                }
                local_5c = 3;
              }
            }
          }
          iVar3 = mxmlFindElement(local_78,iVar2,"LocalIPAddress",0,0,1);
          if (((iVar3 != 0) && (pcVar4 = mxmlGetText(iVar3,0), pcVar4 != 0)) &&
             (iVar3 = inet_aton(pcVar4,&iStack_64), iVar3 == 0)) {
            puts("LocalIPAddress data error");
            bVar1 = false;
            goto LAB_00439ab0;
          }
          apmib_set(0x20072,&iStack_64);
          iVar3 = apmib_set(0x10071,&iStack_64);
          if (iVar3 == 0) {
            puts("Add table entry error!");
            bVar1 = false;
            goto LAB_00439ab0;
          }
        }
      } while (local_78 != 0);
    }
    if (0 < local_9c) {
      local_1c = 1;
    }
LAB_00439ab0:
    mxmlDelete(iVar2);
    if (("" == 0) || (!bVar1)) {
      if (bVar1) {
        memcpy(&local_18,"O",3);
        apmib_set("n",&local_1c);
        apmib_update(4);
        FUN_00421468("firewall.sh",0,0);
      }
      else {
        memcpy(&local_18,"ERROR",6);
      }
      FUN_004260e0("SetVirtualServerSettings",&local_18);
    }
  }
  return 0;
}

