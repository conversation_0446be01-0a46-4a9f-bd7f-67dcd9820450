
undefined4 FUN_00438350(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  int iVar3;
  undefined4 uVar4;
  char *pcVar5;
  int local_24;
  in_addr iStack_20;
  undefined4 local_1c;
  undefined4 local_18;
  undefined4 local_14;
  undefined4 local_10;
  
  local_24 = 0;
  bVar1 = true;
  local_1c = 0;
  local_18 = 0;
  local_14 = 0;
  local_10 = 0;
  iVar2 = mxmlLoadString(0,param_1,0);
  if (iVar2 == 0) {
    return 0;
  }
  iVar3 = mxmlFindElement(iVar2,iVar2,"soap:Envelope",0,0,1);
  if ((iVar3 != 0) && (iVar3 = mxmlFindElement(iVar3,iVar2,"SetDMZSettings",0,0,1), iVar3 != 0)) {
    uVar4 = mxmlFindElement(iVar3,iVar2,"Enabled",0,0,1);
    pcVar5 = mxmlGetText(uVar4,0);
    if (pcVar5 != 0) {
      iVar3 = strcmp(pcVar5,"true");
      if (iVar3 == 0) {
        local_24 = 1;
      }
      else {
        iVar3 = strcmp(pcVar5,"false");
        if (iVar3 != 0) {
          bVar1 = false;
          goto LAB_004385a0;
        }
        local_24 = 0;
      }
      apmib_set(140,&local_24);
    }
    if (local_24 == 1) {
      uVar4 = mxmlFindElement(uVar4,iVar2,"IPAddress",0,0,1);
      pcVar5 = mxmlGetText(uVar4,0);
      if (pcVar5 != 0) {
        iVar3 = inet_aton(pcVar5,&iStack_20);
        if (iVar3 == 0) {
          bVar1 = false;
        }
        else {
          apmib_set(141,&iStack_20);
        }
      }
    }
  }
LAB_004385a0:
  mxmlDelete(iVar2);
  if (("" == 0) || (!bVar1)) {
    if (bVar1) {
      memcpy(&local_1c,"O",3);
      apmib_update(4);
      FUN_00421468("firewall.sh",0,0);
    }
    else {
      memcpy(&local_1c,"ERROR",6);
    }
    FUN_004260e0("SetDMZSettings",&local_1c);
  }
  return 0;
}

