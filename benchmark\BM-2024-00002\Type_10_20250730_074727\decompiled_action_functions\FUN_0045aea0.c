
undefined4 FUN_0045aea0(int param_1)

{
  undefined4 uVar1;
  int iVar2;
  int iVar3;
  int iVar4;
  void *__ptr;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetAdvNetworkSettings");
    uVar1 = 0;
  }
  else {
    iVar2 = mxmlNewXML("1.0");
    if (iVar2 == 0) {
      puts("xml=NULL");
      uVar1 = 0;
    }
    else {
      iVar3 = mxmlNewElement(iVar2,"soap:Envelope");
      if (iVar3 == 0) {
        mxmlDelete(iVar2);
        puts("soap_env=NULL");
        uVar1 = 0;
      }
      else {
        mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar3 = mxmlNewElement(iVar3,"soap:Body");
        if (iVar3 == 0) {
          mxmlDelete(iVar2);
          puts("body=NULL");
          uVar1 = 0;
        }
        else {
          iVar3 = mxmlNewElement(iVar3,"GetAdvNetworkSettingsResponse");
          if (iVar3 == 0) {
            mxmlDelete(iVar2);
            puts("GetAdvNetworkSettingsResponse_xml=NULL");
            uVar1 = 0;
          }
          else {
            mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
            iVar4 = mxmlNewElement(iVar3,"GetAdvNetworkSettingsResult");
            if (iVar4 == 0) {
              mxmlDelete(iVar2);
              puts("GetAdvNetworkSettingsResult_xml=NULL");
              uVar1 = 0;
            }
            else {
              mxmlNewText(iVar4,0,"O");
              iVar4 = mxmlNewElement(iVar3,"UPNP");
              if (iVar4 == 0) {
                mxmlDelete(iVar2);
                puts("UPNP_xml=NULL");
                uVar1 = 0;
              }
              else {
                mxmlNewText(iVar4,0,"false");
                iVar4 = mxmlNewElement(iVar3,"MulticastIPv4");
                if (iVar4 == 0) {
                  mxmlDelete(iVar2);
                  puts("MulticastIPv4_xml=NULL");
                  uVar1 = 0;
                }
                else {
                  mxmlNewText(iVar4,0,"false");
                  iVar4 = mxmlNewElement(iVar3,"MulticastIPv6");
                  if (iVar4 == 0) {
                    mxmlDelete(iVar2);
                    puts("MulticastIPv6_xml=NULL");
                    uVar1 = 0;
                  }
                  else {
                    mxmlNewText(iVar4,0,"false");
                    iVar3 = mxmlNewElement(iVar3,"WANPortSpeed");
                    if (iVar3 == 0) {
                      mxmlDelete(iVar2);
                      puts("WANPortSpeed_xml=NULL");
                      uVar1 = 0;
                    }
                    else {
                      mxmlNewText(iVar3,0,"Auto");
                      __ptr = mxmlSaveAllocString(iVar2,0);
                      if (__ptr == 0) {
                        puts("retstring=NULL");
                      }
                      else {
                        FUN_0041ed70("",200,__ptr,"");
                        free(__ptr);
                      }
                      uVar1 = mxmlDelete(iVar2);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return uVar1;
}

