
int FUN_0044a530(undefined4 param_1)

{
  int iVar1;
  short sVar8;
  char *pcVar2;
  char *pcVar3;
  int iVar4;
  uint uVar5;
  int iVar6;
  undefined4 uVar7;
  int iVar9;
  int iVar10;
  int local_50;
  timeval local_4c;
  undefined4 local_44;
  undefined4 local_40;
  undefined4 local_3c;
  undefined4 local_38;
  int local_34;
  int local_30;
  int local_2c;
  int local_28;
  
  local_50 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar8 = HttpDenyPage(param_1);
    goto LAB_0044afb8;
  }
  iVar1 = httpGetEnv(param_1,"ping_addr");
  pcVar2 = httpGetEnv(param_1,"doType");
  pcVar3 = httpGetEnv(param_1,"isNew");
  if ((iVar1 == 0) || (pcVar2 == 0)) {
LAB_0044af20:
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "diagnostic_para");
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  }
  else {
    iVar4 = strcmp(pcVar3,"new");
    if (iVar4 == 0) {
      printf("[ %s ] %03d:  new operate.\n\n","pingAndTracert/httpPingAndTracertIframeRpm.c",447);
      iVar4 = swcheckTrResuIsEmpty();
      if (iVar4 == 0) {
LAB_0044a794:
        iVar4 = swChkPingnResultEmpty();
        if (iVar4 != 0) {
          swGetPingLastOverTime(&local_44);
          gettimeofday(&local_4c,0);
          uVar5 = swGetTimeDiff(local_4c.tv_sec,local_4c.tv_usec,local_44,local_40);
          if (uVar5 < 0xbb9) {
            pcVar2 = "ping refuesd..\r";
            goto LAB_0044a8bc;
          }
          printf("[ %s ] %03d:  timedout, del last Ping..\n\n",
                 "pingAndTracert/httpPingAndTracertIframeRpm.c",492);
          local_3c = 0;
          local_38 = 0;
          swDiagnosticSendOp(0,0,local_34,local_30,local_2c,local_28);
          while (iVar4 = swChkPingStop(), iVar4 == 0) {
            usleep(1000);
          }
          pingClearAll();
        }
        iVar4 = strcmp(pcVar2,"ping");
        iVar10 = 0;
        if (iVar4 == 0) {
          pcVar3 = httpGetEnv(param_1,"sendNum");
          iVar10 = atoi(pcVar3);
          pcVar3 = httpGetEnv(param_1,"pSize");
          iVar4 = atoi(pcVar3);
          pcVar3 = httpGetEnv(param_1,"overTime");
          iVar9 = atoi(pcVar3);
        }
        else {
          iVar4 = 0;
          iVar9 = 0;
        }
        iVar6 = strcmp(pcVar2,"tracert");
        if (iVar6 == 0) {
          httpGetEnv(param_1,"trHops");
        }
        iVar6 = strcmp(pcVar2,"tracert");
        if (iVar6 == 0) {
          pcVar2 = httpGetEnv(param_1,"trHops");
          iVar4 = atoi(pcVar2);
          iVar1 = ipAddrDispose(iVar1);
          if (iVar1 != 0) {
            local_3c = 1;
            local_38 = 1;
            local_34 = iVar1;
            local_30 = iVar4;
            swDiagnosticSendOp(1,1,iVar1,iVar4,local_2c,local_28);
            usleep(iVar9 * 1000);
            uVar7 = swGetTracertResult(&local_50);
            FUN_00449ccc(param_1,uVar7,local_50,0,0);
            goto joined_r0x0044acb4;
          }
        }
        else {
          iVar6 = strcmp(pcVar2,"ping");
          if (iVar6 != 0) goto LAB_0044af20;
          printf("[ %s ] %03d:  Here is new ping\n\n","pingAndTracert/httpPingAndTracertIframeRpm.c"
                 ,564);
          iVar1 = ipAddrDispose(iVar1);
          if (iVar1 != 0) {
            local_3c = 1;
            local_38 = 0;
            local_34 = iVar1;
            local_30 = iVar10;
            local_2c = iVar4;
            local_28 = iVar9;
            swDiagnosticSendOp(1,0,iVar1,iVar10,iVar4,iVar9);
            usleep(iVar9 * 1000);
            uVar7 = swGetPingResult(&local_50);
            FUN_0044a128(param_1,uVar7,local_50,0,0);
            goto joined_r0x0044ad58;
          }
          printf("[ %s ] %03d:  host is error\n\n","pingAndTracert/httpPingAndTracertIframeRpm.c",
                 570);
        }
        uVar7 = "5";
      }
      else {
        swGetTracertLastOverTime(&local_44);
        gettimeofday(&local_4c,0);
        uVar5 = swGetTimeDiff(local_4c.tv_sec,local_4c.tv_usec,local_44,local_40);
        if (8000 < uVar5) {
          printf("[ %s ] %03d:  timedout, del last tracert..\n\n",
                 "pingAndTracert/httpPingAndTracertIframeRpm.c",460);
          local_38 = 1;
          local_3c = 0;
          swDiagnosticSendOp(0,1,local_34,local_30,local_2c,local_28);
          while (iVar4 = swChkTracertStop(), iVar4 == 0) {
            usleep(1000);
          }
          swTracertClearAll();
          goto LAB_0044a794;
        }
        pcVar2 = "tracert refuesd..\r";
LAB_0044a8bc:
        puts(pcVar2);
        uVar7 = 9;
      }
      FUN_00449bd4(param_1,uVar7);
    }
    else {
      iVar1 = strcmp(pcVar3,"old");
      if (iVar1 != 0) {
        iVar1 = strcmp(pcVar3,"stop");
        if (iVar1 == 0) {
          iVar1 = strcmp(pcVar2,"tracert");
          if (iVar1 == 0) {
            local_38 = 1;
            local_3c = 0;
            local_34 = 0;
            local_30 = 0;
            swDiagnosticSendOp(0,1,0,0,local_2c,local_28);
            while (iVar1 = swChkTracertStop(), iVar1 == 0) {
              usleep(1000);
            }
            swTracertClearAll();
          }
          iVar1 = strcmp(pcVar2,"ping");
          if (iVar1 == 0) {
            printf("[ %s ] %03d:  stop ping ~~\n\n","pingAndTracert/httpPingAndTracertIframeRpm.c",
                   644);
            local_3c = 0;
            local_38 = 0;
            local_34 = 0;
            local_30 = 0;
            local_2c = 0;
            local_28 = 0;
            swDiagnosticSendOp(0,0,0,0,0,0);
            while (iVar1 = swChkPingStop(), iVar1 == 0) {
              usleep(1000);
            }
            swPingClearAll();
          }
        }
        else {
          puts("impossible, must be some problem.");
        }
        goto LAB_0044af20;
      }
      iVar1 = strcmp(pcVar2,"tracert");
      if (iVar1 == 0) {
        pcVar2 = httpGetEnv(param_1,"lineNum");
        iVar1 = atoi(pcVar2);
        uVar7 = swGetTracertResult(&local_50);
        FUN_00449ccc(param_1,uVar7,local_50,1,iVar1);
joined_r0x0044acb4:
        if (local_50 == 1) {
          swTracertClearAll();
        }
      }
      else {
        iVar1 = strcmp(pcVar2,"ping");
        if (iVar1 != 0) goto LAB_0044af20;
        pcVar2 = httpGetEnv(param_1,"lineNum");
        iVar1 = atoi(pcVar2);
        uVar7 = swGetPingResult(&local_50);
        FUN_0044a128(param_1,uVar7,local_50,1,iVar1);
joined_r0x0044ad58:
        if (local_50 == 1) {
          swPingClearAll();
        }
      }
    }
  }
  HttpWebV4Head(param_1,0,1);
  iVar1 = httpRpmFsA(param_1,"/userRpm/PingIframeRpm.htm");
  if (iVar1 == 2) {
    return 2;
  }
  sVar8 = HttpErrorPage(param_1,10,0,0);
LAB_0044afb8:
  return sVar8;
}

