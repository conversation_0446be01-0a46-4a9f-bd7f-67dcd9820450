
undefined4 FUN_00440b90(int param_1)

{
  int iVar1;
  int iVar2;
  undefined4 uVar3;
  char *pcVar4;
  undefined4 local_14;
  int local_10 [2];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetAutoRebootSettings");
  }
  else {
    local_14 = 0;
    local_10[0] = 0;
    iVar1 = mxmlLoadString(0,param_1,0);
    if (iVar1 != 0) {
      iVar2 = mxmlFindElement(iVar1,iVar1,"SetAutoRebootSettings",0,0,1);
      if (iVar2 != 0) {
        uVar3 = mxmlFindElement(iVar1,iVar1,"AutoReboot",0,0,1);
        pcVar4 = mxmlGetText(uVar3,0);
        if (pcVar4 == 0) {
          puts("error, auto reboot is NULL");
        }
        else {
          iVar2 = strcmp(pcVar4,"true");
          if (iVar2 == 0) {
            local_14 = 1;
          }
          else {
            local_14 = 0;
          }
        }
        iVar2 = apmib_set(0x1b64,&local_14);
        if (iVar2 == 0) {
          puts("error, apmib set auto reboot");
        }
        else {
          uVar3 = mxmlFindElement(iVar1,iVar1,"StartTime",0,0,1);
          pcVar4 = mxmlGetText(uVar3,0);
          if (pcVar4 != 0) {
            local_10[0] = atoi(pcVar4);
            iVar1 = apmib_set(0x1b65,local_10);
            if (iVar1 == 0) {
              puts("error set MIB_AUTO_REBOOT_TIME");
            }
            iVar1 = apmib_set(0x1b60,local_10);
            if (iVar1 == 0) {
              puts("error set MIB_AUTO_UPGRADE_FIRM_TIME");
            }
          }
        }
      }
    }
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar2 = mxmlNewElement(iVar2,"soap:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"SetAutoRebootSettingsResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("SetAutoRebootSettingsResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar2 = mxmlNewElement(iVar2,"SetAutoRebootSettingsResult");
            if (iVar2 == 0) {
              mxmlDelete(iVar1);
              puts("SetAutoRebootSettingsResult=NULL");
            }
            else {
              mxmlNewText(iVar2,0,"O");
              if ("" == 0) {
                apmib_update(4);
                pcVar4 = mxmlSaveAllocString(iVar1,0);
                if (pcVar4 != 0) {
                  puts(pcVar4);
                  FUN_0041ed70("",200,pcVar4,"");
                  free(pcVar4);
                  system("killall crond");
                  system("/bin/isAutoSettings");
                }
              }
              mxmlDelete(iVar1);
            }
          }
        }
      }
    }
  }
  return 0;
}

