
undefined4 FUN_0043c34c(int param_1)

{
  int iVar1;
  int iVar2;
  undefined4 uVar3;
  char *__s1;
  int iVar4;
  void *__ptr;
  uint local_14;
  undefined4 local_10 [2];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetAutoUpgradeFirmware");
  }
  else {
    local_14 = 0;
    local_10[0] = 0;
    iVar1 = mxmlLoadString(0,param_1,0);
    if (iVar1 == 0) {
      puts("ERROR!  tree is NULL");
    }
    else {
      iVar2 = mxmlFindElement(iVar1,iVar1,"SetAutoUpgradeFirmware",0,0,1);
      if (iVar2 != 0) {
        uVar3 = mxmlFindElement(iVar2,iVar1,"AutoUpgrade",0,0,1);
        __s1 = mxmlGetText(uVar3,0);
        if (__s1 == 0) {
          puts("AutoUpgrade==NULL");
        }
        else {
          iVar4 = strncmp(__s1,"true",4);
          local_14 = (uint)(iVar4 == 0);
          apmib_set(0x1b61,&local_14);
        }
        uVar3 = mxmlFindElement(iVar2,iVar1,"StartTime",0,0,1);
        iVar2 = mxmlGetText(uVar3,0);
        if (iVar2 != 0) {
          local_10[0] = FUN_0042c9cc(iVar2);
          iVar2 = apmib_set(0x1b60,local_10);
          if (iVar2 == 0) {
            puts("error set MIB_AUTO_UPGRADE_FIRM_TIME");
          }
          iVar2 = apmib_set(0x1b65,local_10);
          if (iVar2 == 0) {
            puts("error set MIB_AUTO_REBOOT_TIME");
          }
        }
      }
      iVar2 = mxmlNewXML("1.0");
      if (iVar2 == 0) {
        printf("Create new xml erro!!!");
      }
      else {
        iVar4 = mxmlNewElement(iVar2,"SOAP-ENV:Envelope");
        if (iVar4 == 0) {
          mxmlDelete(iVar2);
          puts("soap_env=NULL");
        }
        else {
          mxmlElementSetAttr(iVar4,"xmlns:SOAP-ENV","http://schemas.xmlsoap.org/soap/envelope/");
          mxmlElementSetAttr(iVar4,"SOAP-ENV:encodingStyle",
                             "http://schemas.xmlsoap.org/soap/encoding/");
          iVar4 = mxmlNewElement(iVar4,"SOAP-ENV:Body");
          if (iVar4 == 0) {
            mxmlDelete(iVar2);
            puts("body=NULL");
          }
          else {
            iVar4 = mxmlNewElement(iVar4,"SetAutoUpgradeFirmware");
            if (iVar4 == 0) {
              mxmlDelete(iVar2);
              puts("SetAutoUpgradeFirmwareResponse_xml=NULL");
            }
            else {
              mxmlElementSetAttr(iVar4,"xmlns","http://purenetworks.com/HNAP1/");
              iVar4 = mxmlNewElement(iVar4,"SetAutoUpgradeFirmwareResult");
              if (iVar4 == 0) {
                mxmlDelete(iVar2);
                puts("SetAutoUpgradeFirmwareResult_xml=NULL");
              }
              else {
                mxmlNewText(iVar4,0,"O");
                if (("" == 0) &&
                   (__ptr = mxmlSaveAllocString(iVar2,0), __ptr != 0)) {
                  apmib_update(4);
                  system("killall crond");
                  system("/bin/isAutoSettings");
                  FUN_0041ed70("",200,__ptr,"");
                  free(__ptr);
                }
                mxmlDelete(iVar2);
                mxmlDelete(iVar1);
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

