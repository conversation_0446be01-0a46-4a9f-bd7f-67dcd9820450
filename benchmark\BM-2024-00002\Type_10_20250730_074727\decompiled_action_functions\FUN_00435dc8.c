
undefined4 FUN_00435dc8(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  FILE *__stream;
  char *pcVar4;
  size_t __n;
  void *__ptr;
  char *local_1c8;
  undefined auStack_19c [100];
  undefined auStack_138 [100];
  char cStack_d4;
  undefined auStack_d3 [203];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetPPPoEServerStatus");
  }
  else {
    local_1c8 = "false";
    memset(auStack_19c,0,100);
    memset(auStack_138,0,100);
    memset(&cStack_d4,0,200);
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"SOAP-ENV:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:SOAP-ENV","http://schemas.xmlsoap.org/soap/envelope/");
        mxmlElementSetAttr(iVar2,"SOAP-ENV:encodingStyle",
                           "http://schemas.xmlsoap.org/soap/encoding/");
        iVar2 = mxmlNewElement(iVar2,"SOAP-ENV:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"GetPPPoEServerStatusResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("GetPPPoEServerStatusResponse_xml=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar3 = mxmlNewElement(iVar2,"GetPPPoEServerStatusResult");
            if (iVar3 == 0) {
              mxmlDelete(iVar1);
              puts("GetPPPoEServerStatusResult_xml=NULL");
            }
            else {
              mxmlNewText(iVar3,0,"O");
              __stream = fopen("/tmp/pppoe_info_log","r+");
              if (__stream == 0) {
                puts("fp===NULL");
              }
              else {
                fread(&cStack_d4,1,199,__stream);
                pcVar4 = strchr(&cStack_d4,10);
                __n = pcVar4 - &cStack_d4;
                memcpy(auStack_19c,&cStack_d4,__n);
                pcVar4 = strchr(pcVar4 + 1,10);
                memcpy(auStack_138,auStack_d3 + __n,pcVar4 - (int)(auStack_d3 + __n));
                local_1c8 = "true";
                fclose(__stream);
              }
              iVar3 = mxmlNewElement(iVar2,"Type");
              if (iVar3 == 0) {
                mxmlDelete(iVar1);
                puts("Type_xml=NULL");
              }
              else {
                mxmlNewText(iVar3,0,local_1c8);
                iVar3 = mxmlNewElement(iVar2,"UserName");
                if (iVar3 == 0) {
                  mxmlDelete(iVar1);
                  puts("UserName_xml=NULL");
                }
                else {
                  mxmlNewText(iVar3,0,auStack_19c);
                  iVar2 = mxmlNewElement(iVar2,"Password");
                  if (iVar2 == 0) {
                    mxmlDelete(iVar1);
                    puts("Password_xml=NULL");
                  }
                  else {
                    mxmlNewText(iVar2,0,auStack_138);
                    __ptr = mxmlSaveAllocString(iVar1,0);
                    if (__ptr != 0) {
                      FUN_0041ed70("",200,__ptr,"");
                      free(__ptr);
                    }
                    mxmlDelete(iVar1);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

