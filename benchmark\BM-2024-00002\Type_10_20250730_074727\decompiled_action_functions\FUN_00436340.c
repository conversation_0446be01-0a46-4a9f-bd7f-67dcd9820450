
undefined4 FUN_00436340(int param_1)

{
  int iVar1;
  int iVar2;
  char *__s1;
  int iVar3;
  void *__ptr;
  char acStack_198 [400];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetPPPoEServerSettings");
  }
  else {
    memset(acStack_198,0,400);
    iVar1 = mxmlLoadString(0,param_1,0);
    if (iVar1 == 0) {
      puts("ERROR!  tree is NULL");
    }
    else {
      iVar2 = mxmlFindElement(iVar1,iVar1,"SetPPPoEServerSettings",0,0,1);
      if (iVar2 == 0) {
        puts("ERROR! state or tree is NULL");
      }
      else {
        iVar2 = mxmlFindElement(iVar2,iVar1,"Enabled",0,0,1);
        if (iVar2 == 0) {
          puts("state1=NULL");
          mxmlDelete(iVar1);
        }
        else {
          __s1 = mxmlGetText(iVar2,0);
          if (__s1 != 0) {
            iVar2 = strncmp(__s1,"true",4);
            if (iVar2 == 0) {
              snprintf(acStack_198,99,"rm %s","/tmp/pppoe_info_log");
              printf("tempbuf=%s\n",acStack_198);
              system(acStack_198);
              system("killall getpppoeinfo");
              system("getpppoeinfo &");
            }
            else {
              iVar2 = strncmp(__s1,"false",5);
              if (iVar2 == 0) {
                system("killall getpppoeinfo");
              }
            }
          }
          iVar2 = mxmlNewXML("1.0");
          if (iVar2 == 0) {
            printf("Create new xml erro!!!");
          }
          else {
            iVar3 = mxmlNewElement(iVar2,"SOAP-ENV:Envelope");
            if (iVar3 == 0) {
              mxmlDelete(iVar2);
              puts("soap_env=NULL");
            }
            else {
              mxmlElementSetAttr(iVar3,"xmlns:SOAP-ENV","http://schemas.xmlsoap.org/soap/envelope/")
              ;
              mxmlElementSetAttr(iVar3,"SOAP-ENV:encodingStyle",
                                 "http://schemas.xmlsoap.org/soap/encoding/");
              iVar3 = mxmlNewElement(iVar3,"SOAP-ENV:Body");
              if (iVar3 == 0) {
                mxmlDelete(iVar2);
                puts("body=NULL");
              }
              else {
                iVar3 = mxmlNewElement(iVar3,"SetPPPoEServerSettingsResponse");
                if (iVar3 == 0) {
                  mxmlDelete(iVar2);
                  puts("SetPPPoEServerSettingsResponse_xml=NULL");
                }
                else {
                  mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
                  iVar3 = mxmlNewElement(iVar3,"SetPPPoEServerSettingsResult");
                  if (iVar3 == 0) {
                    mxmlDelete(iVar2);
                    puts("SetPPPoEServerSettingsResult_xml=NULL");
                  }
                  else {
                    mxmlNewText(iVar3,0,"O");
                    __ptr = mxmlSaveAllocString(iVar2,0);
                    if (__ptr != 0) {
                      FUN_0041ed70("",200,__ptr,"");
                      free(__ptr);
                    }
                    mxmlDelete(iVar2);
                    mxmlDelete(iVar1);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

