
int FUN_0042a00c(undefined4 param_1)

{
  char *__nptr;
  uint uVar1;
  short sVar2;
  uint *puVar3;
  uint uVar4;
  char local_30;
  undefined auStack_2f [35];
  
  local_30 = '\0';
  memset(auStack_2f,0,31);
  uVar4 = 0;
  __nptr = httpGetEnv(param_1,"wan");
  if (((__nptr != 0) && (uVar4 = atoi(__nptr), uVar4 < 0)) ||
     (uVar1 = getMaxWanPortNumber(), uVar1 <= uVar4)) {
    uVar4 = 0;
  }
  uVar4 = swGetWanType(uVar4);
  puVar3 = "";
  if (uVar4 < 9) {
    for (; puVar3 != 0; puVar3 = puVar3[10]) {
      if (*puVar3 == uVar4) {
        sprintf(&local_30,"../userRpm/%s",puVar3 + 1);
        sVar2 = GoUrl(param_1,&local_30);
        return sVar2;
      }
    }
  }
  return 2;
}

