
/* WARNING: Removing unreachable block (ram,FUN_0045faa0) */
/* WARNING: Type propagation algorithm not settling */

int FUN_0045fa94(undefined4 param_1)

{
  int iVar1;
  int iVar2;
  short sVar10;
  uint32_t uVar3;
  uint32_t uVar4;
  uint32_t uVar5;
  uint32_t uVar6;
  undefined4 uVar7;
  undefined1 *puVar8;
  char *pcVar9;
  size_t __n;
  uint uVar11;
  uint uStack_e28;
  uint uStack_e24;
  uint uStack_e20;
  uint uStack_e1c;
  uint auStack_e18 [4];
  uint32_t auStack_e08 [4];
  char acStack_df8 [16];
  char acStack_de8 [20];
  char acStack_dd4 [20];
  undefined auStack_dc0 [24];
  char acStack_da8 [36];
  char acStack_d84 [36];
  uint uStack_d60;
  uint uStack_d5c;
  uint uStack_d58;
  uint auStack_d54 [5];
  undefined auStack_d40 [4];
  int iStack_d3c;
  int iStack_d38;
  undefined auStack_d24 [348];
  undefined uStack_bc8;
  byte bStack_bc7;
  byte abStack_bc5 [40];
  byte bStack_b9d;
  ushort uStack_b9c;
  byte abStack_b9a [2906];
  undefined4 uStack_40;
  char *pcStack_3c;
  char *pcStack_38;
  undefined *puStack_34;
  uint *puStack_30;
  uint *puStack_2c;
  
  uStack_e28 = 0;
  uStack_e24 = 0;
  uStack_e20 = 0;
  uStack_e1c = 0;
  auStack_e18[0] = 0;
  iVar1 = swGetBoardType();
  swWlanBasicCfgGet(0,auStack_d40);
  swGetSystemMode(auStack_e18 + 2);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    iVar2 = getRefererFlag();
    if (iVar2 == 0) {
      sVar10 = HttpDenyPage(param_1);
      goto LAB_0045fa54;
    }
    setRefererFlag(0);
  }
  memset(&uStack_bc8,0,0xb82);
  iVar2 = swGetBoardType();
  if (iVar2 == 0) {
    iVar2 = httpGetEnv(param_1,"getWdsResult");
    if (iVar2 != 0) {
      swWlanWDSScan(0,&uStack_bc8,0);
      goto LAB_0045eee4;
    }
    memcpy(auStack_dc0,"popupSiteSurveyRpm.htm",23);
    auStack_e18[1] = 25;
    uStack_40 = httpGetEnv(param_1,"QUERY_STRING");
    swGetLanCfg(auStack_e08);
    uVar3 = ntohl(auStack_e08[0]);
    uVar4 = ntohl(auStack_e08[0]);
    uVar5 = ntohl(auStack_e08[0]);
    uVar6 = ntohl(auStack_e08[0]);
    sprintf(acStack_df8,"%d.%d.%d.%d",uVar3 >> 24,uVar4 >> 16 & 255,(int)(uVar5 & -256) >> 8,
            uVar6 & 255);
    swWlanWDSScan(0,&uStack_bc8,1);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "waitWdsInf");
    writePageParamSet(param_1,""%s",",auStack_dc0,0);
    writePageParamSet(param_1,""%s",",uStack_40,1);
    writePageParamSet(param_1,"%d,",auStack_e18 + 1,2);
    writePageParamSet(param_1,""%s",",acStack_df8,3);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    pcVar9 = "/userRpm/WaitForWdsScanResult.htm";
  }
  else {
    swWlanActivateScan(0,&uStack_bc8);
LAB_0045eee4:
    if (auStack_e18[2] == 4) {
      iVar2 = 1;
      iStack_d3c = iStack_d38;
LAB_0045ef34:
      if (iStack_d3c == iVar2) {
        uVar7 = wlanGetApDevName(0);
        swWlanInactiveVap(uVar7);
      }
    }
    else if (auStack_e18[2] < 5) {
      if (auStack_e18[2] == 3) {
LAB_0045ef20:
        iVar2 = 3;
        goto LAB_0045ef34;
      }
    }
    else if (auStack_e18[2] - 6 < 3) goto LAB_0045ef20;
    uVar11 = bStack_bc7;
    auStack_e18[0] = uVar11;
    if (iVar1 == 0) {
      for (uStack_e1c = 0; uStack_e1c < uVar11; uStack_e1c = uStack_e1c + 1) {
        uStack_e24 = (uint)*(ushort *)(&bStack_b9d + uStack_e1c * "/");
        if ((uStack_e24 - "4" < 13) || (uStack_e24 - 100 < ")")) {
          HTTP_DEBUG_PRINT("wireless/httpWlanCfg.c:190",
                           "**: filter out ap working on dfs chan(%d)\n",uStack_e24);
          auStack_e18[0] = auStack_e18[0] - 1;
        }
      }
    }
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "siteSurveyPara");
    uStack_e1c = 2;
    writePageParamSet(param_1,"%d,",&uStack_e1c,0);
    writePageParamSet(param_1,"%d,",auStack_e18,1);
    if (((auStack_e18[2] - 7 < 2) || (auStack_e18[2] == 3)) || (auStack_e18[2] == 6)) {
      puVar8 = httpGetEnv(param_1,"iMAC");
      if (puVar8 == 0) {
        
      }
      writePageParamSet(param_1,""%s",",puVar8,0);
      puVar8 = httpGetEnv(param_1,"iSSID");
      if (puVar8 == 0) {
        
      }
      writePageParamSet(param_1,""%s",",puVar8,0);
      puVar8 = httpGetEnv(param_1,"iWdsChan");
      if (puVar8 == 0) {
        
      }
      writePageParamSet(param_1,""%s",",puVar8,0);
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "mptBssid");
    for (uStack_e1c = 0; uStack_e1c < 4; uStack_e1c = uStack_e1c + 1) {
      writePageParamSet(param_1,""%s",",auStack_d24 + uStack_e1c * ",",uStack_e1c);
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "siteList");
    pcStack_3c = acStack_de8;
    pcStack_38 = acStack_dd4;
    puStack_34 = &uStack_bc8;
    puStack_30 = &uStack_e24;
    puStack_2c = &uStack_e20;
    for (uStack_e1c = 0; uStack_e1c < uVar11; uStack_e1c = uStack_e1c + 1) {
      if ((iVar1 != 0) ||
         ((uStack_e24 = (uint)*(ushort *)(&bStack_b9d + uStack_e1c * "/"),
          12 < uStack_e24 - "4" && ("(" < uStack_e24 - 100)))) {
        iVar2 = uStack_e1c * ".";
        sprintf(pcStack_3c,"%02X-%02X-%02X-%02X-%02X-%02X",abStack_bc5[iVar2],
                abStack_bc5[iVar2 + 1],abStack_bc5[(uStack_e1c * 0x00000018) * 2],
                abStack_bc5[iVar2 + 3],abStack_bc5[iVar2 + 4],
                abStack_bc5[iVar2 + 5]);
        strncpy(pcStack_38,pcStack_3c,18);
        writePageParamSet(param_1,""%s",",pcStack_38,0);
        strncpy(acStack_da8,puStack_34 + uStack_e1c * "8","!");
        writePageParamSet(param_1,""%s",",acStack_da8,1);
        uStack_e28 = (uint)(&bStack_b9d)[uStack_e1c * 46];
        writePageParamSet(param_1,"%d,",&uStack_e28,2);
        if ((iVar1 == 4) || (iVar1 == 7)) {
          uStack_e24 = (int)(*(ushort *)(&bStack_b9d + uStack_e1c * "/") - 0x967) / 5;
          if (14 < uStack_e24) {
            
          }
        }
        else {
          uStack_e24 = (uint)*(ushort *)(&bStack_b9d + uStack_e1c * "/");
        }
        writePageParamSet(param_1,"%d,",puStack_30,3);
        uStack_e20 = (uint)(&bStack_b9d)[uStack_e1c * 0x00000031];
        writePageParamSet(param_1,"%d,",puStack_2c,4);
      }
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    memset(acStack_d84,0,"D");
    uStack_e1c = 0;
    pcVar9 = httpGetEnv(param_1,"ssid");
    if (pcVar9 == 0) {
      acStack_d84[0] = '\0';
    }
    else {
      __n = strlen(pcVar9);
      strncpy(acStack_d84,pcVar9,__n);
    }
    pcVar9 = httpGetEnv(param_1,"curRegion");
    if (pcVar9 == 0) {
      
    }
    else {
      uStack_e1c = atoi(pcVar9);
      if (uStack_e1c < "l") {
        uStack_d60 = uStack_e1c;
      }
    }
    pcVar9 = httpGetEnv(param_1,"channel");
    if (pcVar9 == 0) {
      uStack_d5c = 6;
    }
    else {
      uStack_e1c = atoi(pcVar9);
      if (uStack_e1c - 1 < 15) {
        uStack_d5c = uStack_e1c;
      }
    }
    pcVar9 = httpGetEnv(param_1,"chanWidth");
    if (pcVar9 == 0) {
      uStack_d58 = 2;
    }
    else {
      uStack_e1c = atoi(pcVar9);
      if (uStack_e1c - 1 < 3) {
        uStack_d58 = uStack_e1c;
      }
    }
    pcVar9 = httpGetEnv(param_1,"mode");
    if (pcVar9 == 0) {
      auStack_d54[0] = 1;
    }
    else {
      uStack_e1c = atoi(pcVar9);
      if (uStack_e1c - 1 < 8) {
        auStack_d54[0] = uStack_e1c;
      }
    }
    pcVar9 = httpGetEnv(param_1,"wrr");
    if (pcVar9 != 0) {
      iVar1 = strcmp(pcVar9,"true");
      if ((iVar1 == 0) || (iVar1 = atoi(pcVar9), iVar1 == 1)) {
        auStack_d54[1] = 1;
      }
      else {
        auStack_d54[1] = 0;
      }
    }
    pcVar9 = httpGetEnv(param_1,"s");
    if (pcVar9 != 0) {
      iVar1 = strcmp(pcVar9,"true");
      if ((iVar1 == 0) || (iVar1 = atoi(pcVar9), iVar1 == 1)) {
        auStack_d54[2] = 1;
      }
      else {
        auStack_d54[2] = 0;
      }
    }
    pcVar9 = httpGetEnv(param_1,"select");
    if (pcVar9 != 0) {
      iVar1 = strcmp(pcVar9,"true");
      if ((iVar1 == 0) || (iVar1 = atoi(pcVar9), iVar1 == 1)) {
        auStack_d54[3] = 1;
      }
      else {
        auStack_d54[3] = 0;
      }
    }
    pcVar9 = httpGetEnv(param_1,"rate");
    if (pcVar9 != 0) {
      auStack_d54[4] = atoi(pcVar9);
    }
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "pagePara");
    writePageParamSet(param_1,""%s",",acStack_d84,0);
    writePageParamSet(param_1,"%d,",&uStack_d60,1);
    writePageParamSet(param_1,"%d,",&uStack_d5c,2);
    writePageParamSet(param_1,"%d,",&uStack_d58,3);
    writePageParamSet(param_1,"%d,",auStack_d54,4);
    writePageParamSet(param_1,"%d,",auStack_d54 + 1,5);
    writePageParamSet(param_1,"%d,",auStack_d54 + 2,6);
    writePageParamSet(param_1,"%d,",auStack_d54 + 3,7);
    writePageParamSet(param_1,"%d,",auStack_d54 + 4,8);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,"<script language=JavaScript>\nvar isInScanning = 0;\n</script>");
    if ((auStack_e18[2] < 9) && ((1 << (auStack_e18[2] & 31) & 0x1c8U) != 0)) {
      HttpWebV4Head(param_1,0,0);
      pcVar9 = "/userRpm/popupSiteSurveyRpm_AP.htm";
    }
    else {
      HttpWebV4Head(param_1,0,1);
      pcVar9 = "/userRpm/popupSiteSurveyRpm.htm";
    }
  }
  iVar1 = httpRpmFsA(param_1,pcVar9);
  if (iVar1 == 2) {
    return 2;
  }
  sVar10 = HttpErrorPage(param_1,10,0,0);
LAB_0045fa54:
  return sVar10;
}

