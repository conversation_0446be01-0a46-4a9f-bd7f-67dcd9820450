
/* WARNING: Type propagation algorithm not settling */

undefined4 FUN_00436d70(void)

{
  int iVar1;
  int iVar2;
  int iVar3;
  void *__ptr;
  int local_44;
  int local_40;
  int local_3c;
  int local_38;
  int local_34 [11];
  
  local_34[1] = 0;
  local_34[2] = 0;
  local_34[3] = 0;
  local_34[4] = 0;
  local_34[5] = 0;
  local_34[6] = 0;
  local_34[7] = 0;
  local_34[8] = 0;
  local_34[9] = 0;
  local_34[10] = 0;
  apmib_get(0x1b59,&local_44);
  apmib_get(229,&local_40);
  apmib_get(195,&local_3c);
  apmib_get(246,&local_38);
  apmib_get(245,local_34);
  if (local_44 == 1) {
    memcpy(local_34 + 1,"true",5);
  }
  else {
    memcpy(local_34 + 1,"false",6);
  }
  if (local_40 < 1) {
    memcpy(local_34 + 3,"false",6);
  }
  else {
    memcpy(local_34 + 3,"true",5);
  }
  if (local_3c == 1) {
    memcpy(local_34 + 5,"true",5);
  }
  else {
    memcpy(local_34 + 5,"false",6);
  }
  if (local_38 == 1) {
    memcpy(local_34 + 7,"true",5);
  }
  else {
    memcpy(local_34 + 7,"false",6);
  }
  if (local_34[0] == 1) {
    memcpy(local_34 + 9,"true",5);
  }
  else {
    memcpy(local_34 + 9,"false",6);
  }
  iVar1 = mxmlNewXML("1.0");
  if (iVar1 == 0) {
    puts("Create new xml erro!!!");
  }
  else {
    iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
    if (iVar2 == 0) {
      puts("Create new element error!!!");
    }
    else {
      mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
      mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
      mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
      iVar2 = mxmlNewElement(iVar2,"soap:Body");
      if (iVar2 == 0) {
        puts("Create new element error!!!");
        mxmlDelete(iVar1);
      }
      else {
        iVar2 = mxmlNewElement(iVar2,"GetFirewallSettingsResponse");
        if (iVar2 == 0) {
          puts("Create new element error!!!");
          mxmlDelete(iVar1);
        }
        else {
          mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
          iVar3 = mxmlNewElement(iVar2,"GetFirewallSettingsResult");
          if (iVar3 == 0) {
            puts("Create new element error!!!");
            mxmlDelete(iVar1);
          }
          else {
            iVar3 = mxmlNewText(iVar3,0,"O");
            if (iVar3 == 0) {
              puts("Create new text error!!!");
              mxmlDelete(iVar1);
            }
            else {
              iVar3 = mxmlNewElement(iVar2,"SPIIPv4");
              if (iVar3 == 0) {
                puts("Create new element error!!!");
                mxmlDelete(iVar1);
              }
              else {
                iVar3 = mxmlNewText(iVar3,0,local_34 + 1);
                if (iVar3 == 0) {
                  puts("Create new text error!!!");
                  mxmlDelete(iVar1);
                }
                else {
                  iVar3 = mxmlNewElement(iVar2,"AntiSpoof");
                  if (iVar3 == 0) {
                    puts("Create new element error!!!");
                    mxmlDelete(iVar1);
                  }
                  else {
                    iVar3 = mxmlNewText(iVar3,0,local_34 + 3);
                    if (iVar3 == 0) {
                      puts("Create new text error!!!");
                      mxmlDelete(iVar1);
                    }
                    else {
                      iVar3 = mxmlNewElement(iVar2,"WANPing");
                      if (iVar3 == 0) {
                        puts("Create new element error!!!");
                        mxmlDelete(iVar1);
                      }
                      else {
                        iVar3 = mxmlNewText(iVar3,0,local_34 + 5);
                        if (iVar3 == 0) {
                          puts("Create new text error!!!");
                          mxmlDelete(iVar1);
                        }
                        else {
                          iVar3 = mxmlNewElement(iVar2,"ALGPPTP");
                          if (iVar3 == 0) {
                            puts("Create new element error!!!");
                            mxmlDelete(iVar1);
                          }
                          else {
                            iVar3 = mxmlNewText(iVar3,0,local_34 + 7);
                            if (iVar3 == 0) {
                              puts("Create new text error!!!");
                              mxmlDelete(iVar1);
                            }
                            else {
                              iVar3 = mxmlNewElement(iVar2,"ALGIPSec");
                              if (iVar3 == 0) {
                                puts("Create new element error!!!");
                                mxmlDelete(iVar1);
                              }
                              else {
                                iVar3 = mxmlNewText(iVar3,0,local_34 + 9);
                                if (iVar3 == 0) {
                                  puts("Create new text error!!!");
                                  mxmlDelete(iVar1);
                                }
                                else {
                                  iVar3 = mxmlNewElement(iVar2,"ALGRTSP");
                                  if (iVar3 == 0) {
                                    puts("Create new element error!!!");
                                    mxmlDelete(iVar1);
                                  }
                                  else {
                                    iVar3 = mxmlNewText(iVar3,0,"false");
                                    if (iVar3 == 0) {
                                      puts("Create new text error!!!");
                                      mxmlDelete(iVar1);
                                    }
                                    else {
                                      iVar2 = mxmlNewElement(iVar2,"ALGRSPI");
                                      if (iVar2 == 0) {
                                        puts("Create new element error!!!");
                                        mxmlDelete(iVar1);
                                      }
                                      else {
                                        iVar2 = mxmlNewText(iVar2,0,"false");
                                        if (iVar2 == 0) {
                                          puts("Create new text error!!!");
                                          mxmlDelete(iVar1);
                                        }
                                        else {
                                          __ptr = mxmlSaveAllocString(iVar1,0);
                                          if (__ptr != 0) {
                                            FUN_0041ed70("",200,__ptr,"");
                                            free(__ptr);
                                          }
                                          mxmlDelete(iVar1);
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

