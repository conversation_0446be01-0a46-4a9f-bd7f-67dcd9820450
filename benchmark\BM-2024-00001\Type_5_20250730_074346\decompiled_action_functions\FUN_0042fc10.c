
int FUN_0042fc10(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  short sVar5;
  char *pcVar3;
  uint32_t uVar4;
  char *__src;
  undefined4 uVar6;
  int iVar7;
  uint uVar8;
  code *pcVar9;
  uint local_680;
  uint local_67c;
  uint32_t local_678;
  uint32_t local_674;
  uint32_t local_670;
  uint32_t local_66c;
  uint32_t local_668;
  undefined auStack_664 [8];
  char acStack_65c [32];
  undefined *local_63c;
  undefined4 local_638;
  undefined *local_634;
  undefined4 local_630;
  undefined *local_62c;
  undefined4 local_628;
  undefined *local_624;
  undefined4 local_620;
  undefined *local_61c;
  undefined4 local_618;
  undefined *local_614;
  undefined4 local_610;
  undefined *local_60c;
  undefined4 local_608;
  undefined *local_604;
  undefined4 local_600;
  undefined *local_5fc;
  undefined4 local_5f8;
  undefined *local_5f4;
  undefined4 local_5f0;
  undefined *local_5ec;
  undefined4 local_5e8;
  undefined *local_5e4;
  undefined4 local_5e0;
  undefined *local_5dc;
  undefined4 local_5d8;
  undefined *local_5d4;
  undefined4 local_5d0;
  undefined *local_5cc;
  undefined4 local_5c8;
  undefined *local_5c4;
  undefined4 local_5c0;
  undefined *local_5bc;
  undefined4 local_5b8;
  undefined *local_5b4;
  undefined4 local_5b0;
  undefined *local_5ac;
  undefined4 local_5a8;
  undefined *local_5a4;
  undefined4 local_5a0;
  undefined *local_59c;
  undefined4 local_598;
  undefined *local_594;
  undefined4 local_590;
  undefined *local_58c;
  undefined4 local_588;
  undefined *local_584;
  undefined4 local_580;
  undefined *local_57c;
  undefined4 local_578;
  undefined *local_574;
  undefined4 local_570;
  undefined *local_56c;
  undefined4 local_568;
  undefined *local_564;
  undefined4 local_560;
  undefined4 local_55c;
  undefined auStack_554 [8];
  uint local_54c;
  in_addr_t local_548;
  in_addr_t local_544;
  in_addr_t local_540;
  in_addr_t local_53c;
  char acStack_538 [63];
  undefined local_4f9;
  undefined auStack_4f8 [119];
  undefined local_481;
  undefined auStack_480 [119];
  undefined local_409;
  uint32_t local_408;
  uint32_t local_3f8;
  uint32_t local_3f4;
  uint local_3f0;
  uint local_3ec;
  uint local_3e8;
  undefined auStack_3e4 [364];
  int local_278;
  undefined auStack_274 [4];
  undefined auStack_270 [4];
  undefined auStack_26c [4];
  undefined auStack_268 [4];
  undefined auStack_264 [64];
  undefined auStack_224 [120];
  undefined auStack_1ac [120];
  undefined auStack_134 [4];
  undefined auStack_130 [4];
  undefined auStack_12c [16];
  undefined auStack_11c [16];
  undefined auStack_10c [16];
  undefined auStack_fc [4];
  undefined auStack_f8 [4];
  undefined auStack_f4 [4];
  undefined auStack_f0 [4];
  undefined auStack_ec [4];
  undefined auStack_e8 [16];
  undefined auStack_d8 [16];
  undefined auStack_c8 [16];
  undefined auStack_b8 [16];
  undefined auStack_a8 [16];
  undefined auStack_98 [16];
  undefined auStack_88 [16];
  undefined auStack_78 [16];
  undefined auStack_68 [4];
  undefined auStack_64 [64];
  undefined auStack_24 [8];
  
  local_63c = auStack_274;
  local_634 = auStack_270;
  local_62c = auStack_26c;
  local_624 = auStack_268;
  local_61c = auStack_264;
  local_614 = auStack_224;
  local_60c = auStack_1ac;
  local_604 = auStack_134;
  local_5fc = auStack_130;
  local_5f4 = auStack_12c;
  local_5ec = auStack_11c;
  local_5e4 = auStack_10c;
  local_5dc = auStack_fc;
  local_5d4 = auStack_f4;
  local_5cc = auStack_f8;
  local_5c4 = auStack_f0;
  local_5bc = auStack_ec;
  local_5b4 = auStack_e8;
  local_5ac = auStack_d8;
  local_5a4 = auStack_c8;
  local_59c = auStack_b8;
  local_594 = auStack_a8;
  local_58c = auStack_98;
  local_584 = auStack_88;
  local_57c = auStack_78;
  local_574 = auStack_68;
  local_56c = auStack_64;
  local_564 = auStack_24;
  local_66c = 0;
  local_668 = 0;
  local_680 = 0;
  local_55c = 0;
  local_638 = 0;
  local_630 = 0;
  local_568 = 16;
  local_5f0 = 16;
  local_5e8 = 16;
  local_5e0 = 16;
  local_5b0 = 16;
  local_5a8 = 16;
  local_5a0 = 16;
  local_598 = 16;
  local_590 = 16;
  local_588 = 16;
  local_580 = 16;
  local_578 = 16;
  local_608 = "x";
  local_610 = "x";
  local_618 = "@";
  local_628 = 0;
  local_620 = 0;
  local_600 = 0;
  local_5f8 = 0;
  local_5d8 = 0;
  local_5d0 = 0;
  local_5c8 = 0;
  local_5c0 = 0;
  local_5b8 = 0;
  local_570 = 0;
  local_560 = 0;
  memset(auStack_554,0,368);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    sVar5 = HttpDenyPage(param_1);
    goto LAB_00430dc0;
  }
  swGetL2tpUcCfg(auStack_554);
  iVar2 = httpGetEnv(param_1,"Connect");
  if ((iVar2 == 0) && (iVar2 = httpGetEnv(param_1,"Save"), iVar2 == 0)) {
    iVar2 = httpGetEnv(param_1,"Disconnect");
    uVar8 = 0;
    if (iVar2 != 0) {
      pcVar9 = swL2tpLinkDownReq;
LAB_004303f0:
      uVar8 = 1;
      (*pcVar9)();
      taskDelay("<");
    }
LAB_00430418:
    swL2tpLinkStateGet(&local_680);
    swGetL2tpUcCfg(auStack_554);
    if (((local_54c == 0) || (iVar2 = swDhcpcStateIsSucceed(0), iVar2 == 0)) || (local_680 == 0)) {
      local_678 = 0;
      local_674 = 0;
      local_670 = 0;
      local_66c = 0;
      local_668 = 0;
    }
    else {
      swGetDhcpcNetInfo(0,&local_678,&local_674,&local_670,&local_66c,&local_668);
    }
    local_67c = 1;
    pageParaSet(&local_63c,&local_67c,0);
    local_67c = 0;
    pageParaSet(&local_63c,&local_67c,1);
    local_67c = 6;
    pageParaSet(&local_63c,&local_67c,2);
    pcVar3 = httpGetEnv(param_1,"IpType");
    if (pcVar3 != 0) {
      iVar2 = atoi(pcVar3);
      local_54c = (uint)(iVar2 == 0);
    }
    local_67c = (uint)(local_54c == 0);
    pageParaSet(&local_63c,&local_67c,3);
    pageParaSet(&local_63c,acStack_538,4);
    pageParaSet(&local_63c,auStack_4f8,5);
    pageParaSet(&local_63c,auStack_480,6);
    local_67c = (uint)(local_680 != 0);
    pageParaSet(&local_63c,&local_67c,7);
    if (local_680 == 1) {
      local_67c = local_680;
    }
    else if (local_680 == 0) {
      local_67c = 0;
    }
    else {
      local_67c = 2;
    }
    pageParaSet(&local_63c,&local_67c,8);
    local_67c = ntohl(local_678);
    sprintf(acStack_65c,"%d.%d.%d.%d",local_67c >> 24,local_67c >> 16 & 255,
            local_67c >> 8 & 255,local_67c & 255);
    pageParaSet(&local_63c,acStack_65c,9);
    local_67c = ntohl(local_674);
    sprintf(acStack_65c,"%d.%d.%d.%d",local_67c >> 24,local_67c >> 16 & 255,
            local_67c >> 8 & 255,local_67c & 255);
    pageParaSet(&local_63c,acStack_65c,10);
    local_67c = ntohl(local_670);
    sprintf(acStack_65c,"%d.%d.%d.%d",local_67c >> 24,local_67c >> 16 & 255,
            local_67c >> 8 & 255,local_67c & 255);
    pageParaSet(&local_63c,acStack_65c,11);
    local_67c = ntohl(local_548);
    sprintf(acStack_65c,"%d.%d.%d.%d",local_67c >> 24,local_67c >> 16 & 255,
            local_67c >> 8 & 255,local_67c & 255);
    pageParaSet(&local_63c,acStack_65c,18);
    local_67c = ntohl(local_544);
    sprintf(acStack_65c,"%d.%d.%d.%d",local_67c >> 24,local_67c >> 16 & 255,
            local_67c >> 8 & 255,local_67c & 255);
    pageParaSet(&local_63c,acStack_65c,19);
    local_67c = ntohl(local_540);
    sprintf(acStack_65c,"%d.%d.%d.%d",local_67c >> 24,local_67c >> 16 & 255,
            local_67c >> 8 & 255,local_67c & 255);
    pageParaSet(&local_63c,acStack_65c,20);
    local_67c = ntohl(local_53c);
    sprintf(acStack_65c,"%d.%d.%d.%d",local_67c >> 24,local_67c >> 16 & 255,
            local_67c >> 8 & 255,local_67c & 255);
    pageParaSet(&local_63c,acStack_65c,26);
    local_66c = ntohl(local_66c);
    sprintf(acStack_65c,"%d.%d.%d.%d",local_66c >> 24,local_66c >> 16 & 255,
            local_66c >> 8 & 255,local_66c & 255);
    pageParaSet(&local_63c,acStack_65c,23);
    local_668 = ntohl(local_668);
    sprintf(acStack_65c,"%d.%d.%d.%d",local_668 >> 24,local_668 >> 16 & 255,
            local_668 >> 8 & 255,local_668 & 255);
    pageParaSet(&local_63c,acStack_65c,24);
    local_67c = local_3f0;
    pageParaSet(&local_63c,&local_67c,12);
    local_67c = local_3ec;
    pageParaSet(&local_63c,&local_67c,13);
    local_67c = local_3e8;
    pageParaSet(&local_63c,&local_67c,14);
    local_67c = 0;
    pageParaSet(&local_63c,&local_67c,15);
    pageParaSet(&local_63c,&local_67c,16);
    swGetL2tpCfg(auStack_554);
    local_67c = ntohl(local_408);
    sprintf(acStack_65c,"%d.%d.%d.%d",local_67c >> 24,local_67c >> 16 & 255,
            local_67c >> 8 & 255,local_67c & 255);
    pageParaSet(&local_63c,acStack_65c,17);
    local_67c = ntohl(local_3f8);
    uVar4 = ntohl(local_3f4);
    sprintf(acStack_65c,"%d.%d.%d.%d",local_67c >> 24,local_67c >> 16 & 255,
            local_67c >> 8 & 255,local_67c & 255);
    pageParaSet(&local_63c,acStack_65c,21);
    sprintf(acStack_65c,"%d.%d.%d.%d",uVar4 >> 24,uVar4 >> 16 & 255,uVar4 >> 8 & 255,
            uVar4 & 255);
    pageParaSet(&local_63c,acStack_65c,22);
    if ((uVar8 == 0) &&
       (((local_54c == 1 && (local_548 == 0)) ||
        ((1 < local_680 || ((local_3e8 - 1 < 2 && (l2tpIpMaskGet(&local_67c,0), local_67c == 0))))))
       )) {
      uVar8 = 1;
    }
    local_67c = uVar8;
    pageParaSet(&local_63c,&local_67c,25);
    swGetSystemMode(auStack_664);
    local_67c = 3;
    pageParaSet(&local_63c,&local_67c,27);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "l2tpInf");
    iVar2 = 0;
    do {
      iVar7 = iVar2 + 1;
      pageDynParaPrintf(&local_63c,iVar2,param_1);
      iVar2 = iVar7;
    } while (iVar7 != 28);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintfWanTypeInfo(param_1);
    HttpWebV4Head(param_1,0,1);
    iVar2 = httpRpmFsA(param_1,"/userRpm/L2TPCfgRpm.htm");
    if (iVar2 == 2) {
      return 2;
    }
    iVar2 = 10;
    pcVar3 = 0;
  }
  else {
    iVar2 = httpGetEnv(param_1,"L2TPName");
    if (iVar2 == 0) {
      pcVar9 = memset;
      iVar2 = 0;
      uVar6 = "x";
    }
    else {
      pcVar9 = strncpy;
      local_481 = 0;
      uVar6 = "w";
    }
    (*pcVar9)(auStack_4f8,iVar2,uVar6);
    iVar2 = httpGetEnv(param_1,"L2TPPwd");
    if (iVar2 == 0) {
      pcVar9 = memset;
      iVar2 = 0;
      uVar6 = "x";
    }
    else {
      pcVar9 = strncpy;
      local_409 = 0;
      uVar6 = "w";
    }
    (*pcVar9)(auStack_480,iVar2,uVar6);
    pcVar3 = httpGetEnv(param_1,"IpType");
    if (pcVar3 == 0) {
      local_54c = 1;
    }
    else {
      iVar2 = atoi(pcVar3);
      local_54c = (uint)(iVar2 == 0);
    }
    pcVar3 = httpGetEnv(param_1,"L2TPServerName");
    if (pcVar3 != 0) {
      do {
        __src = pcVar3;
        pcVar3 = __src + 1;
      } while (*__src == ' ');
      local_4f9 = 0;
      strncpy(acStack_538,__src,"?");
    }
    if (local_54c == 0) {
      pcVar3 = httpGetEnv(param_1,"sta_ip");
      iVar2 = swChkDotIpAddr(pcVar3);
      if (iVar2 == 0) {
        pcVar3 = "";
        iVar2 = 0x138b;
      }
      else {
        local_548 = inet_addr(pcVar3);
        pcVar3 = httpGetEnv(param_1,"sta_mask");
        iVar2 = swChkDotIpAddr(pcVar3);
        if (iVar2 == 0) {
          pcVar3 = "";
          iVar2 = 0x138c;
        }
        else {
          local_544 = inet_addr(pcVar3);
          pcVar3 = httpGetEnv(param_1,"sta_gw");
          iVar2 = swChkDotIpAddr(pcVar3);
          if (iVar2 == 0) {
            pcVar3 = "";
            iVar2 = 0x138f;
          }
          else {
            local_540 = inet_addr(pcVar3);
            pcVar3 = httpGetEnv(param_1,"sta_dns");
            if (pcVar3 == 0) {
              pcVar3 = "0.0.0.0";
            }
            iVar2 = swChkDotIpAddr(pcVar3);
            if (iVar2 != 0) {
              local_53c = inet_addr(pcVar3);
              goto LAB_00430164;
            }
            pcVar3 = "";
            iVar2 = 0x138d;
          }
        }
      }
    }
    else {
LAB_00430164:
      pcVar3 = httpGetEnv(param_1,"mtu");
      if (pcVar3 != 0) {
        local_3f0 = atoi(pcVar3);
      }
      pcVar3 = httpGetEnv(param_1,"idletime");
      if (pcVar3 != 0) {
        local_3ec = atoi(pcVar3);
      }
      pcVar3 = httpGetEnv(param_1,"linktype");
      if (pcVar3 != 0) {
        local_3e8 = atoi(pcVar3);
      }
      if (local_54c == 0) {
        pcVar9 = swChkL2tpCfg;
      }
      else {
        pcVar9 = swChkL2tpDomain;
      }
      sVar5 = (*pcVar9)(auStack_554);
      iVar2 = sVar5;
      pcVar3 = acStack_65c;
      if (iVar2 == 0) {
        if (local_54c != 0) {
          dhcpcSetDhcpcAutoDnsEnabled(7);
        }
        swGetL2tpCfg(auStack_3e4);
        iVar2 = swL2tpIsLinkUp();
        if (((((iVar2 == 0) || (local_278 != 3)) || (bVar1 = true, local_3e8 != 3)) &&
            (((iVar2 = swL2tpIsLinkUp(), iVar2 == 0 || (local_278 != 1)) ||
             (bVar1 = true, local_3e8 != 1)))) && (bVar1 = false, local_3e8 == 2)) {
          bVar1 = true;
        }
        swSetL2tpCfg(auStack_554);
        iVar2 = httpGetEnv(param_1,"Connect");
        if (iVar2 == 0) {
          iVar2 = httpGetEnv(param_1,"Save");
          uVar8 = 1;
          if ((iVar2 == 0) || (!bVar1)) goto LAB_00430418;
        }
        swL2tpLinkDownReq();
        pcVar9 = swL2tpLinkUpReq;
        goto LAB_004303f0;
      }
      sprintf(pcVar3,"../userRpm/L2TPCfgRpm.htm?wan=%d",0);
    }
  }
  sVar5 = HttpErrorPage(param_1,iVar2,pcVar3,0);
LAB_00430dc0:
  return sVar5;
}

