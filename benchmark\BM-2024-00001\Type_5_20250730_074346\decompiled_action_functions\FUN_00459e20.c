
int FUN_00459e20(undefined4 param_1)

{
  int iVar1;
  short sVar9;
  undefined4 uVar2;
  char *pcVar3;
  char cVar10;
  size_t sVar4;
  uint uVar5;
  int iVar6;
  undefined4 uVar7;
  undefined4 uVar8;
  char *pcVar11;
  char *pcVar12;
  code *pcVar13;
  undefined4 local_b08;
  undefined4 local_b04;
  char *local_b00;
  int local_afc;
  int local_af8;
  undefined4 local_af4;
  undefined4 local_af0;
  int local_aec;
  char acStack_ae8 [16];
  char acStack_ad8 [16];
  char acStack_ac8 [16];
  undefined *local_ab8;
  undefined4 local_ab4;
  undefined4 local_ab0;
  undefined *local_aa8;
  undefined4 local_aa4;
  undefined4 local_aa0;
  char acStack_a98 [20];
  int *local_a84;
  undefined4 local_a80;
  char *local_a7c;
  undefined4 local_a78;
  undefined4 local_a74;
  int *local_a6c;
  undefined4 local_a68;
  int *local_a64;
  undefined4 local_a60;
  undefined4 *local_a5c;
  undefined4 local_a58;
  undefined4 *local_a54;
  undefined4 local_a50;
  undefined4 local_a4c;
  undefined *local_a44;
  undefined4 local_a40;
  undefined *local_a3c;
  undefined4 local_a38;
  undefined *local_a34;
  undefined4 local_a30;
  undefined *local_a2c;
  undefined4 local_a28;
  undefined4 local_a24;
  undefined4 local_a1c;
  undefined4 local_a18;
  undefined4 local_a14;
  undefined4 local_a10;
  int local_a0c;
  undefined4 local_a08;
  sysinfo local_9f0;
  undefined auStack_9b0 [128];
  char acStack_930 [255];
  char acStack_831 [257];
  undefined auStack_730 [256];
  char acStack_630 [540];
  undefined auStack_414 [128];
  undefined auStack_394 [540];
  undefined auStack_178 [64];
  undefined auStack_138 [256];
  undefined4 *local_38;
  undefined4 *local_34;
  char **local_30;
  
  local_ab8 = auStack_730;
  local_aa8 = auStack_9b0;
  local_a44 = auStack_414;
  local_a3c = auStack_394;
  local_a34 = auStack_178;
  local_a2c = auStack_138;
  local_a38 = 540;
  local_a30 = "@";
  local_a6c = &local_afc;
  local_a64 = &local_af8;
  local_a5c = &local_af4;
  local_a54 = &local_af0;
  local_a84 = &local_aec;
  local_a7c = acStack_a98;
  local_a28 = 256;
  local_ab4 = 256;
  local_a40 = 128;
  local_a78 = 20;
  local_aa4 = 128;
  local_afc = 0;
  local_af8 = 0;
  local_af0 = 0;
  local_aec = 0;
  local_ab0 = 0;
  local_aa0 = 0;
  local_a24 = 0;
  local_a4c = 0;
  local_a68 = 0;
  local_a60 = 0;
  local_a58 = 0;
  local_a50 = 0;
  local_a74 = 0;
  local_a80 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar9 = HttpDenyPage(param_1);
  }
  else {
    iVar1 = httpGetEnv(param_1,"ClearLog");
    if (iVar1 != 0) {
      puts("before clear log");
      swSysLogClear();
    }
    iVar1 = httpGetEnv(param_1,"doMailLog");
    if (iVar1 != 0) {
      iVar1 = FUN_004593cc(param_1);
      return iVar1;
    }
    local_af4 = ucGetAutoAvilableCfg();
    iVar1 = httpGetEnv(param_1,"logType");
    if ((iVar1 == 0) || (local_afc = getEnvToInt(param_1,"logType",0,11), local_afc == -128)) {
      local_afc = 0;
    }
    iVar1 = httpGetEnv(param_1,"logLevel");
    if ((iVar1 == 0) || (local_af8 = getEnvToInt(param_1,"logLevel",0,7), local_af8 == -128)) {
      local_af8 = 7;
    }
    iVar1 = httpGetEnv(param_1,"pageNum");
    uVar2 = 1;
    if (iVar1 != 0) {
      uVar2 = getEnvToInt(param_1,"pageNum",1,20);
    }
    local_af0 = uVar2;
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "syslogWebConf");
    pageDynParaListPrintf(&local_a6c,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "logList");
    pcVar3 = swLogRead(local_afc,local_af8,0);
    if ((pcVar3 != 0) && (pcVar11 = pcVar3, *pcVar3 != '\0')) {
      do {
        cVar10 = *pcVar11;
        do {
          pcVar12 = pcVar11;
          if (cVar10 == '\0') {
            pcVar12 = pcVar12 + -2;
            goto LAB_0045a1e4;
          }
          cVar10 = pcVar12[1];
          pcVar11 = pcVar12 + 1;
        } while (cVar10 != '\0');
        pcVar11 = pcVar12 + 2;
      } while (*pcVar11 != '\0');
LAB_0045a1e4:
      while (pcVar11 = pcVar12, pcVar11 != pcVar3) {
        pcVar12 = pcVar11 + -1;
        if (*pcVar12 == '\0') {
          sVar4 = strlen(pcVar11);
          strncpy(acStack_831 + 1,pcVar11,sVar4 - 1);
          acStack_831[sVar4] = '\0';
          pageParaSet(&local_ab8,acStack_831 + 1,0);
          pageDynParaListPrintf(&local_ab8,param_1);
        }
      }
      sVar4 = strlen(pcVar11);
      strncpy(acStack_831 + 1,pcVar11,sVar4 - 1);
      acStack_831[sVar4] = '\0';
      pageParaSet(&local_ab8,acStack_831 + 1,0);
      pageDynParaListPrintf(&local_ab8,param_1);
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "logExpList");
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "logWanList");
    local_34 = &local_b08;
    local_38 = &local_b04;
    local_30 = &local_b00;
    iVar1 = 0;
    while (iVar6 = getMaxWanPortNumber(), iVar1 < iVar6) {
      uVar5 = swGetWanType(iVar1);
      if (7 < uVar5) {
        uVar5 = 8;
      }
      iVar6 = swGetWanIpMask(iVar1,local_34,local_38);
      if (iVar6 == 0) {
        inet_ntoa_b(local_b08,acStack_ae8);
        inet_ntoa_b(local_b04,acStack_ad8);
        swGetGatewayMask(iVar1,local_30,local_38);
        pcVar13 = inet_ntoa_b;
        pcVar3 = local_b00;
        pcVar11 = acStack_ac8;
      }
      else {
        strcpy(acStack_ae8,"0.0.0.0");
        strcpy(acStack_ad8,"0.0.0.0");
        pcVar11 = "0.0.0.0";
        pcVar13 = strcpy;
        pcVar3 = acStack_ac8;
      }
      iVar1 = iVar1 + 1;
      (*pcVar13)(pcVar3,pcVar11);
      sprintf(acStack_930,"W%d = %s : W = %s : M = %s : G = %s",iVar1,(&PTR_0x0056a630)[uVar5],
              acStack_ae8,acStack_ad8,acStack_ac8);
      pageParaSet(&local_aa8,acStack_930,0);
      pageDynParaListPrintf(&local_aa8,param_1);
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "logInf");
    sysinfo(&local_9f0);
    swGetSntpSystemTime(&local_a1c);
    sprintf(acStack_930,"Time = %04d-%02d-%02d %2d:%02d:%02d %us",local_a08,local_a0c + 1,local_a10,
            local_a14,local_a18,local_a1c,local_9f0.uptime);
    pageParaSet(&local_a44,acStack_930,0);
    uVar2 = getSysHardwareRevision();
    uVar7 = getSysSoftwareRevisionPrefix();
    uVar8 = getSysSoftwareRevision();
    sprintf(acStack_630,"H-Ver = %s : S-Ver = %s%s",uVar2,uVar7,uVar8);
    pageParaSet(&local_a44,acStack_630,1);
    swGetLanIpMask(&local_b08,&local_b04);
    inet_ntoa_b(local_b08,acStack_ae8);
    inet_ntoa_b(local_b04,acStack_ad8);
    sprintf(acStack_930,"L = %s : M = %s",acStack_ae8,acStack_ad8);
    pageParaSet(&local_a44,acStack_930,2);
    pageParaSet(&local_a44,"",3);
    pageDynParaPrintf(&local_a44,0,param_1);
    pageDynParaPrintf(&local_a44,1,param_1);
    pageDynParaPrintf(&local_a44,2,param_1);
    pageDynParaPrintf(&local_a44,3,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "logTypeArray");
    for (iVar1 = 0; iVar6 = swGetLogTypeNum(), iVar1 < iVar6; iVar1 = iVar1 + 1) {
      iVar6 = swIsLogTypeDisabled(iVar1);
      if (iVar6 == 0) {
        local_aec = iVar1;
        pcVar3 = swGetLogTypeStr(iVar1);
        strcpy(acStack_a98,pcVar3);
        pageDynParaPrintf(&local_a84,0,param_1);
        pageDynParaPrintf(&local_a84,1,param_1);
      }
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/SystemLogRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar9 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar9;
}

