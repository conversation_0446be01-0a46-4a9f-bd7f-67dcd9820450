
int httpFirmwareUpdateTempRpmHtm(undefined4 param_1)

{
  int iVar1;
  short sVar2;
  undefined4 uVar3;
  
  iVar1 = HttpIsAccessFromLAN();
  if ((iVar1 == 0) && (iVar1 = getForbiddenWanUpgrade(), iVar1 != 0)) {
    return 2;
  }
  while (0x00000001 == 0) {
    HTTP_DEBUG_PRINT("softwareUpgrade/httpSoftUpload.c:307","softUpgradeThread waiting...%d",
                     0xffffffff);
    sleep(5);
  }
  if ((0xffffffff == 0) || (iVar1 = swGetUpgradeMode(), iVar1 != 0)) {
    if (0xffffffff == 0) {
      uVar3 = 2;
    }
    else {
      uVar3 = 10;
    }
    sVar2 = HttpRestartRpmHtm(param_1,uVar3);
    swReboot(2);
  }
  else {
    sVar2 = HttpErrorPage(param_1,0,"../userRpm/SoftwareUpgradeRpm.htm",0);
  }
  return sVar2;
}

