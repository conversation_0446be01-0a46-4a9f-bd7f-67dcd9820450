
int FUN_00420ba8(undefined4 param_1)

{
  ushort uVar1;
  undefined4 *__s;
  int iVar2;
  short sVar13;
  uint uVar3;
  int iVar4;
  int iVar5;
  undefined4 uVar6;
  char *pcVar7;
  in_addr_t iVar8;
  int iVar9;
  size_t sVar10;
  char *pcVar11;
  uint uVar12;
  uint uVar14;
  uint uVar15;
  byte local_270;
  int local_26c;
  uint local_268;
  char acStack_264 [8];
  int local_25c;
  undefined4 local_258;
  int local_254;
  undefined4 local_250;
  char local_24c [12];
  uint local_240;
  undefined auStack_23c [4];
  undefined auStack_238 [4];
  undefined auStack_234 [4];
  undefined auStack_230 [4];
  undefined auStack_22c [4];
  int *local_228;
  undefined4 local_224;
  undefined4 *local_220;
  undefined4 local_21c;
  undefined4 local_218;
  undefined4 local_210;
  char acStack_20c [24];
  undefined local_1f4;
  undefined local_1f3;
  char local_1f2 [9];
  undefined local_1e9;
  undefined4 local_1e8;
  undefined4 local_1e4;
  char acStack_1d8 [24];
  undefined local_1c0;
  byte local_1bf;
  byte local_1be;
  byte local_1bd;
  undefined *local_1bc;
  undefined4 local_1b8;
  undefined *local_1b4;
  undefined4 local_1b0;
  undefined *local_1ac;
  undefined4 local_1a8;
  undefined *local_1a4;
  undefined4 local_1a0;
  undefined *local_19c;
  undefined4 local_198;
  undefined4 local_194;
  undefined4 local_18c;
  int local_188;
  char acStack_184 [24];
  undefined local_16c;
  in_addr_t local_168;
  in_addr_t local_164;
  undefined auStack_160 [8];
  undefined *local_158;
  undefined4 local_154;
  uint *local_150;
  undefined4 local_14c;
  uint *local_148;
  undefined4 local_144;
  uint *local_140;
  undefined4 local_13c;
  undefined *local_138;
  undefined4 local_134;
  undefined *local_130;
  undefined4 local_12c;
  undefined *local_128;
  undefined4 local_124;
  undefined4 *local_120;
  undefined4 local_11c;
  undefined4 local_118;
  undefined auStack_110 [25];
  undefined auStack_f7 [25];
  undefined auStack_de [26];
  undefined auStack_c4 [28];
  uint local_a8;
  uint local_a4;
  uint local_a0;
  undefined auStack_9c [25];
  undefined auStack_83 [25];
  undefined auStack_6a [26];
  undefined4 local_50 [2];
  uint local_48;
  undefined *local_44;
  char *local_40;
  undefined *local_3c;
  undefined *local_38;
  undefined *local_34;
  undefined *local_30;
  undefined *local_2c;
  
  local_268 = 0;
  swGetAccessCtrlRulesTableSize();
  __s = malloc(176);
  if (__s == 0) {
    printf("\n%s %d not enough memory to malloc\n","AccessRulesRpmHtm",385);
    return -1;
  }
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    free(__s);
    sVar13 = HttpDenyPage(param_1);
    goto LAB_004224c0;
  }
  iVar2 = httpGetEnv(param_1,"Add");
  if (iVar2 != 0) {
    free(__s);
    iVar2 = FUN_004205d8(param_1);
    return iVar2;
  }
  iVar2 = httpGetEnv(param_1,"Add");
  if ((iVar2 != 0) || (iVar2 = httpGetEnv(param_1,"Modify"), iVar2 != 0)) {
    free(__s);
    iVar2 = FUN_0041fabc(param_1);
    return iVar2;
  }
  memset(&local_1e8,0,",");
  local_228 = &local_25c;
  local_220 = &local_258;
  local_1bc = auStack_23c;
  local_1b4 = auStack_238;
  local_1ac = auStack_234;
  local_1a4 = auStack_230;
  local_19c = auStack_22c;
  local_158 = auStack_c4;
  local_150 = &local_a8;
  local_148 = &local_a4;
  local_140 = &local_a0;
  local_138 = auStack_9c;
  local_130 = auStack_83;
  local_128 = auStack_6a;
  local_120 = local_50;
  local_124 = 25;
  local_154 = 25;
  local_134 = 25;
  local_12c = 25;
  local_218 = 0;
  local_224 = 0;
  local_21c = 0;
  local_194 = 0;
  local_1b8 = 0;
  local_1b0 = 0;
  local_1a8 = 0;
  local_1a0 = 0;
  local_198 = 0;
  local_118 = 0;
  local_14c = 0;
  local_144 = 0;
  local_13c = 0;
  local_11c = 0;
  uVar3 = getEnvToInt(param_1,"Page",1,0x7fffffff);
  local_48 = 1;
  if (uVar3 != 0xffffff80) {
    local_48 = uVar3;
  }
  iVar2 = getEnvToInt(param_1,"enableCtrl",0,1);
  if (iVar2 != -128) {
    local_254 = iVar2;
    local_250 = getEnvToInt(param_1,"defRule",0,1);
    swSetAccessCtrlGlobalCfg(&local_254);
  }
  iVar2 = getEnvToInt(param_1,"enableId",0,7);
  if (iVar2 != -128) {
    iVar4 = getEnvToInt(param_1,"enable",0,1);
    iVar5 = swGetAccessCtrlRulesTableSize();
    iVar5 = getEnvToInt(param_1,"Page",1,iVar5 + 7U >> 3);
    if (iVar5 != -128) {
      iVar2 = iVar2 + (iVar5 + -1) * 8;
      swEnableAccessCtrlEntry(iVar2,iVar4 != 0);
    }
  }
  uVar6 = swGetAccessCtrlRulesTableSize();
  getEnvToInt(param_1,"EntryIndex",0,uVar6);
  pcVar7 = httpGetEnv(param_1,"doAll");
  if (pcVar7 == 0) {
    pcVar7 = httpGetEnv(param_1,"Del");
    if (pcVar7 != 0) {
      iVar2 = atoi(pcVar7);
      uVar6 = swDelFilterEntry(3,(local_48 - 1) * 8 + iVar2,1);
      goto LAB_00422028;
    }
    iVar4 = httpGetEnv(param_1,"Save");
    if (iVar4 == 0) {
      iVar2 = httpGetEnv(param_1,"moveItem");
      if (iVar2 != 0) {
        pcVar7 = httpGetEnv(param_1,"srcIndex");
        iVar2 = 0;
        if (pcVar7 != 0) {
          iVar2 = atoi(pcVar7);
        }
        pcVar7 = httpGetEnv(param_1,"dstIndex");
        iVar4 = 0;
        if (pcVar7 != 0) {
          iVar4 = atoi(pcVar7);
        }
        printf("move items %d -> %d\n",iVar2,iVar4);
        swAccessCtrlMoveRules(iVar4 + -1,iVar2 + -1);
      }
      goto LAB_00422024;
    }
    memset(&local_1e8,0,",");
    local_1e8 = 1;
    pcVar7 = httpGetEnv(param_1,"rule_name");
    if (pcVar7 != 0) {
      local_1c0 = 0;
      strncpy(acStack_1d8,pcVar7,24);
    }
    local_1e4 = getEnvToInt(param_1,"enable",0,1);
    pcVar7 = httpGetEnv(param_1,"Changed");
    iVar4 = strcmp(pcVar7,"1");
    if (iVar4 != 0) {
      local_1bf = getEnvToInt(param_1,"hosts_lists",0,255);
      local_1be = getEnvToInt(param_1,"targets_lists",0,255);
      local_1bd = getEnvToInt(param_1,"scheds_lists",0,255);
      iVar2 = 0;
      uVar6 = 0;
LAB_00421f50:
      uVar6 = swSetAccessRulesEntry(&local_1e8,iVar2,uVar6,1);
      goto LAB_00422028;
    }
    local_1bf = getEnvToInt(param_1,"Host_Index",0,255);
    local_1be = getEnvToInt(param_1,"Target_Index",0,255);
    local_1bd = getEnvToInt(param_1,"Schedule_Index",0,255);
    memset(&local_18c,0,"4");
    local_18c = 1;
    local_188 = getEnvToInt(param_1,"address_type",0,1);
    pcVar7 = httpGetEnv(param_1,"hosts_lists_name");
    if (pcVar7 != 0) {
      local_16c = 0;
      strncpy(acStack_184,pcVar7,24);
    }
    if (local_188 == 1) {
      pcVar7 = httpGetEnv(param_1,"src_ip_start");
      if (pcVar7 == 0) {
LAB_00421348:
        pcVar7 = httpGetEnv(param_1,"src_ip_end");
        if (pcVar7 != 0) {
          iVar4 = swChkDotIpAddr(pcVar7);
          if (iVar4 == 0) goto LAB_0042160c;
          local_24c[0] = '\0';
          local_24c[1] = '\0';
          local_24c[2] = '\0';
          local_24c[3] = '\0';
          local_24c[4] = '\0';
          local_24c[5] = '\0';
          local_24c[6] = '\0';
          local_24c[7] = '\0';
          local_24c[8] = '\0';
          local_24c[9] = '\0';
          local_24c[10] = '\0';
          local_24c[11] = '\0';
          local_240 = 0;
          strncpy(local_24c,pcVar7,15);
          local_164 = inet_addr(local_24c);
        }
        if ((local_168 == 0) || (local_164 == 0)) {
          local_168 = local_168 + local_164;
          local_164 = local_168;
        }
        goto LAB_00421444;
      }
      iVar4 = swChkDotIpAddr(pcVar7);
      if (iVar4 != 0) {
        local_24c[0] = '\0';
        local_24c[1] = '\0';
        local_24c[2] = '\0';
        local_24c[3] = '\0';
        local_24c[4] = '\0';
        local_24c[5] = '\0';
        local_24c[6] = '\0';
        local_24c[7] = '\0';
        local_24c[8] = '\0';
        local_24c[9] = '\0';
        local_24c[10] = '\0';
        local_24c[11] = '\0';
        local_240 = 0;
        strncpy(local_24c,pcVar7,15);
        local_168 = inet_addr(local_24c);
        goto LAB_00421348;
      }
LAB_0042160c:
      iVar4 = 0x1f43;
    }
    else {
      if (local_188 == 0) {
        uVar6 = httpGetEnv(param_1,"mac_addr");
        swMacStr2Eth(uVar6,auStack_160);
      }
LAB_00421444:
      pcVar7 = httpGetEnv(param_1,"Host_Changed");
      iVar4 = strcmp(pcVar7,"1");
      uVar6 = 0;
      if (iVar4 == 0) {
        uVar6 = swSetLanHostsEntry(&local_18c,local_1bf,1,1);
      }
      iVar4 = swFilterFindErrorNum(uVar6);
      if (iVar4 == 0) {
        memset(__s,0,176);
        *__s = 1;
        uVar6 = getEnvToInt(param_1,"target_type",0,1);
        __s[1] = uVar6;
        pcVar7 = httpGetEnv(param_1,"targets_lists_name");
        if (pcVar7 != 0) {
          *(undefined *)(__s + 8) = 0;
          strncpy((char *)(__s + 2),pcVar7,24);
        }
        if (__s[1] == 1) {
          pcVar7 = httpGetEnv(param_1,"dst_ip_start");
          if (pcVar7 != 0) {
            iVar4 = swChkDotIpAddr(pcVar7);
            if (iVar4 == 0) goto LAB_0042160c;
            local_24c[0] = '\0';
            local_24c[1] = '\0';
            local_24c[2] = '\0';
            local_24c[3] = '\0';
            local_240 = local_240 & 0xffffff00;
            strncpy(local_24c,pcVar7,15);
            iVar8 = inet_addr(local_24c);
            __s[9] = iVar8;
          }
          pcVar7 = httpGetEnv(param_1,"dst_ip_end");
          if (pcVar7 != 0) {
            iVar4 = swChkDotIpAddr(pcVar7);
            if (iVar4 == 0) goto LAB_0042160c;
            local_24c[0] = '\0';
            local_24c[1] = '\0';
            local_24c[2] = '\0';
            local_24c[3] = '\0';
            local_240 = local_240 & 0xffffff00;
            strncpy(local_24c,pcVar7,15);
            iVar8 = inet_addr(local_24c);
            __s[10] = iVar8;
          }
          if ((__s[9] == 0) || (__s[10] == 0)) {
            iVar4 = __s[9] + __s[10];
            __s[10] = iVar4;
            __s[9] = iVar4;
          }
          pcVar7 = httpGetEnv(param_1,"proto");
          if (pcVar7 != 0) {
            iVar4 = atoi(pcVar7);
            *(char *)(__s + 11) = iVar4;
          }
          if (*(char *)(__s + 11) != '\x03') {
            pcVar7 = httpGetEnv(param_1,"dst_port_start");
            if (pcVar7 != 0) {
              iVar4 = atoi(pcVar7);
              if (iVar4 - 1U < -1) {
                *(short *)(__s + ".") = iVar4;
                goto LAB_00421720;
              }
LAB_0042176c:
              iVar4 = 0x1f44;
              goto LAB_004224b4;
            }
LAB_00421720:
            pcVar7 = httpGetEnv(param_1,"dst_port_end");
            if (pcVar7 != 0) {
              iVar4 = atoi(pcVar7);
              if (-2 < iVar4 - 1U) goto LAB_0042176c;
              *(short *)(__s + 12) = iVar4;
            }
            if ((*(short *)(__s + ".") == 0) || (*(short *)(__s + 12) == 0)) {
              sVar13 = *(short *)(__s + ".") + *(short *)(__s + 12);
              *(short *)(__s + 12) = sVar13;
              *(short *)(__s + ".") = sVar13;
            }
            uVar1 = *(ushort *)(__s + ".");
            if (*(ushort *)(__s + 12) < uVar1) {
              *(ushort *)(__s + ".") = *(ushort *)(__s + 12);
              *(ushort *)(__s + 12) = uVar1;
            }
          }
        }
        else if (__s[1] == 0) {
          iVar5 = 0;
          iVar4 = 0;
          do {
            sprintf(acStack_264,"url_%d",iVar5);
            pcVar7 = httpGetEnv(param_1,acStack_264);
            if (pcVar7 != 0) {
              do {
                pcVar11 = pcVar7;
                pcVar7 = pcVar11 + 1;
              } while (*pcVar11 == ' ');
              if (pcVar11 != 0) {
                sVar10 = strlen(pcVar11);
                if (30 < sVar10) {
                  iVar4 = 9000;
                  goto LAB_004224b4;
                }
                iVar9 = swChkLegalDomain(pcVar11);
                if (iVar9 == 0) {
                  iVar4 = 0x2329;
                  goto LAB_004224b4;
                }
                *(undefined *)(__s + iVar4 + "P") = 0;
                strncpy((char *)(__s + iVar4 + "2"),pcVar11,30);
              }
            }
            iVar5 = iVar5 + 1;
            iVar4 = iVar4 + 31;
          } while (iVar5 != 4);
        }
        pcVar7 = httpGetEnv(param_1,"Target_Changed");
        iVar4 = strcmp(pcVar7,"1");
        if ((iVar4 == 0) && (local_1be != 255)) {
          uVar6 = swSetAccessTargetsEntry(__s,local_1be,1,1);
        }
        else {
          iVar4 = strcmp(pcVar7,"0");
          uVar6 = 0;
          if ((iVar4 == 0) && (local_1be == 255)) {
            uVar6 = swSetAccessTargetsEntry(__s,255,0,1);
            local_1be = swGetAccessTargetSeqByName(__s + 2);
          }
        }
        iVar4 = swFilterFindErrorNum(uVar6);
        if (iVar4 == 0) {
          memset(&local_210,0,"(");
          local_210 = 1;
          pcVar7 = httpGetEnv(param_1,"time_sched_name");
          if (pcVar7 != 0) {
            local_1f4 = 0;
            strncpy(acStack_20c,pcVar7,24);
          }
          iVar4 = getEnvToInt(param_1,"day_type",0,1);
          local_1f3 = 128;
          if (iVar4 == 0) {
            iVar4 = httpGetEnv(param_1,"Mon_select");
            local_270 = iVar4 != 0;
            iVar4 = httpGetEnv(param_1,"Tue_select");
            if (iVar4 != 0) {
              local_270 = local_270 | 2;
            }
            iVar4 = httpGetEnv(param_1,"Wed_select");
            if (iVar4 != 0) {
              local_270 = local_270 | 4;
            }
            iVar4 = httpGetEnv(param_1,"Thu_select");
            if (iVar4 != 0) {
              local_270 = local_270 | 8;
            }
            iVar4 = httpGetEnv(param_1,"Fri_select");
            if (iVar4 != 0) {
              local_270 = local_270 | 16;
            }
            iVar4 = httpGetEnv(param_1,"Sat_select");
            if (iVar4 != 0) {
              local_270 = local_270 | " ";
            }
            iVar4 = httpGetEnv(param_1,"Sun_select");
            if (iVar4 != 0) {
              local_270 = local_270 | "@";
            }
            local_1f3 = swWeekDaysToUint8(local_270 << 24);
          }
          iVar4 = httpGetEnv(param_1,"all_hours");
          if (iVar4 == 0) {
            pcVar7 = httpGetEnv(param_1,"time_sched_start_time");
            if (pcVar7 != 0) {
              local_1f2[4] = 0;
              strncpy(local_1f2,pcVar7,4);
              iVar4 = 0;
              sVar10 = strlen(local_1f2);
              pcVar11 = pcVar7;
              if (sVar10 == 4) {
                do {
                  iVar4 = iVar4 + 1;
                  if (9 < (byte)(*pcVar11 - 0x30U)) goto LAB_00421cd0;
                  pcVar11 = pcVar7 + iVar4;
                } while (iVar4 != 4);
                iVar4 = swChkLegalFirewallTime(local_1f2);
                if (iVar4 != 0) {
                  pcVar7 = httpGetEnv(param_1,"time_sched_end_time");
                  if (pcVar7 != 0) {
                    local_1e9 = 0;
                    strncpy(local_1f2 + 5,pcVar7,4);
                    sVar10 = strlen(local_1f2 + 5);
                    iVar4 = 0;
                    pcVar11 = pcVar7;
                    if (sVar10 == 4) {
                      do {
                        iVar4 = iVar4 + 1;
                        if (9 < (byte)(*pcVar11 - 0x30U)) goto LAB_00421d88;
                        pcVar11 = pcVar7 + iVar4;
                      } while (iVar4 != 4);
                      iVar4 = swChkLegalFirewallTime(local_1f2 + 5);
                      if (iVar4 != 0) goto LAB_00421d98;
                    }
                  }
LAB_00421d88:
                  iVar4 = 0x1f41;
                  goto LAB_004224b4;
                }
              }
            }
LAB_00421cd0:
            iVar4 = 8000;
          }
          else {
            builtin_strncpy(local_1f2,"0000",5);
            local_1e9 = 0;
            memcpy(local_1f2 + 5,"2400",4);
LAB_00421d98:
            pcVar7 = httpGetEnv(param_1,"Schedule_Changed");
            iVar4 = strcmp(pcVar7,"1");
            if ((iVar4 == 0) && (local_1bd != 255)) {
              iVar4 = swSetScheduleEntry(&local_210,local_1bd,1,1);
            }
            else {
              iVar5 = strcmp(pcVar7,"0");
              iVar4 = 0;
              if ((iVar5 == 0) &&
                 ((local_1bd == 255 &&
                  (iVar4 = swSetScheduleEntry(&local_210,255,0,1), iVar4 == 0)))) {
                local_1bd = swGetAccessSchedSeqByName(acStack_20c);
              }
            }
            iVar4 = swFilterFindErrorNum(iVar4);
            if (iVar4 == 0) {
              pcVar7 = httpGetEnv(param_1,"SelIndex");
              if (pcVar7 != 0) {
                iVar2 = atoi(pcVar7);
              }
              uVar6 = 1;
              goto LAB_00421f50;
            }
          }
        }
      }
    }
  }
  else {
    iVar2 = strcmp(pcVar7,"DelAll");
    if (iVar2 == 0) {
      swDelAllFilterEntry(3);
    }
    iVar2 = strcmp(pcVar7,"EnAll");
    if (iVar2 == 0) {
      swAccessCtrlCfgAll(1);
    }
    iVar2 = strcmp(pcVar7,"DisAll");
    uVar6 = 0;
    if (iVar2 == 0) {
      swAccessCtrlCfgAll(0);
LAB_00422024:
      uVar6 = 0;
    }
LAB_00422028:
    iVar2 = swFilterFindErrorNum(uVar6);
    if (iVar2 != 0) {
      HttpErrorPage(param_1,iVar2,0,0);
      return 2;
    }
    memset(&local_1e8,0,",");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "access_rules_data_param");
    uVar3 = local_48;
    uVar14 = local_48 - 1;
    iVar2 = swGetAccessRulesEntry(uVar14 * 8,&local_1e8);
    if ((iVar2 != 0) || (iVar2 = uVar3 - 2, local_48 < 2)) {
      iVar2 = local_48 - 1;
      uVar14 = local_48;
    }
    local_44 = auStack_c4;
    local_40 = acStack_1d8;
    local_3c = auStack_9c;
    local_38 = auStack_83;
    local_34 = auStack_f7;
    local_30 = auStack_6a;
    local_2c = auStack_de;
    uVar3 = 0;
    uVar15 = 0;
    while (uVar12 = swGetAccessCtrlRulesTableSize(), uVar3 < uVar12) {
      memset(&local_1e8,0,",");
      iVar4 = swGetAccessRulesEntry(uVar3,&local_1e8);
      if (iVar4 == 0) break;
      uVar3 = uVar3 + 1;
      if (iVar2 << 3 < uVar3) {
        if ((int)(uVar14 << 3) < uVar3) break;
        uVar15 = uVar15 + 1;
        memcpy(local_44,local_40,25);
        local_a4 = local_1be;
        local_a0 = local_1bd;
        local_a8 = local_1bf;
        memset(auStack_110,0,"K");
        local_26c = CONCAT21(CONCAT11(local_1bf,local_1be),local_1bd) << 8;
        swGetRuleNames(auStack_110,local_26c);
        memcpy(local_3c,auStack_110,25);
        memcpy(local_38,local_34,25);
        memcpy(local_30,local_2c,25);
        local_50[0] = local_1e4;
        pageDynParaListPrintf(&local_158,param_1);
      }
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    local_268 = uVar14;
    pageParaSet(&local_1bc,&local_268,0);
    local_268 = (uint)((int)(uVar14 << 3) < uVar3);
    pageParaSet(&local_1bc,&local_268,1);
    local_268 = uVar15;
    pageParaSet(&local_1bc,&local_268,2);
    local_268 = 8;
    pageParaSet(&local_1bc,&local_268,3);
    local_268 = swGetFilterEntryNumCfg(3);
    pageParaSet(&local_1bc,&local_268,4);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "access_rules_page_param");
    pageDynParaListPrintf(&local_1bc,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    swGetAccessCtrlGlobalCfg(&local_254);
    local_25c = local_254;
    local_258 = local_250;
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "access_global_cfg_dyn_array");
    pageDynParaPrintf(&local_228,0,param_1);
    pageDynParaPrintf(&local_228,1,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar2 = httpRpmFsA(param_1,"/userRpm/AccessCtrlAccessRulesRpm.htm");
    if (iVar2 == 2) {
      return 2;
    }
    iVar4 = 10;
  }
LAB_004224b4:
  sVar13 = HttpErrorPage(param_1,iVar4,0,0);
LAB_004224c0:
  return sVar13;
}

