
code * FUN_0044ecdc(int param_1)

{
  code *pcVar1;
  int iVar2;
  int iVar3;
  int iVar4;
  FILE *__stream;
  void *__ptr;
  char *local_48;
  int local_38;
  undefined4 local_34;
  undefined4 local_30;
  undefined4 local_2c;
  undefined4 local_28;
  undefined4 local_24;
  undefined4 local_20;
  undefined4 local_1c;
  undefined2 local_18;
  char local_14 [12];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetWanStatus");
    pcVar1 = 0;
  }
  else {
    local_34 = 0;
    local_30 = 0;
    local_2c = 0;
    local_28 = 0;
    local_24 = 0;
    local_20 = 0;
    local_1c = 0;
    local_18 = 0;
    local_14[0] = '\0';
    local_14[1] = '\0';
    local_14[2] = '\0';
    local_14[3] = '\0';
    local_14[4] = '\0';
    local_14[5] = '\0';
    local_14[6] = '\0';
    local_14[7] = '\0';
    local_14[8] = '\0';
    local_14[9] = '\0';
    iVar2 = mxmlNewXML("1.0");
    if (iVar2 == 0) {
      puts("xml=NULL");
      pcVar1 = 0;
    }
    else {
      iVar3 = mxmlNewElement(iVar2,"soap:Envelope");
      if (iVar3 == 0) {
        mxmlDelete(iVar2);
        puts("soap_env=NULL");
        pcVar1 = 0;
      }
      else {
        mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar3 = mxmlNewElement(iVar3,"soap:Body");
        if (iVar3 == 0) {
          mxmlDelete(iVar2);
          puts("body=NULL");
          pcVar1 = 0;
        }
        else {
          iVar3 = mxmlNewElement(iVar3,"GetWanStatusResponse");
          if (iVar3 == 0) {
            mxmlDelete(iVar2);
            puts("GetWanStatusResponse_xml=NULL");
            pcVar1 = 0;
          }
          else {
            mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
            iVar4 = mxmlNewElement(iVar3,"GetWanStatusResult");
            if (iVar4 == 0) {
              mxmlDelete(iVar2);
              puts("GetWanStatusResult_xml=NULL");
              pcVar1 = 0;
            }
            else {
              mxmlNewText(iVar4,0,FUN_004ad49c);
              iVar4 = FUN_00426c54();
              if (iVar4 == 0) {
                apmib_get("h",&local_38);
                if (local_38 == 3) {
                  FUN_00426a2c("ppp0",&local_34);
                }
                else {
                  FUN_00426a2c("eth1",&local_34);
                }
                if (local_34 == '\0') {
                  local_48 = "CONNECTING";
                }
                else {
                  FUN_0042c320(0);
                  __stream = fopen("/tmp/InternetConnectStatus","r+");
                  if (__stream == 0) {
                    local_48 = "LIMITED_CONNECTION";
                  }
                  else {
                    fread(local_14,1,9,__stream);
                    iVar4 = strncmp(local_14,FUN_004ad49c,2);
                    if (iVar4 == 0) {
                      local_48 = "CONNECTED";
                    }
                    else {
                      local_48 = "LIMITED_CONNECTION";
                    }
                    fclose(__stream);
                  }
                }
              }
              else {
                FUN_0042c320(1);
                FUN_0042c320(3);
                local_48 = "DISCONNECTED";
              }
              iVar4 = mxmlNewElement(iVar3,"Status");
              if (iVar4 == 0) {
                mxmlDelete(iVar2);
                puts("Status_xml=NULL");
                pcVar1 = 0;
              }
              else {
                mxmlNewText(iVar4,0,local_48);
                iVar3 = mxmlNewElement(iVar3,"Session");
                if (iVar3 == 0) {
                  mxmlDelete(iVar2);
                  puts("Session_xml=NULL");
                  pcVar1 = 0;
                }
                else {
                  FUN_004263f0("cat /proc/sys/net/netfilter/nf_conntrack_count",&local_20,9);
                  mxmlNewText(iVar3,0,&local_20);
                  __ptr = mxmlSaveAllocString(iVar2,0);
                  if (__ptr == 0) {
                    puts("retstring=NULL");
                  }
                  else {
                    FUN_0041ed70("",200,__ptr,"");
                    free(__ptr);
                  }
                  mxmlDelete(iVar2);
                  pcVar1 = FUN_004ad49c;
                }
              }
            }
          }
        }
      }
    }
  }
  return pcVar1;
}

