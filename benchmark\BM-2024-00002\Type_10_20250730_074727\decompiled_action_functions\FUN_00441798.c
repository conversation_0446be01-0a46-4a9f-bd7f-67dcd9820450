
undefined4 FUN_00441798(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  void *__ptr;
  undefined auStack_1b10 [6144];
  char acStack_310 [384];
  char acStack_190 [384];
  int local_10;
  int local_c;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetNetworkTomographySettings");
  }
  else {
    memset(auStack_1b10,0,0x1800);
    memset(acStack_310,0,384);
    memset(acStack_190,0,384);
    local_10 = 0;
    local_c = 0;
    iVar1 = apmib_get(0x1b72,auStack_1b10);
    if (iVar1 == 0) {
      puts("error, apmib get MIB_PING_ADDRESS");
    }
    else {
      iVar1 = apmib_get(0x1b73,&local_10);
      if (iVar1 == 0) {
        puts("error, apmib get MIB_PING_NUMBER");
      }
      else {
        if (local_10 != 0) {
          snprintf(acStack_310,383,"%d",local_10);
        }
        iVar1 = apmib_get(0x1b74,&local_c);
        if (iVar1 == 0) {
          puts("error, apmib get MIB_PING_SIZE");
        }
        else if (local_c != 0) {
          snprintf(acStack_190,383,"%d",local_c);
        }
      }
    }
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar2 = mxmlNewElement(iVar2,"soap:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"GetNetworkTomographySettingsResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("GetNetworkTomographySettingsResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar3 = mxmlNewElement(iVar2,"GetNetworkTomographySettingsResult");
            if (iVar3 == 0) {
              mxmlDelete(iVar1);
              puts("GetNetworkTomographySettingsResult=NULL");
            }
            else {
              mxmlNewText(iVar3,0,"O");
              iVar3 = mxmlNewElement(iVar2,"Address");
              if (iVar3 == 0) {
                mxmlDelete(iVar1);
                puts("address=NULL");
              }
              else {
                mxmlNewText(iVar3,0,auStack_1b10);
                iVar3 = mxmlNewElement(iVar2,"Number");
                if (iVar3 == 0) {
                  mxmlDelete(iVar1);
                  puts("number=NULL");
                }
                else {
                  mxmlNewText(iVar3,0,acStack_310);
                  iVar2 = mxmlNewElement(iVar2,"Size");
                  if (iVar2 == 0) {
                    mxmlDelete(iVar1);
                    puts("size=NULL");
                  }
                  else {
                    mxmlNewText(iVar2,0,acStack_190);
                    if ("" == 0) {
                      __ptr = mxmlSaveAllocString(iVar1,0);
                      if (__ptr != 0) {
                        FUN_0041ed70("",200,__ptr,"");
                        free(__ptr);
                      }
                    }
                    mxmlDelete(iVar1);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

