
/* WARNING: Removing unreachable block (ram,FUN_00462918) */

int FUN_00462918(undefined4 param_1)

{
  int iVar1;
  short sVar3;
  int iVar2;
  byte *pbVar4;
  uint uStack_d78;
  uint uStack_d74;
  uint uStack_d70;
  char acStack_d6c [20];
  char acStack_d58 [36];
  undefined auStack_d34 [8];
  int iStack_d2c;
  undefined uStack_bbc;
  byte bStack_bbb;
  byte abStack_b8e [2902];
  undefined *puStack_38;
  uint *puStack_34;
  uint *puStack_30;
  uint *puStack_2c;
  
  uStack_d78 = 0;
  uStack_d74 = 0;
  uStack_d70 = 0;
  swWlanBasicCfgGet(0,auStack_d34);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar3 = HttpDenyPage(param_1);
  }
  else {
    memset(&uStack_bbc,0,0xb82);
    swWlanActivateScan(0,&uStack_bbc);
    if (iStack_d2c == 1) {
      swWlanInactiveVap("ath0");
    }
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "siteSurveyPara");
    httpPrintf(param_1,"%d, %d,",2,bStack_bbb);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "siteList");
    puStack_38 = &uStack_bbc;
    puStack_34 = &uStack_d78;
    pbVar4 = abStack_b8e;
    puStack_30 = &uStack_d74;
    puStack_2c = &uStack_d70;
    for (iVar1 = 0; iVar1 < (int)bStack_bbb; iVar1 = iVar1 + 1) {
      sprintf(acStack_d6c,"%02X-%02X-%02X-%02X-%02X-%02X",pbVar4[-43],pbVar4[-42],
              pbVar4[-41],pbVar4[-40],pbVar4[-39],pbVar4[-38]);
      writePageParamSet(param_1,""%s",",acStack_d6c,0);
      strncpy(acStack_d58,puStack_38 + iVar1 * "8","!");
      writePageParamSet(param_1,""%s",",acStack_d58,1);
      uStack_d78 = pbVar4[-3];
      writePageParamSet(param_1,"%d,",puStack_34,2);
      iVar2 = getProductId();
      if ((((iVar2 == 0x8430002) || (iVar2 = getProductId(), iVar2 == 0x8100001)) ||
          (iVar2 = getProductId(), iVar2 == 0x8100002)) ||
         (((iVar2 = getProductId(), iVar2 == 0x8100001 ||
           (iVar2 = getProductId(), iVar2 == 0x8100002)) ||
          (iVar2 = getProductId(), iVar2 == 0x8020002)))) {
        uStack_d74 = (int)(*(ushort *)(pbVar4 + -2) - 0x967) / 5;
        if (14 < uStack_d74) {
          
        }
      }
      else {
        uStack_d74 = (uint)*(ushort *)(pbVar4 + -2);
      }
      writePageParamSet(param_1,"%d,",puStack_30,3);
      uStack_d70 = (uint)*pbVar4;
      writePageParamSet(param_1,"%d,",puStack_2c,4);
      pbVar4 = pbVar4 + ".";
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/WzdWlanSiteSurveyRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar3 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar3;
}

