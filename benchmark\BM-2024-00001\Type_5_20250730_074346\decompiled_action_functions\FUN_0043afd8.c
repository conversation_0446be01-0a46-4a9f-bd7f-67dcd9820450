
/* WARNING: Globals starting with '_' overlap smaller symbols at the same address */

undefined4 FUN_0043afd8(undefined4 param_1)

{
  uint32_t uVar1;
  uint32_t uVar2;
  uint32_t uVar3;
  uint32_t uVar4;
  char *pcVar5;
  in_addr_t iVar6;
  short sVar9;
  uint uVar7;
  int iVar8;
  uint32_t local_268;
  undefined auStack_264 [4];
  int local_260;
  undefined4 local_25c;
  char acStack_258 [16];
  char acStack_248 [16];
  char acStack_238 [16];
  uint32_t local_228 [4];
  char local_218 [52];
  char acStack_1e4 [64];
  undefined auStack_1a4 [4];
  int local_1a0;
  
  builtin_strncpy(local_218 + 4,".htm",5);
  builtin_strncpy(local_218,"main",4);
  memset(local_218 + 9,0,")");
  swGetLanCfg(local_228);
  swGetWanIpMask(0,&local_268,auStack_264);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  _0x00000000 = "@";
  _0x00000000 = 16;
  "" = 0;
  "" = 0;
  indexDynArray = acStack_258;
  _0x00000000 = &local_268;
  "" = acStack_1e4;
  uVar1 = ntohl(local_228[0]);
  uVar2 = ntohl(local_228[0]);
  uVar3 = ntohl(local_228[0]);
  uVar4 = ntohl(local_228[0]);
  sprintf(acStack_258,"%d.%d.%d.%d",uVar1 >> 24,(uVar2 & 0xff0000) >> 16,
          (int)(uVar3 & -256) >> 8,uVar4 & 255);
  uVar1 = ntohl(local_268);
  uVar2 = ntohl(local_268);
  uVar3 = ntohl(local_268);
  uVar4 = ntohl(local_268);
  sprintf(acStack_248,"%d.%d.%d.%d",uVar1 >> 24,(uVar2 & 0xff0000) >> 16,
          (int)(uVar3 & -256) >> 8,uVar4 & 255);
  pcVar5 = getDefaultLanDomain();
  strncpy(acStack_1e4,pcVar5,"@");
  httpClientIPGet(param_1,acStack_238);
  iVar6 = inet_addr(acStack_238);
  sVar9 = swChkSameLanSubnet(iVar6);
  uVar7 = swGetFirstState();
  iVar8 = httpGetEnv(param_1,"redirectToWlanSec");
  if (iVar8 != 0) {
    uVar7 = 255;
  }
  iVar8 = isSupportAccountEnable();
  if (iVar8 != 0) {
    pcVar5 = httpGidGet(param_1);
    iVar8 = strcmp(pcVar5,"Support");
    uVar7 = uVar7 & -(uint)(iVar8 != 0);
  }
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "framePara");
  httpPrintf(param_1,"%d,\r\n",uVar7);
  httpPrintf(param_1,"\"%s\",\r\n",local_218);
  local_260 = 0;
  local_25c = 0;
  swGetSystemMode();
  if (local_260 == 8) {
    iVar8 = getProductId();
    if (iVar8 == -0x6fdfffff) {
      memset(auStack_1a4,0,376);
      ucWlanBasicConfigGet(0,auStack_1a4);
      local_260 = 7;
      if ((local_1a0 != 0) && (local_260 = 6, local_1a0 == 3)) {
        local_260 = local_1a0;
      }
    }
  }
  httpPrintf(param_1,"%d,\r\n",local_260);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "indexPara");
  pageDynParaPrintf(&indexDynArray,0,param_1);
  pageDynParaPrintf(&indexDynArray,1,param_1);
  pageDynParaPrintf(&indexDynArray,2,param_1);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  HttpWebV4Head(param_1,0,0);
  iVar8 = isOEMWanLogin();
  if ((iVar8 == 0) || (sVar9 == 0)) {
    pcVar5 = "/userRpm/Index.htm";
  }
  else {
    pcVar5 = "/userRpm/Index_Ltv.htm";
  }
  iVar8 = httpRpmFsA(param_1,pcVar5);
  if (iVar8 != 2) {
    puts("open filesys error\r");
  }
  return 2;
}

