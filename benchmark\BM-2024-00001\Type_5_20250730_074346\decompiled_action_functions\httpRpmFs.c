
undefined4 httpRpmFs(int param_1)

{
  uint uVar1;
  char *__dest;
  char *pcVar2;
  int __fd;
  undefined4 uVar3;
  void *__buf;
  int iVar4;
  undefined4 uVar5;
  ssize_t sVar6;
  int iVar7;
  uint __nbytes;
  uint local_f8;
  undefined auStack_f4 [52];
  stat sStack_c0;
  
  if (*(short *)(param_1 + "0") != 200) {
    return 0xffffffff;
  }
  __dest = httpBufferGet();
  if (httpServerUncompress == '\0') {
    httpMimeContentEncodingGet(param_1,0);
  }
  strcpy(__dest,"/tmp/");
  pcVar2 = httpRpmDataGet(param_1);
  strcat(__dest,pcVar2);
  pcVar2 = strstr(__dest,"..");
  if (pcVar2 != 0) {
    return 0xffffffff;
  }
  __fd = open(__dest,0);
  if (__fd < 0) {
    strcpy(__dest,"/web/");
    pcVar2 = httpRpmDataGet(param_1);
    strcat(__dest,pcVar2);
    pcVar2 = strstr(__dest,"..");
    if (pcVar2 != 0) {
      return 0xffffffff;
    }
    __fd = open(__dest,0);
    if (__fd < 0) {
      printf("open %s error!\r\n",__dest);
      perror("reason:");
      return 0xffffffff;
    }
  }
  stat(__dest,&sStack_c0);
  httpMimeContentLengthSet(param_1,1,sStack_c0.st_blocks);
  uVar3 = httpReqMemPartIdGet(param_1);
  local_f8 = sStack_c0.st_blocks;
  __buf = httpConservativeAlloc(uVar3,&local_f8);
  uVar1 = local_f8;
  if (__buf == 0) {
    httpStatusSet(param_1,413);
    uVar3 = httpErrStringGet(7);
    httpError(param_1,uVar3);
    FUN_005068d0(__fd);
    return 0xffffffff;
  }
  httpStatusSet(param_1,0);
  iVar4 = httpFileContentTypeGet(auStack_f4);
  if (iVar4 != 0) {
    uVar5 = FUN_00506790(__dest);
    httpMimeHdrSet(param_1,1,"Content-Type",uVar5);
  }
  __nbytes = sStack_c0.st_blocks;
  if (uVar1 < sStack_c0.st_blocks) {
    __nbytes = uVar1;
  }
  iVar4 = 0;
  httpHeaderGenerate(param_1);
  while( true ) {
    sVar6 = read(__fd,__buf,__nbytes);
    iVar4 = iVar4 + sVar6;
    iVar7 = httpBlockPut(param_1,__buf,sVar6);
    if (iVar7 == -1) break;
    __nbytes = sStack_c0.st_blocks - iVar4;
    if (local_f8 < (uint)(sStack_c0.st_blocks - iVar4)) {
      __nbytes = local_f8;
    }
    if ((__nbytes == 0) || (sVar6 == 0)) break;
  }
  httpFileClose(auStack_f4);
  FUN_005068d0(__fd);
  memPoolFree(uVar3,__buf);
  return 2;
}

