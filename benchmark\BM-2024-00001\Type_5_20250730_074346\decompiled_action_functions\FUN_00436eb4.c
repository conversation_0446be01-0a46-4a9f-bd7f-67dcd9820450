
int FUN_00436eb4(undefined4 param_1)

{
  int iVar1;
  char *pcVar2;
  long lVar3;
  short sVar4;
  char *pcVar5;
  undefined1 *puVar6;
  uint32_t uVar7;
  uint32_t __netlong;
  int iVar8;
  uint32_t __netlong_00;
  uint32_t __netlong_01;
  code *pcVar9;
  uint32_t uStack_398;
  int iStack_394;
  undefined4 auStack_390 [2];
  char acStack_388 [16];
  undefined *puStack_378;
  undefined4 uStack_374;
  undefined *puStack_370;
  undefined4 uStack_36c;
  undefined *puStack_368;
  undefined4 uStack_364;
  undefined *puStack_360;
  undefined4 uStack_35c;
  undefined *puStack_358;
  undefined4 uStack_354;
  undefined *puStack_350;
  undefined4 uStack_34c;
  undefined *puStack_348;
  undefined4 uStack_344;
  undefined *puStack_340;
  undefined4 uStack_33c;
  undefined *puStack_338;
  undefined4 uStack_334;
  undefined *puStack_330;
  undefined4 uStack_32c;
  undefined4 *puStack_328;
  undefined4 uStack_324;
  undefined4 *puStack_320;
  undefined4 uStack_31c;
  undefined4 *puStack_318;
  in_addr_t iStack_314;
  in_addr_t iStack_310;
  in_addr_t iStack_30c;
  in_addr_t iStack_308;
  char acStack_304 [63];
  undefined uStack_2c5;
  undefined auStack_2c4 [120];
  undefined auStack_24c [120];
  undefined uStack_1d4;
  undefined4 uStack_1b4;
  undefined auStack_1b0 [4];
  undefined auStack_1ac [4];
  undefined auStack_1a8 [120];
  undefined auStack_130 [120];
  undefined auStack_b8 [4];
  undefined auStack_b4 [64];
  undefined auStack_74 [16];
  undefined auStack_64 [16];
  undefined auStack_54 [16];
  undefined auStack_44 [16];
  undefined4 uStack_34;
  undefined4 uStack_30;
  undefined4 auStack_2c [2];
  
  puStack_378 = auStack_1b0;
  puStack_370 = auStack_1ac;
  puStack_368 = auStack_1a8;
  puStack_360 = auStack_130;
  puStack_358 = auStack_b8;
  puStack_350 = auStack_b4;
  puStack_348 = auStack_74;
  puStack_340 = auStack_64;
  puStack_338 = auStack_54;
  puStack_330 = auStack_44;
  puStack_328 = &uStack_34;
  puStack_320 = &uStack_30;
  puStack_318 = auStack_2c;
  uStack_32c = 16;
  uStack_344 = 16;
  uStack_33c = 16;
  uStack_334 = 16;
  uStack_35c = "x";
  uStack_34c = "@";
  uStack_364 = "x";
  iStack_394 = 0;
  iStack_310 = 0;
  uStack_374 = 0;
  uStack_36c = 0;
  uStack_354 = 0;
  uStack_324 = 0;
  uStack_31c = 0;
  iStack_314 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    iVar1 = HttpDenyPage(param_1);
    iVar1 = iVar1 << 16;
    goto LAB_00437470;
  }
  memset(&puStack_320,0,368);
  uVar7 = 0;
  pcVar2 = httpGetEnv(param_1,"ClientId");
  if (pcVar2 != 0) {
    lVar3 = atol(pcVar2);
    uVar7 = (uint32_t)lVar3;
  }
  iVar1 = httpGetEnv(param_1,"Next");
  if (iVar1 == 0) {
    iVar1 = httpGetEnv(param_1,"Return");
    if (iVar1 != 0) {
      pcVar9 = wzdStepFindPrev;
LAB_00437784:
      iVar1 = (*pcVar9)(&iStack_394);
      if (iVar1 == 0) {
        return 2;
      }
      iVar1 = GoUrl(param_1,iStack_394 + 8);
      iVar1 = iVar1 << 16;
      goto LAB_00437470;
    }
    swGetL2tpCfg(&puStack_320);
    uStack_398 = uVar7;
    pageParaSet(&puStack_378,&uStack_398,0);
    uStack_398 = 0;
    pageParaSet(&puStack_378,&uStack_398,1);
    pageParaSet(&puStack_378,auStack_2c4,2);
    pageParaSet(&puStack_378,auStack_24c,3);
    uStack_398 = (uint32_t)(puStack_318 != &DAT_00000001);
    pageParaSet(&puStack_378,&uStack_398,4);
    pageParaSet(&puStack_378,acStack_304,5);
    uVar7 = iStack_314;
    __netlong = iStack_310;
    __netlong_00 = iStack_30c;
    __netlong_01 = iStack_308;
    if (puStack_318 != 0) {
      uVar7 = 0;
      __netlong = 0;
      __netlong_00 = 0;
      __netlong_01 = 0;
    }
    uStack_398 = ntohl(uVar7);
    sprintf(acStack_388,"%d.%d.%d.%d",uStack_398 >> 24,uStack_398 >> 16 & 255,
            uStack_398 >> 8 & 255,uStack_398 & 255);
    pageParaSet(&puStack_378,acStack_388,6);
    uStack_398 = ntohl(__netlong);
    sprintf(acStack_388,"%d.%d.%d.%d",uStack_398 >> 24,uStack_398 >> 16 & 255,
            uStack_398 >> 8 & 255,uStack_398 & 255);
    pageParaSet(&puStack_378,acStack_388,7);
    uStack_398 = ntohl(__netlong_00);
    sprintf(acStack_388,"%d.%d.%d.%d",uStack_398 >> 24,uStack_398 >> 16 & 255,
            uStack_398 >> 8 & 255,uStack_398 & 255);
    pageParaSet(&puStack_378,acStack_388,8);
    uStack_398 = ntohl(__netlong_01);
    sprintf(acStack_388,"%d.%d.%d.%d",uStack_398 >> 24,uStack_398 >> 16 & 255,
            uStack_398 >> 8 & 255,uStack_398 & 255);
    pageParaSet(&puStack_378,acStack_388,9);
    swGetSystemMode(auStack_390);
    uStack_34 = 3;
    uStack_30 = swIsMultiSystemMode();
    auStack_2c[0] = auStack_390[0];
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "wzdL2TPInf");
    iVar1 = 0;
    do {
      iVar8 = iVar1 + 1;
      pageDynParaPrintf(&puStack_378,iVar1,param_1);
      iVar1 = iVar8;
    } while (iVar8 != 11);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpWizardPrintStepInfo(param_1);
    HttpWebV4Head(param_1,0,0);
    iVar1 = httpRpmFsA(param_1,"/userRpm/WzdL2TPRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar4 = 10;
    puVar6 = 0;
  }
  else {
    swGetL2tpCfg(&puStack_320);
    pcVar2 = httpGetEnv(param_1,"acc");
    if (pcVar2 == 0) {
LAB_004370e8:
      pcVar9 = memset;
      pcVar5 = 0;
    }
    else {
      do {
        pcVar5 = pcVar2;
        pcVar2 = pcVar5 + 1;
      } while (*pcVar5 == ' ');
      if ((pcVar5 == 0) || (*pcVar5 == '\0')) goto LAB_004370e8;
      pcVar9 = strncpy;
      auStack_24c[0] = 0;
    }
    (*pcVar9)(auStack_2c4,pcVar5,"x");
    pcVar2 = httpGetEnv(param_1,"psw");
    if (pcVar2 == 0) {
LAB_00437164:
      pcVar9 = memset;
      pcVar5 = 0;
    }
    else {
      do {
        pcVar5 = pcVar2;
        pcVar2 = pcVar5 + 1;
      } while (*pcVar5 == ' ');
      if ((pcVar5 == 0) || (*pcVar5 == '\0')) goto LAB_00437164;
      pcVar9 = strncpy;
      uStack_1d4 = 0;
    }
    (*pcVar9)(auStack_24c,pcVar5,"x");
    pcVar2 = httpGetEnv(param_1,"IpType");
    if (pcVar2 == 0) {
      puStack_318 = &DAT_00000001;
    }
    else {
      iVar1 = atoi(pcVar2);
      puStack_318 = (undefined4 *)(uint)(iVar1 == 0);
    }
    pcVar2 = httpGetEnv(param_1,"L2TPServerName");
    if (pcVar2 != 0) {
      do {
        pcVar5 = pcVar2;
        pcVar2 = pcVar5 + 1;
      } while (*pcVar5 == ' ');
      uStack_2c5 = 0;
      strncpy(acStack_304,pcVar5,"?");
    }
    if (puStack_318 == 0) {
      pcVar2 = httpGetEnv(param_1,"sta_ip");
      iVar1 = swChkDotIpAddr(pcVar2);
      if (iVar1 == 0) {
        puVar6 = "";
        sVar4 = 0x138b;
      }
      else {
        iStack_314 = inet_addr(pcVar2);
        pcVar2 = httpGetEnv(param_1,"sta_mask");
        iVar1 = swChkDotIpAddr(pcVar2);
        if (iVar1 == 0) {
          puVar6 = "";
          sVar4 = 0x138c;
        }
        else {
          iStack_310 = inet_addr(pcVar2);
          pcVar2 = httpGetEnv(param_1,"sta_gw");
          iVar1 = swChkDotIpAddr(pcVar2);
          if (iVar1 == 0) {
            puVar6 = "";
            sVar4 = 0x138f;
          }
          else {
            iStack_30c = inet_addr(pcVar2);
            pcVar2 = httpGetEnv(param_1,"sta_dns");
            if (pcVar2 == 0) {
              pcVar2 = "0.0.0.0";
            }
            iVar1 = swChkDotIpAddr(pcVar2);
            if (iVar1 != 0) {
              iStack_308 = inet_addr(pcVar2);
              if (puStack_318 != 0) goto LAB_004373dc;
              pcVar9 = swChkL2tpCfg;
              goto LAB_004373e4;
            }
            puVar6 = "";
            sVar4 = 0x138d;
          }
        }
      }
    }
    else {
LAB_004373dc:
      pcVar9 = swChkL2tpDomain;
LAB_004373e4:
      sVar4 = (*pcVar9)(&puStack_320);
      if (sVar4 == 0) {
        uStack_1b4 = 2;
        swSetL2tpCfg(&puStack_320);
        pcVar9 = wzdStepFindNext;
        goto LAB_00437784;
      }
      puVar6 = "";
    }
  }
  iVar1 = HttpErrorPage(param_1,sVar4,puVar6,0);
  iVar1 = iVar1 << 16;
LAB_00437470:
  return iVar1 >> 16;
}

