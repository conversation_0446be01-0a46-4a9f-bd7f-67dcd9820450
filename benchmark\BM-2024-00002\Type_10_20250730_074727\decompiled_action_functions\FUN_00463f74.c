
undefined4 FUN_00463f74(int param_1)

{
  undefined4 uVar1;
  undefined4 uVar2;
  int iVar3;
  int iVar4;
  char *__s1;
  int local_28;
  uint local_10 [2];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetWLanRadioSecurity");
  }
  else {
    local_10[0] = 0;
    iVar3 = mxmlLoadString(0,param_1,0);
    iVar4 = mxmlFindElement(iVar3,iVar3,"soap:Envelope",0,0,1);
    if ((iVar3 == 0) || (iVar4 == 0)) {
      fwrite("Buile tree and get state failed\n",1," ",stderr);
    }
    else {
      local_28 = mxmlFindElement(iVar4,iVar3,"SetMultipleActions",0,0,1);
      uVar2 = wlan_idx;
      uVar1 = vwlan_idx;
      while (vwlan_idx = uVar1, wlan_idx = uVar2, local_28 != 0) {
        local_28 = mxmlFindElement(local_28,iVar3,"SetWLanRadioSecurity",0,0,1);
        if (local_28 != 0) {
          iVar4 = mxmlFindElement(local_28,iVar3,"RadioID",0,0,1);
          __s1 = mxmlGetText(iVar4,0);
          if ((iVar4 != 0) && (__s1 != 0)) {
            iVar4 = strncmp(__s1,"RADIO_2.4GHz",12);
            if (iVar4 != 0) {
              puts("+====================================5G wifi key set");
            }
            else {
              puts("+====================================2.4G wifi key set");
            }
            local_10[0] = (uint)(iVar4 == 0);
            FUN_004622dc(local_28,iVar3,local_10);
          }
        }
      }
      if ("" == 0) {
        apmib_update(4);
        FUN_0045cfd4();
      }
      mxmlDelete(iVar3);
    }
  }
  return 0;
}

