
int FUN_00458b2c(undefined4 param_1)

{
  int iVar1;
  short sVar2;
  byte *pbVar3;
  uint uStack_d78;
  uint uStack_d74;
  uint uStack_d70;
  char acStack_d6c [20];
  char acStack_d58 [36];
  undefined auStack_d34 [8];
  int iStack_d2c;
  undefined uStack_bbc;
  byte bStack_bbb;
  byte abStack_b91 [2905];
  undefined *puStack_38;
  uint *puStack_34;
  uint *puStack_30;
  
  uStack_d78 = 0;
  uStack_d74 = 0;
  uStack_d70 = 0;
  swWlanBasicCfgGet(0,auStack_d34);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar2 = HttpDenyPage(param_1);
  }
  else {
    memset(&uStack_bbc,0,0xb82);
    swWlanActivateScan(0,&uStack_bbc);
    if (iStack_d2c == 1) {
      swWlanInactiveVap("ath0");
    }
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "siteSurveyPara");
    httpPrintf(param_1,"%d, %d,",2,bStack_bbb);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "siteList");
    puStack_38 = &uStack_bbc;
    puStack_34 = &uStack_d78;
    pbVar3 = abStack_b91;
    puStack_30 = &uStack_d74;
    for (iVar1 = 0; iVar1 < (int)bStack_bbb; iVar1 = iVar1 + 1) {
      sprintf(acStack_d6c,"%02X-%02X-%02X-%02X-%02X-%02X",pbVar3[-40],pbVar3[-39],
              pbVar3[-38],pbVar3[-37],pbVar3[-36],pbVar3[-35]);
      writePageParamSet(param_1,""%s",",acStack_d6c,0);
      strncpy(acStack_d58,puStack_38 + iVar1 * "8","!");
      writePageParamSet(param_1,""%s",",acStack_d58,1);
      uStack_d78 = (uint)*pbVar3;
      writePageParamSet(param_1,"%d,",puStack_34,2);
      uStack_d74 = (uint)*(ushort *)(pbVar3 + 1);
      writePageParamSet(param_1,"%d,",puStack_30,3);
      uStack_d70 = pbVar3[3];
      writePageParamSet(param_1,"%d,",&uStack_d70,4);
      pbVar3 = pbVar3 + ".";
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/WzdWlanSiteSurveyRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar2 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar2;
}

