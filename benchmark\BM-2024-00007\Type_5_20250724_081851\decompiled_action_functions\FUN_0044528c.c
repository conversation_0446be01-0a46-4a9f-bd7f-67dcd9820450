
int FUN_0044528c(undefined4 param_1)

{
  char *pcVar1;
  int iVar2;
  int iVar3;
  int iVar4;
  short sVar5;
  int iVar6;
  bool bVar7;
  bool bVar8;
  undefined4 local_98;
  undefined4 local_94;
  int local_90;
  int local_8c;
  undefined4 local_88;
  undefined4 local_84;
  undefined4 local_80;
  undefined4 local_7c;
  undefined4 local_78;
  undefined4 local_74;
  undefined4 local_70;
  undefined4 local_6c;
  undefined4 local_68;
  undefined4 local_64;
  undefined4 local_60;
  undefined4 *local_5c;
  undefined4 local_58;
  undefined4 *local_54;
  undefined4 local_50;
  undefined4 *local_4c;
  undefined4 local_48;
  undefined4 *local_44;
  undefined4 local_40;
  undefined4 *local_3c;
  undefined4 local_38;
  undefined4 *local_34;
  undefined4 local_30;
  undefined4 *local_2c;
  undefined4 local_28;
  undefined4 local_24;
  
  pcVar1 = httpGetEnv(param_1,"QoSCtrl");
  bVar8 = false;
  if (pcVar1 != 0) {
    bVar8 = *pcVar1 == '1';
  }
  pcVar1 = httpGetEnv(param_1,"userWanType");
  bVar7 = false;
  if (pcVar1 != 0) {
    bVar7 = *pcVar1 == '1';
  }
  pcVar1 = httpGetEnv(param_1,"up_bandWidth");
  if (pcVar1 != 0) {
    iVar2 = atoi(pcVar1);
    iVar3 = swGetSwitchMaxSpeed();
    if ((iVar3 < iVar2) || (iVar2 < 1)) {
      iVar3 = swGetSwitchMaxSpeed();
      iVar6 = 27000;
      if (iVar3 == 100000) goto LAB_00445658;
      iVar3 = swGetSwitchMaxSpeed();
      iVar6 = 0x697a;
      if (iVar3 == 1000000) goto LAB_00445658;
    }
    pcVar1 = httpGetEnv(param_1,"down_bandWidth");
    if (pcVar1 != 0) {
      iVar3 = atoi(pcVar1);
      iVar6 = swGetSwitchMaxSpeed();
      if ((iVar6 < iVar3) || (iVar3 < 1)) {
        iVar4 = swGetSwitchMaxSpeed();
        iVar6 = 0x6979;
        if (iVar4 == 100000) goto LAB_00445658;
        iVar6 = swGetSwitchMaxSpeed();
        if (iVar6 == 1000000) {
          iVar6 = 0x697b;
          goto LAB_00445658;
        }
      }
      local_90 = iVar3;
      local_8c = iVar2;
      iVar6 = swSetQoSCfg(bVar8,bVar7,&local_90,1,1);
      if (iVar6 != 0) goto LAB_00445658;
    }
  }
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  local_5c = &local_78;
  local_54 = &local_74;
  local_4c = &local_70;
  local_44 = &local_6c;
  local_3c = &local_68;
  local_34 = &local_64;
  local_2c = &local_60;
  local_84 = 0;
  local_80 = 0;
  local_7c = 0;
  local_88 = 0;
  local_24 = 0;
  local_58 = 0;
  local_50 = 0;
  local_48 = 0;
  local_40 = 0;
  local_38 = 0;
  local_30 = 0;
  local_28 = 0;
  swGetQoSCfg(&local_98,&local_94,&local_88,1);
  local_60 = swGetSwitchMaxSpeed();
  local_78 = local_98;
  local_64 = local_94;
  local_74 = local_84;
  local_70 = local_88;
  local_6c = local_7c;
  local_68 = local_80;
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "QoSCfgPara");
  iVar2 = 0;
  do {
    iVar3 = iVar2 + 1;
    pageDynParaPrintf(&local_5c,iVar2,param_1);
    iVar2 = iVar3;
  } while (iVar3 != 7);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  HttpWebV4Head(param_1,0,1);
  iVar2 = httpRpmFsA(param_1,"/userRpm/QoSCfgRpm.htm");
  if (iVar2 == 2) {
    return 2;
  }
  iVar6 = 10;
LAB_00445658:
  sVar5 = HttpErrorPage(param_1,iVar6,0,0);
  return sVar5;
}

