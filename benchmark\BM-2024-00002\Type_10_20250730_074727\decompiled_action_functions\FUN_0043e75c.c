
undefined4 FUN_0043e75c(int param_1)

{
  int iVar1;
  int iVar2;
  void *__ptr;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","RunReboot");
  }
  else {
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar2 = mxmlNewElement(iVar2,"soap:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"RunRebootResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("RebootResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar2 = mxmlNewElement(iVar2,"RunRebootResult");
            if (iVar2 == 0) {
              mxmlDelete(iVar1);
              puts("RebootResult=NULL");
            }
            else {
              mxmlNewText(iVar2,0,"O");
              if ("" == 0) {
                __ptr = mxmlSaveAllocString(iVar1,0);
                if (__ptr != 0) {
                  FUN_0041ed70("",200,__ptr,"");
                  free(__ptr);
                }
                FUN_004263f0("reboot",0,0);
                FUN_0042c210();
              }
              mxmlDelete(iVar1);
            }
          }
        }
      }
    }
  }
  return 0;
}

