
undefined4 FUN_0043de2c(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  void *__ptr;
  undefined4 local_10;
  in_addr iStack_c;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetNTPServerSettings");
  }
  else {
    local_10 = 1;
    iVar1 = mxmlLoadString(0,param_1,0);
    if (iVar1 == 0) {
      puts("ERROR!  tree is NULL");
    }
    else {
      iVar2 = mxmlFindElement(iVar1,iVar1,"TimeZone",0,0,1);
      if (iVar2 == 0) {
        puts("apmib_set TimeZone  state is NULL");
      }
      else {
        inet_aton("*************",&iStack_c);
        iVar3 = apmib_set(154,&iStack_c);
        if (iVar3 == 0) {
          puts("error, set ntp server ip");
        }
        iVar2 = mxmlGetText(iVar2,0);
        if (iVar2 == 0) {
          puts("apmib_set MIB_NTP_ENABLED  strTimeZone is NULL");
        }
        else {
          iVar2 = apmib_set(153,iVar2 + 3);
          if ((iVar2 == 0) || (iVar2 = apmib_set(151,&local_10), iVar2 == 0)) {
            puts("set MIB_NTP_TIMEZONE is error");
          }
          else {
            set_timeZone();
          }
        }
      }
      iVar2 = mxmlNewXML("1.0");
      if (iVar2 == 0) {
        printf("Create new xml erro!!!");
      }
      else {
        iVar3 = mxmlNewElement(iVar2,"soap:Envelope");
        if (iVar3 == 0) {
          mxmlDelete(iVar2);
          puts("soap_env=NULL");
        }
        else {
          mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
          mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
          mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
          iVar3 = mxmlNewElement(iVar3,"soap:Body");
          if (iVar3 == 0) {
            mxmlDelete(iVar2);
            puts("body=NULL");
          }
          else {
            iVar3 = mxmlNewElement(iVar3,"SetNTPServerSettingsResponse");
            if (iVar3 == 0) {
              mxmlDelete(iVar2);
              puts("SetNTPServerSettingsResponse=NULL");
            }
            else {
              mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
              iVar3 = mxmlNewElement(iVar3,"SetNTPServerSettingsResult");
              if (iVar3 == 0) {
                mxmlDelete(iVar2);
                puts("SetNTPServerSettingsResult=NULL");
              }
              else {
                mxmlNewText(iVar3,0,"O");
                if ("" == 0) {
                  apmib_update(4);
                  __ptr = mxmlSaveAllocString(iVar2,0);
                  if (__ptr != 0) {
                    FUN_0041ed70("",200,__ptr,"");
                    free(__ptr);
                  }
                }
                mxmlDelete(iVar1);
                mxmlDelete(iVar2);
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

