
undefined4 FUN_0044f788(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  void *__ptr;
  char local_10 [8];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetWanplugInfo");
  }
  else {
    local_10[0] = '\0';
    local_10[1] = '\0';
    local_10[2] = '\0';
    local_10[3] = '\0';
    local_10[4] = '\0';
    local_10[5] = '\0';
    local_10[6] = '\0';
    local_10[7] = '\0';
    iVar1 = FUN_0042cc4c("/tmp/WanStatus.txt");
    if (iVar1 == 1) {
      snprintf(local_10,7,"true");
      system("rm /tmp/WanStatus.txt");
    }
    else {
      snprintf(local_10,7,"false");
    }
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar2 = mxmlNewElement(iVar2,"soap:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"GetWanplugInfoResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("GetWanplugInfoResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar3 = mxmlNewElement(iVar2,"GetWanplugInfoResult");
            if (iVar3 == 0) {
              mxmlDelete(iVar1);
              puts("GetWanplugInfoResult=NULL");
            }
            else {
              mxmlNewText(iVar3,0,FUN_004ad49c);
              iVar2 = mxmlNewElement(iVar2,"WanStatus");
              if (iVar2 == 0) {
                mxmlDelete(iVar1);
                puts("WanStatus=NULL");
              }
              else {
                mxmlNewText(iVar2,0,local_10);
                if ("" == 0) {
                  __ptr = mxmlSaveAllocString(iVar1,0);
                  if (__ptr != 0) {
                    FUN_0041ed70("",200,__ptr,"");
                    free(__ptr);
                  }
                }
                mxmlDelete(iVar1);
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

