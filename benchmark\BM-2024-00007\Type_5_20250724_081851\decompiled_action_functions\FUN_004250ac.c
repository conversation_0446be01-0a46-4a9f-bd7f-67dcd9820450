
/* WARNING: Type propagation algorithm not settling */

int FUN_004250ac(undefined4 param_1)

{
  uint *__s;
  ushort uVar1;
  undefined **ppuVar2;
  int iVar3;
  short sVar4;
  undefined1 *puVar5;
  int iVar6;
  uint local_f0 [6];
  ushort local_d8;
  uint local_d4;
  ushort local_d0;
  uint local_cc;
  ushort local_c8;
  uint local_c4;
  ushort local_c0;
  uint local_bc;
  uint local_b8;
  undefined auStack_b4 [4];
  undefined auStack_b0 [4];
  undefined auStack_ac [4];
  undefined auStack_a8 [4];
  undefined auStack_a4 [4];
  undefined auStack_a0 [4];
  undefined auStack_9c [4];
  undefined auStack_98 [4];
  undefined auStack_94 [4];
  undefined auStack_90 [4];
  undefined auStack_8c [4];
  undefined auStack_88 [4];
  undefined *local_84;
  undefined *local_80 [27];
  
  local_84 = auStack_b4;
  local_80[1] = auStack_b0;
  local_80[3] = auStack_ac;
  local_80[5] = auStack_a8;
  local_80[7] = auStack_a4;
  local_80[9] = auStack_a0;
  local_80[11] = auStack_9c;
  local_80[13] = auStack_98;
  local_80[15] = auStack_94;
  local_80[17] = auStack_90;
  local_80[19] = auStack_8c;
  local_80[21] = auStack_88;
  local_80[23] = 0;
  ppuVar2 = local_80;
  do {
    *ppuVar2 = 0;
    ppuVar2 = ppuVar2 + 2;
    __s = local_f0 + 5;
  } while (ppuVar2 != local_80 + 24);
  memset(__s,0,"(");
  swGetFirewallDosCfg(__s);
  uVar1 = local_d8;
  memset(__s,0,"(");
  memset(auStack_b4,0,"0");
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar3 = HttpAccessPermit(param_1);
  if (iVar3 == 0) {
    sVar4 = HttpDenyPage(param_1);
    goto LAB_00425830;
  }
  iVar3 = httpGetEnv(param_1,"Save");
  if (iVar3 == 0) {
LAB_0042556c:
    memset(local_f0 + 5,0,"(");
    swGetFirewallDosCfg(local_f0 + 5);
    iVar3 = isEnableAdvSec();
    local_f0[0] = (uint)(iVar3 != 0);
    pageParaSet(&local_84,local_f0,0);
    iVar3 = isEnableAbandonLanPing();
    local_f0[0] = (uint)(iVar3 != 0);
    pageParaSet(&local_84,local_f0,1);
    local_f0[0] = local_d8;
    pageParaSet(&local_84,local_f0,2);
    local_f0[0] = local_f0[5];
    pageParaSet(&local_84,local_f0,3);
    local_f0[0] = local_d4;
    pageParaSet(&local_84,local_f0,4);
    local_f0[0] = local_d0;
    pageParaSet(&local_84,local_f0,5);
    local_f0[0] = local_cc;
    pageParaSet(&local_84,local_f0,6);
    local_f0[0] = local_c8;
    pageParaSet(&local_84,local_f0,7);
    local_f0[0] = local_c4;
    pageParaSet(&local_84,local_f0,8);
    local_f0[0] = local_c0;
    pageParaSet(&local_84,local_f0,9);
    local_f0[0] = local_bc;
    pageParaSet(&local_84,local_f0,10);
    local_f0[0] = local_b8;
    pageParaSet(&local_84,local_f0,11);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "advPara");
    iVar3 = 0;
    do {
      iVar6 = iVar3 + 1;
      pageDynParaPrintf(&local_84,iVar3,param_1);
      iVar3 = iVar6;
    } while (iVar6 != 12);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar3 = httpRpmFsA(param_1,"/userRpm/AdvScrRpm.htm");
    if (iVar3 == 2) {
      return 2;
    }
    iVar3 = 10;
    puVar5 = 0;
  }
  else {
    iVar3 = getEnvToInt(param_1,"dosAttack",2,2);
    local_f0[5] = (uint)(iVar3 != -128);
    iVar3 = isEnableAdvSec();
    if (iVar3 != 0) {
      iVar3 = getEnvToInt(param_1,"interval",0x80000000,0x7fffffff);
      if (iVar3 == -128) {
        local_d8 = 10;
      }
      else {
        local_d8 = iVar3;
      }
      iVar3 = getEnvToInt(param_1,"bIcmpFlood",2,2);
      local_d4 = (uint)(iVar3 != -128);
      iVar3 = getEnvToInt(param_1,"usIcmpFlood",0x80000000,0x7fffffff);
      if (iVar3 == -128) {
        local_d0 = "2";
      }
      else {
        local_d0 = iVar3;
      }
      iVar3 = getEnvToInt(param_1,"bUdpFlood",2,2);
      local_cc = (uint)(iVar3 != -128);
      iVar3 = getEnvToInt(param_1,"usUdpFlood",0x80000000,0x7fffffff);
      if (iVar3 == -128) {
        local_c8 = 500;
      }
      else {
        local_c8 = iVar3;
      }
      iVar3 = getEnvToInt(param_1,"bTcpSynFlood",2,2);
      local_c4 = (uint)(iVar3 != -128);
      iVar3 = getEnvToInt(param_1,"usTcpSynFlood",0x80000000,0x7fffffff);
      if (iVar3 == -128) {
        local_c0 = "2";
      }
      else {
        local_c0 = iVar3;
      }
    }
    iVar3 = getEnvToInt(param_1,"ping",2,2);
    local_bc = (uint)(iVar3 != -128);
    iVar3 = isEnableAbandonLanPing();
    if (iVar3 != 0) {
      iVar3 = getEnvToInt(param_1,"lanPing",2,2);
      if (iVar3 == -128) {
        local_b8 = 0;
      }
      else {
        local_b8 = 1;
      }
    }
    iVar3 = isEnableAdvSec();
    if (iVar3 == 0) goto LAB_0042556c;
    iVar3 = swChkFirewallDosCfg(local_f0 + 5);
    if (iVar3 == 0) {
      if (local_d8 != uVar1) {
        local_f0[1] = 0;
        local_f0[2] = 0;
        local_f0[3] = 0;
        local_f0[4] = 0;
        swGetSysStatCfg(local_f0 + 1);
        local_f0[2] = local_d8;
        swSetSysStatCfg(local_f0 + 1);
      }
      swSetFirewallDosCfg(local_f0 + 5);
      goto LAB_0042556c;
    }
    puVar5 = "";
  }
  sVar4 = HttpErrorPage(param_1,iVar3,puVar5,0);
LAB_00425830:
  return sVar4;
}

