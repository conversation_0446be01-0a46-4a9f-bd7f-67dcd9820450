
int DynDdnsRpmHtm(undefined4 param_1)

{
  char cVar1;
  char *pcVar2;
  int iVar3;
  short sVar4;
  char *pcVar5;
  int iVar6;
  undefined4 local_3c0;
  int local_3bc;
  undefined *local_3b8;
  undefined4 local_3b4;
  undefined *local_3b0;
  undefined4 local_3ac;
  undefined *local_3a8;
  undefined4 local_3a4;
  undefined *local_3a0;
  undefined4 local_39c;
  undefined *local_398;
  undefined4 local_394;
  undefined *local_390;
  undefined4 local_38c;
  undefined *local_388;
  undefined4 local_384;
  undefined *local_380;
  undefined4 local_37c;
  undefined *local_378;
  undefined4 local_374;
  undefined4 local_370;
  undefined auStack_360 [64];
  undefined auStack_320 [64];
  undefined auStack_2e0 [128];
  undefined auStack_260 [4];
  undefined auStack_25c [4];
  undefined auStack_258 [4];
  undefined auStack_254 [4];
  undefined auStack_250 [4];
  undefined auStack_24c [4];
  uint local_248;
  char local_244 [63];
  undefined local_205;
  char local_204 [63];
  undefined local_1c5;
  char local_ac [127];
  undefined local_2d;
  int local_2c;
  
  local_3c0 = 0;
  memset(&local_248,0,560);
  local_3b8 = auStack_360;
  local_3b0 = auStack_320;
  local_3a8 = auStack_2e0;
  local_3a0 = auStack_260;
  local_398 = auStack_25c;
  local_390 = auStack_258;
  local_388 = auStack_254;
  local_380 = auStack_250;
  local_378 = auStack_24c;
  local_3a4 = "@";
  local_3b4 = "@";
  local_3ac = "@";
  local_370 = 0;
  local_39c = 0;
  local_394 = 0;
  local_38c = 0;
  local_384 = 0;
  local_37c = 0;
  local_374 = 0;
  httpStatusSet(param_1,0);
  iVar6 = 0;
  httpHeaderGenerate(param_1);
  pcVar2 = httpGetEnv(param_1,"wan");
  if (pcVar2 != 0) {
    iVar6 = atoi(pcVar2);
    local_2c = iVar6;
  }
  pcVar2 = httpGetEnv(param_1,"provider");
  if (pcVar2 != 0) {
    do {
      cVar1 = *pcVar2;
      pcVar2 = pcVar2 + 1;
    } while (cVar1 == ' ');
  }
  memset(&local_248,0,560);
  swGetDynDdnsCfg(iVar6,&local_248);
  iVar3 = httpGetEnv(param_1,"Save");
  if (iVar3 == 0) {
    iVar3 = httpGetEnv(param_1,"Login");
    if (iVar3 != 0) goto LAB_00447960;
    iVar3 = httpGetEnv(param_1,"Logout");
    if (iVar3 == 0) goto LAB_00447bd0;
    pcVar2 = httpGetEnv(param_1,"wan");
    iVar6 = 0;
    if (pcVar2 != 0) {
      iVar6 = atoi(pcVar2);
    }
    suspendDyn(iVar6);
  }
  else {
LAB_00447960:
    pcVar2 = httpGetEnv(param_1,"username");
    if (pcVar2 == 0) {
      local_244[0] = '\0';
    }
    else {
      do {
        pcVar5 = pcVar2;
        pcVar2 = pcVar5 + 1;
      } while (*pcVar5 == ' ');
      local_205 = 0;
      strncpy(local_244,pcVar5,"?");
    }
    pcVar2 = httpGetEnv(param_1,"pwd");
    if (pcVar2 == 0) {
      local_204[0] = '\0';
    }
    else {
      do {
        pcVar5 = pcVar2;
        pcVar2 = pcVar5 + 1;
      } while (*pcVar5 == ' ');
      local_1c5 = 0;
      strncpy(local_204,pcVar5,"?");
    }
    pcVar2 = httpGetEnv(param_1,"cliUrl");
    if (pcVar2 == 0) {
      local_ac[0] = '\0';
    }
    else {
      do {
        pcVar5 = pcVar2;
        pcVar2 = pcVar5 + 1;
      } while (*pcVar5 == ' ');
      local_2d = 0;
      strncpy(local_ac,pcVar5,127);
    }
    pcVar2 = httpGetEnv(param_1,"EnDdns");
    local_248 = 0;
    if (pcVar2 != 0) {
      iVar6 = atoi(pcVar2);
      local_248 = (uint)(iVar6 == 2);
    }
    pcVar2 = httpGetEnv(param_1,"wan");
    if (pcVar2 == 0) {
      local_2c = 0;
    }
    else {
      local_2c = atoi(pcVar2);
    }
    iVar6 = local_2c;
    swSetDynDdnsCfg(local_2c,&local_248);
    if (((local_ac[0] == '\0') || (local_204[0] == '\0')) || (local_244[0] == '\0'))
    goto LAB_00447bd0;
    dynStart(iVar6);
  }
  usleep(500000);
LAB_00447bd0:
  swGetDynDdnsCfg(iVar6,&local_248);
  pageParaSet(&local_3b8,local_244,0);
  pageParaSet(&local_3b8,local_204,1);
  pageParaSet(&local_3b8,local_ac,2);
  usleep(1500000);
  local_3bc = swGetDynDnsState(iVar6);
  pageParaSet(&local_3b8,&local_3bc,3);
  "" = get_dyn_state(iVar6);
  local_3bc = "";
  pageParaSet(&local_3b8,&local_3bc,4);
  local_3bc = 2;
  pageParaSet(&local_3b8,&local_3bc,5);
  local_3bc = 2;
  pageParaSet(&local_3b8,&local_3bc,6);
  local_3bc = iVar6;
  pageParaSet(&local_3b8,&local_3bc,7);
  local_3c0 = getMaxWanPortNumber();
  pageParaSet(&local_3b8,&local_3c0,8);
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "serInf");
  iVar6 = 0;
  do {
    iVar3 = iVar6 + 1;
    pageDynParaPrintf(&local_3b8,iVar6,param_1);
    iVar6 = iVar3;
  } while (iVar3 != 9);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  httpPrintfDdnsTypeInfo(param_1);
  HttpWebV4Head(param_1,0,1);
  iVar6 = httpRpmFsA(param_1,"/userRpm/DynDdnsRpm.htm");
  iVar3 = 2;
  if (iVar6 != 2) {
    sVar4 = HttpErrorPage(param_1,10,0,0);
    iVar3 = sVar4;
  }
  return iVar3;
}

