
int FUN_00459bcc(undefined4 param_1)

{
  int iVar1;
  short sVar2;
  undefined4 local_10;
  undefined4 local_c;
  
  local_10 = 0;
  local_c = 0;
  iVar1 = swGetLogData(&local_10,&local_c);
  if (iVar1 == 0) {
    httpStatusSet(param_1,0);
    httpMimeContentTypeSet(param_1,1,"x-bin/octet-stream");
    httpMimeHdrSet(param_1,1,"Content-disposition","attachment;filename=\"Syslog.txt\"");
    httpHeaderGenerate(param_1);
    httpBlockPut(param_1,local_10,local_c);
    iVar1 = 2;
  }
  else {
    sVar2 = HttpErrorPage(param_1,iVar1,"../userRpm/SystemLogRpm.htm",0);
    iVar1 = sVar2;
  }
  return iVar1;
}

