
FILE * FUN_004504f0(undefined4 param_1)

{
  FILE *__stream;
  size_t sVar1;
  undefined auStack_19018 [102404];
  
  __stream = fopen("/tmp/tr069/cpe.log","r");
  if (__stream != 0) {
    sVar1 = fread(auStack_19018,1,0x18fff,__stream);
    httpStatusSet(param_1,0);
    httpMimeContentTypeSet(param_1,1,"x-bin/octet-stream");
    httpMimeHdrSet(param_1,1,"Content-disposition","attachment;filename=\"logger.txt\"");
    httpHeaderGenerate(param_1);
    httpBlockPut(param_1,auStack_19018,sVar1);
    fclose(__stream);
    __stream = 2;
  }
  return __stream;
}

