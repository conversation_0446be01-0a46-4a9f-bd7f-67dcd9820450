
int FUN_0044d9b0(undefined4 param_1)

{
  int iVar1;
  short sVar4;
  char *pcVar2;
  int iVar3;
  undefined4 uVar5;
  code *pcVar6;
  uint local_900;
  undefined4 local_8fc;
  uint local_8f8;
  int local_8f4;
  char local_8f0 [44];
  char acStack_8c4 [44];
  undefined auStack_898 [44];
  undefined auStack_86c [44];
  undefined local_840;
  undefined auStack_83f [43];
  undefined local_814;
  undefined auStack_813 [43];
  undefined *local_7e8;
  undefined4 local_7e4;
  undefined *local_7e0;
  undefined4 local_7dc;
  undefined *local_7d8;
  undefined4 local_7d4;
  undefined *local_7d0;
  undefined4 local_7cc;
  undefined *local_7c8;
  undefined4 local_7c4;
  undefined *local_7c0;
  undefined4 local_7bc;
  undefined *local_7b8;
  undefined4 local_7b4;
  undefined *local_7b0;
  undefined4 local_7ac;
  undefined *local_7a8;
  undefined4 local_7a4;
  undefined *local_7a0;
  undefined4 local_79c;
  undefined *local_798;
  undefined4 local_794;
  undefined *local_790;
  undefined4 local_78c;
  undefined *local_788;
  undefined4 local_784;
  undefined *local_780;
  undefined4 local_77c;
  undefined *local_778;
  undefined4 local_774;
  undefined *local_770;
  undefined4 local_76c;
  undefined *local_768;
  undefined4 local_764;
  undefined *local_760;
  undefined4 local_75c;
  undefined *local_758;
  undefined4 local_754;
  undefined4 local_750;
  undefined4 local_74c;
  char acStack_748 [120];
  char acStack_6d0 [280];
  undefined auStack_5b8 [8];
  int local_5b0;
  char acStack_5ac [120];
  char acStack_534 [120];
  int local_4bc;
  char acStack_4b8 [44];
  int local_48c;
  int local_488;
  uint local_484;
  char acStack_480 [45];
  char acStack_453 [47];
  undefined auStack_424 [8];
  int local_41c;
  undefined auStack_290 [4];
  undefined auStack_28c [4];
  undefined auStack_288 [4];
  undefined auStack_284 [120];
  undefined auStack_20c [120];
  undefined auStack_194 [4];
  undefined auStack_190 [44];
  undefined auStack_164 [44];
  undefined auStack_138 [44];
  undefined auStack_10c [44];
  undefined auStack_e0 [4];
  undefined auStack_dc [4];
  undefined auStack_d8 [4];
  undefined auStack_d4 [4];
  undefined auStack_d0 [4];
  undefined auStack_cc [45];
  undefined auStack_9f [45];
  undefined auStack_72 [45];
  undefined auStack_45 [49];
  
  local_900 = 0;
  local_8fc = 0;
  memset(local_8f0,0,",");
  memset(acStack_8c4,0,",");
  memset(auStack_898,0,",");
  memset(auStack_86c,0,",");
  local_840 = 0;
  memset(auStack_83f,0,"+");
  local_814 = 0;
  memset(auStack_813,0,"+");
  local_7e0 = auStack_28c;
  local_7d8 = auStack_288;
  local_7d0 = auStack_284;
  local_7c8 = auStack_20c;
  local_7c0 = auStack_194;
  local_7b8 = auStack_190;
  local_7b0 = auStack_164;
  local_7a8 = auStack_138;
  local_7a0 = auStack_10c;
  local_798 = auStack_e0;
  local_790 = auStack_dc;
  local_788 = auStack_d8;
  local_780 = auStack_d4;
  local_778 = auStack_d0;
  local_770 = auStack_cc;
  local_768 = auStack_9f;
  local_760 = auStack_72;
  local_7e8 = auStack_290;
  local_758 = auStack_45;
  local_7b4 = ",";
  local_7ac = ",";
  local_7a4 = ",";
  local_79c = ",";
  local_7c4 = "x";
  local_7cc = "x";
  local_8f8 = 0;
  local_8f4 = 0;
  local_750 = 0;
  local_7e4 = 0;
  local_7dc = 0;
  local_7d4 = 0;
  local_7bc = 0;
  local_794 = 0;
  local_754 = ",";
  local_76c = ",";
  local_764 = ",";
  local_75c = ",";
  local_78c = 0;
  local_784 = 0;
  local_77c = 0;
  local_774 = 0;
  local_74c = 0;
  memset(local_7e8,0,632);
  memset(auStack_5b8,0,404);
  memset(auStack_424,0,404);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar4 = HttpDenyPage(param_1);
    goto LAB_0044e65c;
  }
  swGetPppoeV6Cfg(0,auStack_5b8);
  iVar1 = httpGetEnv(param_1,"Connect");
  if ((iVar1 != 0) || (iVar1 = httpGetEnv(param_1,"Save"), iVar1 != 0)) {
    iVar1 = httpGetEnv(param_1,"ipv6Enable");
    if (iVar1 == 0) {
      IPV6_ECHO("ucSetIPv6Enable(FALSE);");
    }
    else {
      IPV6_ECHO("ucSetIPv6Enable(TRUE);");
    }
    ucSetIPv6Enable(iVar1 != 0);
    pcVar2 = httpGetEnv(param_1,"pppoeSession");
    if (pcVar2 != 0) {
      local_5b0 = atoi(pcVar2);
    }
    pcVar2 = httpGetEnv(param_1,"username");
    if (pcVar2 != 0) {
      strcpy(acStack_5ac,pcVar2);
    }
    pcVar2 = httpGetEnv(param_1,"password");
    if (pcVar2 != 0) {
      strcpy(acStack_534,pcVar2);
    }
    pcVar2 = httpGetEnv(param_1,"getipv6way");
    if (pcVar2 != 0) {
      local_4bc = atoi(pcVar2);
    }
    if (local_4bc == 2) {
      pcVar2 = httpGetEnv(param_1,"fixedIp");
      if (pcVar2 == 0) {
        pcVar2 = "";
      }
      strcpy(acStack_4b8,pcVar2);
      IPV6_ECHO("%s %d wanPppoev6Cfg.ipUsrConfig = %s\r\n","PPPoEv6CfgRpmHtm",190,acStack_4b8);
    }
    pcVar2 = httpGetEnv(param_1,"mtu");
    if (pcVar2 != 0) {
      local_48c = atoi(pcVar2);
    }
    pcVar2 = httpGetEnv(param_1,"dnsType");
    if (pcVar2 != 0) {
      iVar1 = atoi(pcVar2);
      IPV6_ECHO("%s %d atoi(pszStr) = %d\r\n","PPPoEv6CfgRpmHtm",202,iVar1);
      iVar1 = atoi(pcVar2);
      local_484 = (uint)(iVar1 == 0);
    }
    pcVar2 = httpGetEnv(param_1,"dnsserver1");
    if (pcVar2 == 0) {
      memset(acStack_480,0,"-");
    }
    else {
      strcpy(acStack_480,pcVar2);
    }
    pcVar2 = httpGetEnv(param_1,"dnsserver2");
    if (pcVar2 == 0) {
      memset(acStack_453,0,"-");
    }
    else {
      strcpy(acStack_453,pcVar2);
    }
    pcVar2 = httpGetEnv(param_1,"connectMode");
    if (pcVar2 != 0) {
      local_488 = atoi(pcVar2);
    }
    iVar1 = local_4bc;
    iVar3 = swGetPppObtainIpWay();
    if (iVar1 != iVar3) {
      stopCurrentConnection(4);
    }
    swGetPppoeV6Cfg(0,auStack_424);
    if ((local_41c != local_5b0) && (stopCurrentConnection(4), local_41c == 0)) {
      gPppoeStartDial = 1;
    }
    swSetPppoeV6Cfg(auStack_5b8);
    if (local_5b0 == 0) {
      swGetPppoeCfg(acStack_748);
      strncpy(acStack_748,acStack_5ac,"x");
      strncpy(acStack_6d0,acStack_534,"x");
      swSetPppoeCfg(acStack_748);
      pppUserStop();
    }
    iVar1 = memcmp(auStack_5b8,auStack_424,404);
    if ((iVar1 != 0) || (iVar1 = swGetWanIpv6Type(), iVar1 != 4)) {
      swPppoev6LinkUpReq();
      sleep(3);
    }
  }
  iVar1 = httpGetEnv(param_1,"Connect");
  if (iVar1 == 0) {
    iVar1 = httpGetEnv(param_1,"Disconnect");
    if (iVar1 != 0) {
      pcVar6 = swPppoev6LinkDownReq;
      goto LAB_0044e1c0;
    }
  }
  else {
    pcVar6 = swPppoev6LinkUpReq;
LAB_0044e1c0:
    (*pcVar6)();
    sleep(1);
  }
  LanIpv6RpmHtm(param_1,4);
  local_900 = swGetIPv6Enable();
  pageParaSet(&local_7e8,&local_900,0);
  local_900 = 4;
  pageParaSet(&local_7e8,&local_900,1);
  pageParaSet(&local_7e8,&local_5b0,2);
  pageParaSet(&local_7e8,acStack_5ac,3);
  pageParaSet(&local_7e8,acStack_534,4);
  pageParaSet(&local_7e8,&local_4bc,5);
  swPppoev6LinkStateGet(&local_8f4);
  if (local_8f4 == 1) {
    iVar1 = swGetPppObtainIpWay();
    if (iVar1 == 0) {
      uVar5 = getWanIpv6IfName();
      getSlaacParameters(uVar5,local_8f0,auStack_898,&local_8fc);
      uVar5 = ",";
      pcVar6 = simplify_slaac_addr;
LAB_0044e358:
      (*pcVar6)(local_8f0,uVar5);
    }
    else if (iVar1 == 1) {
      pcVar6 = swGetDhcpv6Ip;
      uVar5 = 4;
      goto LAB_0044e358;
    }
    if (((local_8f0[0] != '\0') && (local_8f0[0] != '\0')) &&
       (iVar1 = swGetPppPrefixAndLength(auStack_898,&local_8fc), iVar1 == 0)) {
      sprintf(acStack_8c4,"%s/%d",auStack_898,local_8fc);
    }
    pageParaSet(&local_7e8,local_8f0,6);
    pageParaSet(&local_7e8,acStack_8c4,7);
    swGetRunningGateway(auStack_86c,4);
    pageParaSet(&local_7e8,auStack_86c,8);
  }
  pageParaSet(&local_7e8,acStack_4b8,9);
  pageParaSet(&local_7e8,&local_48c,10);
  pageParaSet(&local_7e8,&local_488,11);
  local_8f8 = (uint)(local_8f4 - 1U < 2);
  pageParaSet(&local_7e8,&local_8f8,12);
  pageParaSet(&local_7e8,&local_8f4,13);
  local_900 = (uint)(local_484 != 1);
  pageParaSet(&local_7e8,&local_900,14);
  pageParaSet(&local_7e8,acStack_480,15);
  pageParaSet(&local_7e8,acStack_453,16);
  swGetDhcpv6Dns(&local_840,&local_814,4);
  pageParaSet(&local_7e8,&local_840,17);
  pageParaSet(&local_7e8,&local_814,18);
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "pppoev6Inf");
  iVar1 = 0;
  do {
    iVar3 = iVar1 + 1;
    pageDynParaPrintf(&local_7e8,iVar1,param_1);
    iVar1 = iVar3;
  } while (iVar3 != 19);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  httpPrintfWanIpv6TypeInfo(param_1);
  HttpWebV4Head(param_1,0,1);
  iVar1 = httpRpmFsA(param_1,"/userRpm/PPPoEv6CfgRpm.htm");
  if (iVar1 == 2) {
    return 2;
  }
  sVar4 = HttpErrorPage(param_1,10,0,0);
LAB_0044e65c:
  return sVar4;
}

