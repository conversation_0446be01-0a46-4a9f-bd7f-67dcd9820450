
void DdnsAddRpmHtm(undefined4 param_1)

{
  char *pcVar1;
  int iVar2;
  
  pcVar1 = httpGetEnv(param_1,"wan");
  iVar2 = 0;
  if (pcVar1 != 0) {
    iVar2 = atoi(pcVar1);
  }
  pcVar1 = httpGetEnv(param_1,"provider");
  if (pcVar1 == 0) {
    iVar2 = swGetDdnsProviderIndex(iVar2);
  }
  else {
    iVar2 = atoi(pcVar1);
  }
  printf("selectedIndex:%d\n",iVar2);
  if (iVar2 == 0) {
    PeanutHullDdnsRpmHtm(param_1);
    return;
  }
  if (iVar2 == 2) {
                    /* WARNING: Could not recover jumptable at DdnsAddRpmHtm. Too many branches */
                    /* WARNING: Treating indirect jump as call */
    DynDdnsRpmHtm(param_1);
    return;
  }
  if (iVar2 == 3) {
    NoipDdnsRpmHtm(param_1);
    return;
  }
  if (iVar2 == 1) {
    CmxDdnsRpmHtm(param_1);
    return;
  }
  return;
}

