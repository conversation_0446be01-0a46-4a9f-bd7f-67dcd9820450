
int FUN_00425ae0(undefined4 param_1)

{
  bool bVar1;
  uint **ppuVar2;
  int iVar3;
  short sVar6;
  char *pcVar4;
  undefined4 uVar5;
  int iVar7;
  uint uVar8;
  long lVar9;
  undefined4 *local_100;
  undefined4 local_fc;
  undefined4 local_f8;
  undefined4 local_f0;
  undefined4 local_ec;
  undefined4 local_e8;
  undefined4 local_e4;
  undefined4 local_e0;
  undefined4 local_dc;
  undefined local_d8;
  undefined4 local_d4;
  undefined4 local_d0;
  char acStack_c4 [24];
  undefined local_ac;
  byte local_ab;
  byte local_aa;
  byte local_a9;
  char acStack_a8 [24];
  undefined local_90;
  uint local_8c;
  uint local_88;
  uint local_84;
  undefined4 local_80;
  uint local_7c;
  uint local_78;
  uint local_74;
  long local_70;
  char *local_6c;
  undefined4 local_68;
  uint *local_64;
  uint *local_60 [18];
  
  local_6c = acStack_a8;
  local_64 = &local_8c;
  local_60[1] = &local_88;
  local_60[3] = &local_84;
  local_60[5] = &local_80;
  local_60[7] = &local_7c;
  local_60[9] = &local_78;
  local_60[11] = &local_74;
  local_60[13] = &local_70;
  local_68 = 25;
  local_60[15] = 0;
  ppuVar2 = local_60;
  do {
    *ppuVar2 = 0;
    ppuVar2 = ppuVar2 + 2;
  } while (ppuVar2 != local_60 + 16);
  local_100 = &local_f0;
  local_fc = 25;
  local_f8 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar3 = HttpAccessPermit(param_1);
  if (iVar3 == 0) {
    sVar6 = HttpDenyPage(param_1);
    goto LAB_00426288;
  }
  pcVar4 = httpGetEnv(param_1,"ClientId");
  lVar9 = 0;
  if (pcVar4 != 0) {
    lVar9 = atol(pcVar4);
  }
  memset(acStack_a8,0,"<");
  local_d8 = 0;
  local_f0 = 0;
  local_ec = 0;
  local_e8 = 0;
  local_e4 = 0;
  local_e0 = 0;
  local_dc = 0;
  memset(&local_d4,0,",");
  if ("" == 0) {
    memset(l_ruleEntry,0,",");
  }
  local_7c = swGetFilterEntryNumCfg(0);
  local_78 = swGetFilterEntryNumCfg(1);
  local_74 = swGetFilterEntryNumCfg(2);
  iVar3 = httpGetEnv(param_1,"Return");
  if (iVar3 == 0) {
    iVar3 = httpGetEnv(param_1,"Next");
    if ((iVar3 == 0) && (iVar3 = httpGetEnv(param_1,"Complete"), iVar3 == 0)) {
      iVar3 = swChkEntryName(3,0x5e4a38);
      if (iVar3 == 0) {
        "" = 0;
      }
      local_80 = 1;
      if ("" == 1) {
        local_90 = 0;
        strncpy(acStack_a8,l_ruleEntry + 16,24);
        local_8c = l_ruleEntry[41];
        local_88 = l_ruleEntry[42];
        local_84 = l_ruleEntry[43];
        local_80 = l_ruleEntry._4_4_;
      }
      else {
        local_8c = local_7c - 1;
        local_88 = local_78 - 1;
        local_84 = local_74 - 1;
      }
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "host_list_dyn_array");
      uVar8 = 0;
      while (uVar8 < local_7c) {
        local_f0 = 0;
        local_ec = 0;
        local_e8 = 0;
        local_e4 = 0;
        local_e0 = 0;
        local_dc = 0;
        local_d8 = 0;
        swGetRuleNamesBySeq(&local_f0,0,uVar8);
        pageDynParaListPrintf(&local_100,param_1);
        uVar8 = uVar8 + 1;
      }
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "target_list_dyn_array");
      uVar8 = 0;
      while (uVar8 < local_78) {
        local_f0 = 0;
        local_ec = 0;
        local_e8 = 0;
        local_e4 = 0;
        local_e0 = 0;
        local_dc = 0;
        local_d8 = 0;
        swGetRuleNamesBySeq(&local_f0,1,uVar8);
        pageDynParaListPrintf(&local_100,param_1);
        uVar8 = uVar8 + 1;
      }
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "sched_list_dyn_array");
      uVar8 = 0;
      while (uVar8 < local_74) {
        local_f0 = 0;
        local_ec = 0;
        local_e8 = 0;
        local_e4 = 0;
        local_e0 = 0;
        local_dc = 0;
        local_d8 = 0;
        swGetRuleNamesBySeq(&local_f0,2,uVar8);
        pageDynParaListPrintf(&local_100,param_1);
        uVar8 = uVar8 + 1;
      }
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      local_70 = lVar9;
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "access_rules_adv_dyn_array");
      iVar3 = 0;
      do {
        iVar7 = iVar3 + 1;
        pageDynParaPrintf(&local_6c,iVar3,param_1);
        iVar3 = iVar7;
      } while (iVar7 != 9);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      HttpWebV4Head(param_1,0,1);
      iVar3 = httpRpmFsA(param_1,"/userRpm/WzdAccessCtrlRuleAddRpm.htm");
      if (iVar3 == 2) {
        return 2;
      }
      iVar3 = 10;
    }
    else {
      printf("uSubmitType= HTTP_BUT_NEXT_STEP;");
      memset(&local_d4,0,",");
      puts("access_rules: Here is save!");
      local_d4 = 1;
      pcVar4 = httpGetEnv(param_1,"rule_name");
      if (pcVar4 != 0) {
        local_ac = 0;
        strncpy(acStack_c4,pcVar4,24);
      }
      local_ab = getEnvToInt(param_1,"hosts_lists",0,255);
      local_aa = getEnvToInt(param_1,"targets_lists",0,255);
      local_a9 = getEnvToInt(param_1,"scheds_lists",0,255);
      printf("List NO:%d,%d,%d\r\n",local_ab,local_aa,local_a9);
      local_d0 = getEnvToInt(param_1,"enable",0,1);
      bVar1 = "" != 0;
      if (bVar1) {
        iVar3 = swGetFilterEntryNumCfg(3);
        iVar3 = iVar3 + -1;
      }
      else {
        iVar3 = 0;
      }
      uVar5 = swSetAccessRulesEntry(&local_d4,iVar3,bVar1,1);
      iVar3 = swFilterFindErrorNum(uVar5);
      if (iVar3 == 0) {
        memcpy(l_ruleEntry,&local_d4,",");
        "" = 1;
        FUN_00425a48();
        pcVar4 = "../userRpm/AccessCtrlAccessRulesRpm.htm";
        goto LAB_00425f48;
      }
    }
    sVar6 = HttpErrorPage(param_1,iVar3,0,0);
  }
  else {
    pcVar4 = "../userRpm/WzdAccessCtrlSchedAddRpm.htm";
LAB_00425f48:
    sVar6 = GoUrl(param_1,pcVar4);
  }
LAB_00426288:
  return sVar6;
}

