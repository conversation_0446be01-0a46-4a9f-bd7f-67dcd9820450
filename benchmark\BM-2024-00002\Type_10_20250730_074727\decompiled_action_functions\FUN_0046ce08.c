
undefined4 FUN_0046ce08(undefined4 param_1)

{
  int iVar1;
  int iVar2;
  int local_16c;
  undefined auStack_15c [340];
  
  iVar1 = mxmlLoadString(0,param_1,0);
  iVar2 = mxmlFindElement(iVar1,iVar1,"soap:Envelope",0,0,1);
  if ((iVar1 == 0) || (iVar2 == 0)) {
    fwrite("ParControl: GetParentsControl build the tree failed\n",1,"4",stderr);
  }
  else {
    local_16c = mxmlFindElement(iVar2,iVar1,"SetParentsControlInfo",0,0,1);
    if (local_16c == 0) {
      fwrite("ParControl: SetParentsControlInfo build the tree failed\n",1,"8",stderr);
      mxmlDelete(iVar1);
    }
    else {
      iVar2 = apmib_set(0x41cba,auStack_15c);
      if (iVar2 == 0) {
        fwrite("Del all failed! exit!",1,21,stderr);
        mxmlDelete(iVar1);
      }
      else {
        while ((local_16c != 0 &&
               (local_16c = mxmlFindElement(local_16c,iVar1,"UsersInfo",0,0,1), local_16c != 0))) {
          FUN_0046b698(local_16c,iVar1);
        }
        iVar2 = FUN_00469138();
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          system("sysconf firewall &");
        }
        else {
          fwrite("ParControl: ReplyParentsControlXmlSetOk failed\n",1,"/",stderr);
          mxmlDelete(iVar1);
        }
      }
    }
  }
  return 0;
}

