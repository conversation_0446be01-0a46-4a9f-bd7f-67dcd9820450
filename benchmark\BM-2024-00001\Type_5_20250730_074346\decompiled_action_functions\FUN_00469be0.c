
/* WARNING: Removing unreachable block (ram,FUN_00469bec) */
/* WARNING: Type propagation algorithm not settling */

int FUN_00469be0(undefined4 param_1)

{
  int iVar1;
  int iVar2;
  short sVar10;
  uint32_t uVar3;
  uint32_t uVar4;
  uint32_t uVar5;
  uint32_t uVar6;
  undefined4 uVar7;
  undefined1 *puVar8;
  char *pcVar9;
  size_t __n;
  uint uVar11;
  uint uStack_e28;
  uint uStack_e24;
  uint uStack_e20;
  uint uStack_e1c;
  uint uStack_e18;
  uint auStack_e14 [4];
  uint32_t auStack_e04 [4];
  char acStack_df4 [16];
  char acStack_de4 [20];
  char acStack_dd0 [20];
  undefined auStack_dbc [24];
  char acStack_da4 [36];
  char acStack_d80 [36];
  uint uStack_d5c;
  uint uStack_d58;
  uint uStack_d54;
  uint auStack_d50 [5];
  undefined auStack_d3c [4];
  int iStack_d38;
  int iStack_d34;
  undefined auStack_d20 [348];
  undefined uStack_bc4;
  byte bStack_bc3;
  byte abStack_bc1 [40];
  byte bStack_b99;
  ushort uStack_b98;
  byte abStack_b96 [2902];
  undefined4 uStack_40;
  char *pcStack_3c;
  char *pcStack_38;
  undefined *puStack_34;
  uint *puStack_30;
  uint *puStack_2c;
  
  uStack_e28 = 0;
  uStack_e24 = 0;
  uStack_e20 = 0;
  uStack_e1c = 0;
  uStack_e18 = 0;
  iVar1 = swGetBoardType();
  swWlanBasicCfgGet(0,auStack_d3c);
  swGetSystemMode(auStack_e14 + 2);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    iVar2 = getRefererFlag();
    if (iVar2 == 0) {
      sVar10 = HttpDenyPage(param_1);
      goto LAB_00469ba0;
    }
    setRefererFlag(0);
  }
  memset(&uStack_bc4,0,0xb82);
  iVar2 = swGetBoardType();
  if (iVar2 == 0) {
    iVar2 = httpGetEnv(param_1,"getWdsResult");
    if (iVar2 != 0) {
      swWlanWDSScan(0,&uStack_bc4,0);
      goto LAB_00468f84;
    }
    memcpy(auStack_dbc,"popupSiteSurveyRpm.htm",23);
    auStack_e14[1] = 25;
    uStack_40 = httpGetEnv(param_1,"QUERY_STRING");
    swGetLanCfg(auStack_e04);
    uVar3 = ntohl(auStack_e04[0]);
    uVar4 = ntohl(auStack_e04[0]);
    uVar5 = ntohl(auStack_e04[0]);
    uVar6 = ntohl(auStack_e04[0]);
    sprintf(acStack_df4,"%d.%d.%d.%d",uVar3 >> 24,uVar4 >> 16 & 255,(int)(uVar5 & -256) >> 8,
            uVar6 & 255);
    swWlanWDSScan(0,&uStack_bc4,1);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "waitWdsInf");
    writePageParamSet(param_1,""%s",",auStack_dbc,0);
    writePageParamSet(param_1,""%s",",uStack_40,1);
    writePageParamSet(param_1,"%d,",auStack_e14 + 1,2);
    writePageParamSet(param_1,""%s",",acStack_df4,3);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    pcVar9 = "/userRpm/WaitForWdsScanResult.htm";
  }
  else {
    swWlanActivateScan(0,&uStack_bc4);
LAB_00468f84:
    if (auStack_e14[2] == 4) {
      iVar2 = 1;
      iStack_d38 = iStack_d34;
LAB_00468fd4:
      if (iStack_d38 == iVar2) {
        uVar7 = wlanGetApDevName(0);
        swWlanInactiveVap(uVar7);
      }
    }
    else if (auStack_e14[2] < 5) {
      if (auStack_e14[2] == 3) {
LAB_00468fc0:
        iVar2 = 3;
        goto LAB_00468fd4;
      }
    }
    else if (auStack_e14[2] - 6 < 3) goto LAB_00468fc0;
    uVar11 = bStack_bc3;
    uStack_e18 = uVar11;
    if (iVar1 == 0) {
      for (uStack_e1c = 0; uStack_e1c < uVar11; uStack_e1c = uStack_e1c + 1) {
        uStack_e24 = (uint)*(ushort *)(&bStack_b99 + uStack_e1c * "/");
        if ((uStack_e24 - "4" < 13) || (uStack_e24 - 100 < ")")) {
          HTTP_DEBUG_PRINT("wireless/httpWlanCfg.c:257",
                           "**: filter out ap working on dfs chan(%d)\n",uStack_e24);
          uStack_e18 = uStack_e18 - 1;
        }
      }
    }
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "siteSurveyPara");
    uStack_e1c = 2;
    writePageParamSet(param_1,"%d,",&uStack_e1c,0);
    writePageParamSet(param_1,"%d,",&uStack_e18,1);
    if (((auStack_e14[2] - 7 < 2) || (auStack_e14[2] == 3)) || (auStack_e14[2] == 6)) {
      puVar8 = httpGetEnv(param_1,"iMAC");
      if (puVar8 == 0) {
        
      }
      writePageParamSet(param_1,""%s",",puVar8,0);
      puVar8 = httpGetEnv(param_1,"iSSID");
      if (puVar8 == 0) {
        
      }
      writePageParamSet(param_1,""%s",",puVar8,0);
      puVar8 = httpGetEnv(param_1,"iWdsChan");
      if (puVar8 == 0) {
        
      }
      writePageParamSet(param_1,""%s",",puVar8,0);
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "mptBssid");
    for (uStack_e1c = 0; uStack_e1c < 4; uStack_e1c = uStack_e1c + 1) {
      writePageParamSet(param_1,""%s",",auStack_d20 + uStack_e1c * ",",uStack_e1c);
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "siteList");
    pcStack_3c = acStack_de4;
    pcStack_38 = acStack_dd0;
    puStack_34 = &uStack_bc4;
    puStack_30 = &uStack_e24;
    puStack_2c = &uStack_e20;
    for (uStack_e1c = 0; uStack_e1c < uVar11; uStack_e1c = uStack_e1c + 1) {
      if ((iVar1 != 0) ||
         ((uStack_e24 = (uint)*(ushort *)(&bStack_b99 + uStack_e1c * "/"),
          12 < uStack_e24 - "4" && ("(" < uStack_e24 - 100)))) {
        iVar2 = uStack_e1c * ".";
        sprintf(pcStack_3c,"%02X-%02X-%02X-%02X-%02X-%02X",abStack_bc1[iVar2],
                abStack_bc1[iVar2 + 1],abStack_bc1[(uStack_e1c * 0x00000018) * 2],
                abStack_bc1[iVar2 + 3],abStack_bc1[iVar2 + 4],
                abStack_bc1[iVar2 + 5]);
        strncpy(pcStack_38,pcStack_3c,18);
        writePageParamSet(param_1,""%s",",pcStack_38,0);
        strncpy(acStack_da4,puStack_34 + uStack_e1c * "8","!");
        writePageParamSet(param_1,""%s",",acStack_da4,1);
        uStack_e28 = (uint)(&bStack_b99)[uStack_e1c * 46];
        writePageParamSet(param_1,"%d,",&uStack_e28,2);
        if ((iVar1 == 4) || ((iVar1 == 7 || (iVar1 == 9)))) {
          uStack_e24 = (int)(*(ushort *)(&bStack_b99 + uStack_e1c * "/") - 0x967) / 5;
          if (14 < uStack_e24) {
            
          }
        }
        else {
          uStack_e24 = (uint)*(ushort *)(&bStack_b99 + uStack_e1c * "/");
        }
        writePageParamSet(param_1,"%d,",puStack_30,3);
        uStack_e20 = (uint)(&bStack_b99)[uStack_e1c * 0x00000031];
        writePageParamSet(param_1,"%d,",puStack_2c,4);
      }
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    memset(acStack_d80,0,"D");
    uStack_e1c = 0;
    pcVar9 = httpGetEnv(param_1,"ssid");
    if (pcVar9 == 0) {
      acStack_d80[0] = '\0';
    }
    else {
      __n = strlen(pcVar9);
      strncpy(acStack_d80,pcVar9,__n);
    }
    pcVar9 = httpGetEnv(param_1,"curRegion");
    if (pcVar9 == 0) {
      
    }
    else {
      uStack_e1c = atoi(pcVar9);
      if (uStack_e1c < "l") {
        uStack_d5c = uStack_e1c;
      }
    }
    pcVar9 = httpGetEnv(param_1,"channel");
    if (pcVar9 == 0) {
      uStack_d58 = 6;
    }
    else {
      uStack_e1c = atoi(pcVar9);
      if (uStack_e1c - 1 < 15) {
        uStack_d58 = uStack_e1c;
      }
    }
    pcVar9 = httpGetEnv(param_1,"chanWidth");
    if (pcVar9 == 0) {
      uStack_d54 = 2;
    }
    else {
      uStack_e1c = atoi(pcVar9);
      if (uStack_e1c - 1 < 3) {
        uStack_d54 = uStack_e1c;
      }
    }
    pcVar9 = httpGetEnv(param_1,"mode");
    if (pcVar9 == 0) {
      auStack_d50[0] = 1;
    }
    else {
      uStack_e1c = atoi(pcVar9);
      if (uStack_e1c - 1 < 9) {
        auStack_d50[0] = uStack_e1c;
      }
    }
    pcVar9 = httpGetEnv(param_1,"wrr");
    if (pcVar9 != 0) {
      iVar1 = strcmp(pcVar9,"true");
      if ((iVar1 == 0) || (iVar1 = atoi(pcVar9), iVar1 == 1)) {
        auStack_d50[1] = 1;
      }
      else {
        auStack_d50[1] = 0;
      }
    }
    pcVar9 = httpGetEnv(param_1,"s");
    if (pcVar9 != 0) {
      iVar1 = strcmp(pcVar9,"true");
      if ((iVar1 == 0) || (iVar1 = atoi(pcVar9), iVar1 == 1)) {
        auStack_d50[2] = 1;
      }
      else {
        auStack_d50[2] = 0;
      }
    }
    pcVar9 = httpGetEnv(param_1,"select");
    if (pcVar9 != 0) {
      iVar1 = strcmp(pcVar9,"true");
      if ((iVar1 == 0) || (iVar1 = atoi(pcVar9), iVar1 == 1)) {
        auStack_d50[3] = 1;
      }
      else {
        auStack_d50[3] = 0;
      }
    }
    pcVar9 = httpGetEnv(param_1,"rate");
    if (pcVar9 != 0) {
      auStack_d50[4] = atoi(pcVar9);
    }
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "pagePara");
    writePageParamSet(param_1,""%s",",acStack_d80,0);
    writePageParamSet(param_1,"%d,",&uStack_d5c,1);
    writePageParamSet(param_1,"%d,",&uStack_d58,2);
    writePageParamSet(param_1,"%d,",&uStack_d54,3);
    writePageParamSet(param_1,"%d,",auStack_d50,4);
    writePageParamSet(param_1,"%d,",auStack_d50 + 1,5);
    writePageParamSet(param_1,"%d,",auStack_d50 + 2,6);
    writePageParamSet(param_1,"%d,",auStack_d50 + 3,7);
    writePageParamSet(param_1,"%d,",auStack_d50 + 4,8);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    iVar1 = getWifiScanWithoutWds();
    if (iVar1 != 0) {
      iVar1 = httpGetEnv(param_1,"wifiScan");
      auStack_e14[0] = (uint)(iVar1 != 0);
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "wifiScan");
      writePageParamSet(param_1,"%d,",auStack_e14,0);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    }
    httpPrintf(param_1,"<script language=JavaScript>\nvar isInScanning = 0;\n</script>");
    if ((auStack_e14[2] < 9) && ((1 << (auStack_e14[2] & 31) & 0x1c8U) != 0)) {
      HttpWebV4Head(param_1,0,0);
      pcVar9 = "/userRpm/popupSiteSurveyRpm_AP.htm";
    }
    else {
      HttpWebV4Head(param_1,0,1);
      pcVar9 = "/userRpm/popupSiteSurveyRpm.htm";
    }
  }
  iVar1 = httpRpmFsA(param_1,pcVar9);
  if (iVar1 == 2) {
    return 2;
  }
  sVar10 = HttpErrorPage(param_1,10,0,0);
LAB_00469ba0:
  return sVar10;
}

