
int FUN_00457434(void)

{
  int iVar1;
  int iVar2;
  int iVar3;
  FILE *__stream;
  int iVar4;
  char *pcVar5;
  void *pvVar6;
  int local_26c;
  char acStack_268 [256];
  stat sStack_168;
  undefined auStack_d0 [32];
  undefined auStack_b0 [32];
  undefined auStack_90 [32];
  undefined auStack_70 [32];
  undefined auStack_50 [32];
  undefined auStack_30 [32];
  undefined auStack_10 [8];
  
  memset(acStack_268,0,256);
  iVar1 = apmib_get(142,&local_26c);
  if (iVar1 == 0) {
    printf("Set MIB_UPNP_ENABLED error!");
  }
  iVar1 = mxmlNewXML("1.0");
  if (iVar1 == 0) {
    printf("xml is NULL!");
  }
  else {
    iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
    if (iVar2 == 0) {
      printf("soap_env is NULL!");
    }
    else {
      mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
      mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
      mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
      iVar2 = mxmlNewElement(iVar2,"soap:Body");
      if (iVar2 == 0) {
        printf("body is NULL!");
      }
      else {
        iVar2 = mxmlNewElement(iVar2,"GetUpnpSettingsResponse");
        if (iVar2 == 0) {
          printf("GetUpnpSettingsResponse is NULL!");
        }
        else {
          mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
          iVar3 = mxmlNewElement(iVar2,"GetUpnpSettingsResult");
          if (iVar3 == 0) {
            printf("GetUpnpSettingsResult is NULL!");
          }
          else {
            mxmlNewText(iVar3,0,"O");
            iVar3 = mxmlNewElement(iVar2,"Enable");
            if (iVar3 == 0) {
              printf("Enable is NULL!");
            }
            else {
              if (local_26c == 1) {
                mxmlNewText(iVar3,0,"true");
              }
              else {
                mxmlNewText(iVar3,0,"false");
              }
              iVar2 = mxmlNewElement(iVar2,"UpnpClientInfoLists");
              if (iVar2 == 0) {
                printf("UpnpClientInfoLists is NULL!");
              }
              else {
                iVar3 = stat("/tmp/upnp_info",&sStack_168);
                if (iVar3 < 0) {
                  printf("%s %d GetClientInfo error \n","GetUpnpSettings",0x73e);
                }
                else if (0 < sStack_168.st_blocks) {
                  __stream = fopen("/tmp/upnp_info","r");
                  if (__stream == 0) {
                    iVar1 = puts("the file not found!");
                    return iVar1;
                  }
                  memset(acStack_268,0,256);
                  while( true ) {
                    pcVar5 = fgets(acStack_268,256,__stream);
                    if (pcVar5 == 0) {
                      fclose(__stream);
                      pvVar6 = mxmlSaveAllocString(iVar1,0);
                      printf(" retstring =%s \n",pvVar6);
                      FUN_0041ed70("",200,pvVar6,"");
                      free(pvVar6);
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    memset(auStack_d0,0," ");
                    memset(auStack_b0,0," ");
                    memset(auStack_90,0," ");
                    memset(auStack_70,0," ");
                    memset(auStack_50,0," ");
                    sscanf(acStack_268,
                           "%[^\',\'],%[^\',\'],%[^\',\'],%[^\',\'],%[^\',\'],%[^\',\'],%[^\',\'],",
                           auStack_d0,auStack_b0,auStack_90,auStack_70,auStack_30,auStack_10,
                           auStack_50);
                    iVar3 = mxmlNewElement(iVar2,"ClientInfo");
                    if (iVar3 == 0) {
                      printf("ClientInfo is NULL!");
                      return 0;
                    }
                    iVar4 = mxmlNewElement(iVar3,"Protocol");
                    if (iVar4 == 0) {
                      printf("Protocol is NULL!");
                      return 0;
                    }
                    mxmlNewText(iVar4,0,auStack_70);
                    iVar4 = mxmlNewElement(iVar3,"ApplicationName");
                    if (iVar4 == 0) break;
                    mxmlNewText(iVar4,0,auStack_50);
                    iVar4 = mxmlNewElement(iVar3,"ClientIP");
                    if (iVar4 == 0) {
                      printf("ClientIP is NULL!");
                      return 0;
                    }
                    mxmlNewText(iVar4,0,auStack_d0);
                    iVar4 = mxmlNewElement(iVar3,"InternalPort");
                    if (iVar4 == 0) {
                      printf("InternalPort is NULL!");
                      return 0;
                    }
                    mxmlNewText(iVar4,0,auStack_90);
                    iVar3 = mxmlNewElement(iVar3,"ExternalPort");
                    if (iVar3 == 0) {
                      printf("ExternalPort is NULL!");
                      return 0;
                    }
                    mxmlNewText(iVar3,0,auStack_b0);
                    memset(acStack_268,0,256);
                  }
                  printf("ApplicationName is NULL!");
                  return 0;
                }
                mxmlNewElement(iVar2,"ClientInfo");
                pvVar6 = mxmlSaveAllocString(iVar1,0);
                printf(" retstring =%s \n",pvVar6);
                FUN_0041ed70("",200,pvVar6,"");
                free(pvVar6);
                mxmlDelete(iVar1);
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

