
int FUN_0044fdc0(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  short sVar5;
  char *pcVar3;
  size_t sVar4;
  undefined1 *puVar6;
  uint uVar7;
  uint local_150;
  undefined auStack_14c [16];
  undefined auStack_13c [4];
  undefined auStack_138 [4];
  undefined auStack_134 [4];
  char acStack_130 [36];
  undefined *local_10c;
  undefined4 local_108;
  undefined *local_104;
  undefined4 local_100;
  undefined *local_fc;
  undefined4 local_f8;
  undefined *local_f4;
  undefined4 local_f0;
  undefined4 local_ec;
  undefined auStack_e4 [49];
  char acStack_b3 [16];
  char acStack_a3 [35];
  char acStack_80 [15];
  undefined local_71;
  undefined auStack_70 [33];
  char acStack_4f [15];
  undefined local_40;
  char acStack_3f [39];
  
  local_10c = auStack_14c;
  local_104 = auStack_13c;
  local_fc = auStack_138;
  local_f4 = auStack_134;
  local_108 = 16;
  local_ec = 0;
  local_100 = 0;
  local_f8 = 0;
  local_f0 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    sVar5 = HttpDenyPage(param_1);
    goto LAB_00450378;
  }
  memset(acStack_80,0,"b");
  iVar2 = httpGetEnv(param_1,"Save");
  uVar7 = 0;
  if (iVar2 == 0) {
LAB_0045017c:
    memset(acStack_80,0,"b");
    swGetPasswordCfg(acStack_80);
    pageParaSet(&local_10c,acStack_4f,0);
    local_150 = uVar7;
    pageParaSet(&local_10c,&local_150,1);
    local_150 = 1;
    pageParaSet(&local_10c,&local_150,2);
    iVar2 = HttpIsAccessFromLAN(param_1);
    if ((iVar2 == 0) && (iVar2 = getForbiddenWanUpgrade(), iVar2 != 0)) {
      local_150 = 1;
    }
    else {
      local_150 = 0;
    }
    pageParaSet(&local_10c,&local_150,3);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "LoginPwdInf");
    pageDynParaPrintf(&local_10c,0,param_1);
    pageDynParaPrintf(&local_10c,1,param_1);
    pageDynParaPrintf(&local_10c,2,param_1);
    pageDynParaPrintf(&local_10c,3,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar2 = httpRpmFsA(param_1,"/userRpm/ChangeLoginPwdRpm.htm");
    if (iVar2 == 2) {
      return 2;
    }
    iVar2 = 10;
    puVar6 = 0;
  }
  else {
    swGetPasswordCfg(auStack_e4);
    pcVar3 = httpGetEnv(param_1,"oldname");
    if (pcVar3 != 0) {
      local_71 = 0;
      strncpy(acStack_80,pcVar3,15);
    }
    pcVar3 = httpGetEnv(param_1,"newname");
    if (pcVar3 != 0) {
      local_40 = 0;
      strncpy(acStack_4f,pcVar3,15);
    }
    pcVar3 = httpGetEnv(param_1,"oldpassword");
    if (pcVar3 != 0) {
      sVar4 = strlen(pcVar3);
      b64_decode(auStack_70," ",pcVar3,sVar4);
    }
    pcVar3 = httpGetEnv(param_1,"newpassword");
    if (pcVar3 != 0) {
      sVar4 = strlen(pcVar3);
      b64_decode(acStack_3f," ",pcVar3,sVar4);
    }
    sVar5 = swChkPasswordCfg(acStack_80);
    iVar2 = sVar5;
    if (iVar2 == 0) {
      iVar2 = strcmp(acStack_b3,acStack_4f);
      uVar7 = 1;
      if (iVar2 == 0) {
        iVar2 = strcmp(acStack_a3,acStack_3f);
        uVar7 = (uint)(iVar2 != 0);
      }
      iVar2 = isSupportAccountEnable();
      if (iVar2 == 0) {
LAB_0045011c:
        swSetPasswordCfg(acStack_80);
        httpAdminPwconf();
        bVar1 = false;
      }
      else {
        pcVar3 = getSupportLoginPwd();
        sVar4 = strlen(pcVar3);
        hex_md5_make_digest(acStack_130,pcVar3,sVar4);
        pcVar3 = getSupportLoginName();
        iVar2 = strcmp(acStack_4f,pcVar3);
        if (iVar2 != 0) goto LAB_0045011c;
        iVar2 = strcmp(acStack_3f,acStack_130);
        bVar1 = true;
        if (iVar2 != 0) goto LAB_0045011c;
      }
      if ((uVar7 != 0) && (!bVar1)) {
        httpSessionInit();
        sVar5 = GoIndex(param_1,1);
        goto LAB_00450378;
      }
      goto LAB_0045017c;
    }
    puVar6 = "";
  }
  sVar5 = HttpErrorPage(param_1,iVar2,puVar6,0);
LAB_00450378:
  return sVar5;
}

