
/* WARNING: Type propagation algorithm not settling */

int FUN_004434fc(undefined4 param_1)

{
  int iVar1;
  short sVar9;
  char *pcVar2;
  uint uVar3;
  uint32_t uVar4;
  uint32_t uVar5;
  uint32_t uVar6;
  uint32_t uVar7;
  int iVar8;
  undefined4 uVar10;
  char *pcVar11;
  uint uVar12;
  uint local_118 [5];
  in_addr_t local_104;
  char acStack_100 [20];
  char acStack_ec [20];
  undefined auStack_d8 [4];
  undefined auStack_d4 [4];
  undefined auStack_d0 [4];
  undefined auStack_cc [4];
  undefined auStack_c8 [4];
  undefined *local_c4;
  undefined4 local_c0;
  undefined *local_bc;
  undefined4 local_b8;
  undefined *local_b4;
  undefined4 local_b0;
  undefined4 local_ac;
  undefined auStack_a4 [18];
  undefined auStack_92 [18];
  undefined auStack_80 [4];
  undefined *local_7c;
  undefined4 local_78;
  undefined *local_74;
  undefined4 local_70;
  undefined *local_6c;
  undefined4 local_68;
  undefined *local_64;
  undefined4 local_60;
  undefined *local_5c;
  undefined4 local_58;
  uint *local_54;
  undefined4 local_50;
  undefined4 local_4c;
  int local_40;
  int local_3c;
  char *local_38;
  char *local_34;
  uint *local_30;
  
  local_118[1] = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar9 = HttpDenyPage(param_1);
LAB_00443ec4:
    iVar1 = sVar9;
  }
  else {
    iVar1 = httpGetEnv(param_1,"Add");
    if (iVar1 == 0) {
      iVar1 = httpGetEnv(param_1,"Modify");
      local_7c = auStack_d8;
      if (iVar1 == 0) {
        local_74 = auStack_d4;
        local_6c = auStack_d0;
        local_64 = auStack_cc;
        local_5c = auStack_c8;
        local_54 = local_118 + 1;
        local_c4 = auStack_a4;
        local_bc = auStack_92;
        local_b4 = auStack_80;
        local_c0 = 18;
        local_b8 = 16;
        local_4c = 0;
        local_78 = 0;
        local_70 = 0;
        local_68 = 0;
        local_60 = 0;
        local_58 = 0;
        local_50 = 0;
        local_ac = 0;
        local_b0 = 0;
        pcVar2 = httpGetEnv(param_1,"Page");
        uVar12 = 1;
        if (pcVar2 != 0) {
          uVar12 = atoi(pcVar2);
          uVar3 = getDhcpsStaticAddrSize();
          if ((uVar3 >> 3 < uVar12) || (uVar12 < 1)) {
            uVar12 = 1;
          }
        }
        local_118[2] = 0;
        local_118[3] = 0;
        local_118[4] = 0;
        local_104 = 0;
        pcVar2 = httpGetEnv(param_1,"Add");
        if (pcVar2 == 0) {
          pcVar2 = httpGetEnv(param_1,"doAll");
          if (pcVar2 != 0) {
            iVar1 = strcmp(pcVar2,"EnAll");
            uVar10 = 1;
            if (iVar1 != 0) {
              iVar1 = strcmp(pcVar2,"DisAll");
              uVar10 = 2;
              if (iVar1 != 0) {
                iVar1 = strcmp(pcVar2,"DelAll");
                uVar10 = 3;
                if (iVar1 != 0) goto LAB_00443a44;
              }
            }
            swSetDhcpsStaticAddrCfgAll(uVar10);
            goto LAB_00443a44;
          }
          pcVar2 = httpGetEnv(param_1,"Del");
          if (pcVar2 != 0) {
            iVar1 = atoi(pcVar2);
            swDelDhcpsStaticEntryCfg(iVar1);
            goto LAB_00443a44;
          }
          iVar1 = httpGetEnv(param_1,"Save");
          if (iVar1 == 0) goto LAB_00443a44;
          local_104 = 0;
          local_118[2] = 0;
          local_118[3] = 0;
          local_118[4] = 0;
          pcVar2 = httpGetEnv(param_1,"Mac");
          if (pcVar2 == 0) {
LAB_004438d4:
            pcVar2 = httpGetEnv(param_1,"Ip");
            if (pcVar2 != 0) {
              do {
                pcVar11 = pcVar2;
                pcVar2 = pcVar11 + 1;
              } while (*pcVar11 == ' ');
              if (pcVar11 != 0) {
                iVar1 = swChkDotIpAddr(pcVar11);
                if (iVar1 == 0) {
                  iVar8 = 0xbb9;
                  goto LAB_00443eb8;
                }
                local_104 = inet_addr(pcVar11);
              }
            }
            pcVar2 = httpGetEnv(param_1,"State");
            if (pcVar2 != 0) {
              iVar1 = atoi(pcVar2);
              local_118[2] = (uint)(iVar1 != 0);
            }
            pcVar2 = httpGetEnv(param_1,"Changed");
            iVar1 = strcmp(pcVar2,"1");
            if (iVar1 == 0) {
              pcVar2 = httpGetEnv(param_1,"SelIndex");
              iVar1 = 0;
              if (pcVar2 != 0) {
                iVar1 = atoi(pcVar2);
              }
              iVar8 = swSetDhcpsStaticEntryCfg(iVar1,local_118 + 2);
            }
            else {
              iVar8 = swAddDhcpsStaticEntryCfg(local_118 + 2);
            }
            if (iVar8 == 0) goto LAB_00443a44;
          }
          else {
            do {
              pcVar11 = pcVar2;
              pcVar2 = pcVar11 + 1;
            } while (*pcVar11 == ' ');
            if (pcVar11 == 0) goto LAB_004438d4;
            iVar1 = swChkStrMacAddr(pcVar11);
            iVar8 = 3000;
            if (iVar1 != 0) {
              swMacStr2Eth(pcVar11,local_118 + 3);
              goto LAB_004438d4;
            }
          }
        }
        else {
          iVar1 = strcmp("Add",pcVar2);
          if (iVar1 == 0) {
            local_118[2] = 1;
          }
LAB_00443a44:
          httpPrintf(param_1,
                     "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n"
                     ,"dhcpList");
          iVar1 = swGetDhcpsStaticAddrEntry((uVar12 - 1) * 8,local_118 + 2);
          if ((iVar1 != 0) || (local_40 = uVar12 - 2, uVar3 = uVar12 - 1, uVar12 == 1)) {
            local_40 = uVar12 - 1;
            uVar3 = uVar12;
          }
          local_40 = local_40 << 3;
          local_3c = uVar3 << 3;
          local_38 = acStack_100;
          local_34 = acStack_ec;
          local_30 = local_118;
          iVar1 = 0;
          uVar12 = 0;
          while (iVar8 = swGetDhcpsStaticAddrCfgSize(), iVar1 < iVar8) {
            local_118[2] = 0;
            local_118[3] = 0;
            local_118[4] = 0;
            local_104 = 0;
            iVar8 = swGetDhcpsStaticAddrEntry(iVar1,local_118 + 2);
            if (iVar8 == 0) break;
            iVar1 = iVar1 + 1;
            if (local_40 < iVar1) {
              if (local_3c < iVar1) break;
              sprintf(local_38,"%02X-%02X-%02X-%02X-%02X-%02X",local_118[3] >> 24,
                      local_118[3] >> 16 & 255,local_118[3] >> 8 & 255,local_118[3] & 255,
                      local_118[4] >> 24,local_118[4] >> 16 & 255);
              uVar12 = uVar12 + 1;
              pageParaSet(&local_c4,local_38,0);
              uVar4 = ntohl(local_104);
              uVar5 = ntohl(local_104);
              uVar6 = ntohl(local_104);
              uVar7 = ntohl(local_104);
              sprintf(local_34,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
                      (int)(uVar6 & -256) >> 8,uVar7 & 255);
              pageParaSet(&local_c4,local_34,1);
              local_118[0] = local_118[2];
              pageParaSet(&local_c4,local_30,2);
              pageDynParaListPrintf(&local_c4,param_1);
            }
          }
          httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
          local_118[0] = uVar3;
          pageParaSet(&local_7c,local_118,0);
          local_118[0] = (uint)(local_3c < iVar1);
          pageParaSet(&local_7c,local_118,1);
          local_118[0] = uVar12;
          pageParaSet(&local_7c,local_118,2);
          local_118[0] = 3;
          pageParaSet(&local_7c,local_118,3);
          local_118[0] = 8;
          pageParaSet(&local_7c,local_118,4);
          local_118[0] = swDhcpsCfgIsChanged();
          pageParaSet(&local_7c,local_118,5);
          httpPrintf(param_1,
                     "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n"
                     ,"DHCPStaticPara");
          pageDynParaPrintf(&local_7c,0,param_1);
          pageDynParaPrintf(&local_7c,1,param_1);
          pageDynParaPrintf(&local_7c,2,param_1);
          pageDynParaPrintf(&local_7c,3,param_1);
          pageDynParaPrintf(&local_7c,4,param_1);
          pageDynParaPrintf(&local_7c,5,param_1);
          httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
          HttpWebV4Head(param_1,0,1);
          iVar1 = httpRpmFsA(param_1,"/userRpm/FixMapCfgRpm.htm");
          if (iVar1 == 2) {
            return 2;
          }
          iVar8 = 10;
        }
LAB_00443eb8:
        sVar9 = HttpErrorPage(param_1,iVar8,0,0);
        goto LAB_00443ec4;
      }
    }
    iVar1 = FUN_00443f04(param_1);
  }
  return iVar1;
}

