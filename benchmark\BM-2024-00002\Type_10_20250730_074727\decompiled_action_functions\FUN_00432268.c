
undefined4 FUN_00432268(void)

{
  int iVar1;
  int iVar2;
  int iVar3;
  void *__ptr;
  undefined4 local_4c;
  undefined4 local_48;
  undefined4 local_44;
  undefined4 local_40;
  undefined4 local_3c;
  undefined4 local_38;
  undefined4 local_34;
  undefined4 local_30;
  undefined4 local_2c;
  undefined4 local_28;
  undefined4 local_24;
  undefined4 local_20;
  undefined4 local_1c;
  undefined4 local_18;
  undefined4 local_14;
  undefined4 local_10;
  
  local_4c = 0;
  local_48 = 0;
  local_44 = 0;
  local_40 = 0;
  local_3c = 0;
  local_38 = 0;
  local_34 = 0;
  local_30 = 0;
  local_2c = 0;
  local_28 = 0;
  local_24 = 0;
  local_20 = 0;
  local_1c = 0;
  local_18 = 0;
  local_14 = 0;
  local_10 = 0;
  iVar1 = mxmlNewXML("1.0");
  if (iVar1 == 0) {
    puts("Create new xml error!!!");
  }
  else {
    iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
    if (iVar2 == 0) {
      puts("Create new element error!!!");
      mxmlDelete(iVar1);
    }
    else {
      mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
      mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
      mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
      iVar2 = mxmlNewElement(iVar2,"soap:Body");
      if (iVar2 == 0) {
        puts("Create new element error!!!");
        mxmlDelete(iVar1);
      }
      else {
        iVar2 = mxmlNewElement(iVar2,"GetWanSpeedTestResponse");
        if (iVar2 == 0) {
          puts("Create new element error!!!");
          mxmlDelete(iVar1);
        }
        else {
          mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
          iVar3 = mxmlNewElement(iVar2,"GetWanSpeedTestResult");
          if (iVar3 == 0) {
            puts("Create new element error!!!");
            mxmlDelete(iVar1);
          }
          else {
            iVar3 = mxmlNewText(iVar3,0,"O");
            if (iVar3 == 0) {
              puts("Create new text error!!!");
              mxmlDelete(iVar1);
            }
            else {
              iVar3 = FUN_00431fc0(&local_4c,31,&local_2c,31);
              if (iVar3 == -1) {
                puts("please wait speed check complete...");
                mxmlDelete(iVar1);
              }
              else {
                iVar3 = mxmlNewElement(iVar2,"UploadBandwidth");
                if (iVar3 == 0) {
                  puts("Create new element error!!!");
                  mxmlDelete(iVar1);
                }
                else {
                  iVar3 = mxmlNewText(iVar3,0,&local_4c);
                  if (iVar3 == 0) {
                    printf("Create new text error!!!");
                    mxmlDelete(iVar1);
                  }
                  else {
                    iVar2 = mxmlNewElement(iVar2,"DownloadBandwidth");
                    if (iVar2 == 0) {
                      puts("Create new element error!!!");
                      mxmlDelete(iVar1);
                    }
                    else {
                      iVar2 = mxmlNewText(iVar2,0,&local_2c);
                      if (iVar2 == 0) {
                        puts("Create new text error!!!");
                        mxmlDelete(iVar1);
                      }
                      else {
                        __ptr = mxmlSaveAllocString(iVar1,0);
                        if (__ptr != 0) {
                          FUN_0041ed70("",200,__ptr,"");
                          free(__ptr);
                        }
                        mxmlDelete(iVar1);
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

