
int FUN_004568fc(undefined4 param_1)

{
  int iVar1;
  short sVar4;
  char *pcVar2;
  int iVar3;
  undefined4 uVar5;
  int iVar6;
  uint uVar7;
  uint uVar8;
  uint local_140;
  undefined auStack_13c [4];
  undefined auStack_138 [4];
  undefined auStack_134 [4];
  undefined auStack_130 [4];
  undefined auStack_12c [4];
  undefined *local_128;
  undefined4 local_124;
  undefined *local_120;
  undefined4 local_11c;
  undefined *local_118;
  undefined4 local_114;
  undefined *local_110;
  undefined4 local_10c;
  undefined *local_108;
  undefined4 local_104;
  undefined4 local_100;
  undefined *local_f8;
  undefined4 local_f4;
  undefined *local_f0;
  undefined4 local_ec;
  undefined *local_e8;
  undefined4 local_e4;
  undefined *local_e0;
  undefined4 local_dc;
  undefined *local_d8;
  undefined4 local_d4;
  undefined4 local_d0;
  ushort local_c8;
  byte local_c6;
  char acStack_c5 [63];
  undefined local_86;
  byte local_85;
  uint local_84;
  undefined auStack_80 [4];
  undefined auStack_7c [4];
  undefined auStack_78 [64];
  undefined auStack_38 [4];
  undefined auStack_34 [4];
  char *local_30;
  
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar4 = HttpDenyPage(param_1);
LAB_004571fc:
    iVar1 = sVar4;
  }
  else {
    iVar1 = httpGetEnv(param_1,"Add");
    if (iVar1 == 0) {
      iVar1 = httpGetEnv(param_1,"Modify");
      local_128 = auStack_13c;
      if (iVar1 == 0) {
        local_120 = auStack_138;
        local_118 = auStack_134;
        local_110 = auStack_130;
        local_108 = auStack_12c;
        local_f8 = auStack_80;
        local_f0 = auStack_7c;
        local_e8 = auStack_78;
        local_e0 = auStack_38;
        local_d8 = auStack_34;
        local_e4 = "@";
        local_100 = 0;
        local_124 = 0;
        local_11c = 0;
        local_114 = 0;
        local_10c = 0;
        local_104 = 0;
        local_d0 = 0;
        local_f4 = 0;
        local_ec = 0;
        local_dc = 0;
        local_d4 = 0;
        pcVar2 = httpGetEnv(param_1,"Page");
        uVar8 = 1;
        if (pcVar2 != 0) {
          uVar8 = atoi(pcVar2);
          iVar1 = getDefaultPortTriggerTblSize();
          if (((int)(((uint)(iVar1 >> 31) >> 29) + iVar1) >> 3 < uVar8) || (uVar8 < 1)
             ) {
            uVar8 = 1;
          }
        }
        memset(&local_c8,0,"H");
        pcVar2 = httpGetEnv(param_1,"Add");
        if (pcVar2 == 0) {
          pcVar2 = httpGetEnv(param_1,"doAll");
          if (pcVar2 != 0) {
            iVar1 = strcmp(pcVar2,"EnAll");
            uVar5 = 1;
            if (iVar1 != 0) {
              iVar1 = strcmp(pcVar2,"DisAll");
              uVar5 = 2;
              if (iVar1 != 0) {
                iVar1 = strcmp(pcVar2,"DelAll");
                uVar5 = 3;
                if (iVar1 != 0) goto LAB_00456e6c;
              }
            }
            swSetPtTableAll(uVar5);
            goto LAB_00456e6c;
          }
          pcVar2 = httpGetEnv(param_1,"Del");
          if (pcVar2 != 0) {
            iVar1 = atoi(pcVar2);
            swDelPtEntry(iVar1);
            goto LAB_00456e6c;
          }
          iVar1 = httpGetEnv(param_1,"Save");
          if (iVar1 == 0) goto LAB_00456e6c;
          memset(&local_c8,0,"H");
          pcVar2 = httpGetEnv(param_1,"trPort");
          if (pcVar2 != 0) {
            iVar1 = atoi(pcVar2);
            local_c8 = iVar1;
          }
          pcVar2 = httpGetEnv(param_1,"trProtocol");
          if (pcVar2 != 0) {
            iVar1 = atoi(pcVar2);
            local_c6 = iVar1;
          }
          pcVar2 = httpGetEnv(param_1,"inPort");
          if (pcVar2 != 0) {
            local_86 = 0;
            strncpy(acStack_c5,pcVar2,"?");
          }
          pcVar2 = httpGetEnv(param_1,"inProtocol");
          if (pcVar2 != 0) {
            iVar1 = atoi(pcVar2);
            local_85 = iVar1;
          }
          pcVar2 = httpGetEnv(param_1,"State");
          if (pcVar2 != 0) {
            iVar1 = atoi(pcVar2);
            local_84 = (uint)(iVar1 != 0);
          }
          iVar1 = swChkFowardTrigger(&local_c8);
          if (iVar1 == 0) {
            pcVar2 = httpGetEnv(param_1,"Changed");
            iVar1 = strcmp(pcVar2,"1");
            if (iVar1 == 0) {
              pcVar2 = httpGetEnv(param_1,"SelIndex");
              iVar1 = 0;
              if (pcVar2 != 0) {
                iVar1 = atoi(pcVar2);
              }
              iVar1 = swModifyPtEntry(iVar1,&local_c8);
            }
            else {
              iVar1 = swAppendPtEntry(&local_c8);
            }
            if (iVar1 == 0) goto LAB_00456e6c;
          }
        }
        else {
          iVar1 = strcmp("Add",pcVar2);
          if (iVar1 == 0) {
            local_84 = 1;
          }
LAB_00456e6c:
          httpPrintf(param_1,
                     "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n"
                     ,"specAppList");
          iVar1 = swGetPtEntry((uVar8 - 1) * 8,&local_c8);
          if ((iVar1 == 0) || (iVar1 = uVar8 - 2, uVar7 = uVar8 - 1, uVar8 == 1)) {
            iVar1 = uVar8 - 1;
            uVar7 = uVar8;
          }
          local_30 = acStack_c5;
          iVar6 = 0;
          uVar8 = 0;
          while (iVar3 = getDefaultPortTriggerTblSize(), iVar6 < iVar3) {
            memset(&local_c8,0,"H");
            iVar3 = swGetPtEntry(iVar6,&local_c8);
            if (iVar3 != 0) break;
            iVar6 = iVar6 + 1;
            if (iVar1 << 3 < iVar6) {
              if ((int)(uVar7 << 3) < iVar6) break;
              local_140 = local_c8;
              uVar8 = uVar8 + 1;
              pageParaSet(&local_f8,&local_140,0);
              local_140 = local_c6;
              pageParaSet(&local_f8,&local_140,1);
              pageParaSet(&local_f8,local_30,2);
              local_140 = local_85;
              pageParaSet(&local_f8,&local_140,3);
              local_140 = local_84;
              pageParaSet(&local_f8,&local_140,4);
              pageDynParaListPrintf(&local_f8,param_1);
            }
          }
          httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
          local_140 = uVar7;
          pageParaSet(&local_128,&local_140,0);
          local_140 = (uint)((int)(uVar7 << 3) < iVar6);
          pageParaSet(&local_128,&local_140,1);
          local_140 = uVar8;
          pageParaSet(&local_128,&local_140,2);
          local_140 = 5;
          pageParaSet(&local_128,&local_140,3);
          local_140 = 8;
          pageParaSet(&local_128,&local_140,4);
          httpPrintf(param_1,
                     "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n"
                     ,"specAppPara");
          pageDynParaPrintf(&local_128,0,param_1);
          pageDynParaPrintf(&local_128,1,param_1);
          pageDynParaPrintf(&local_128,2,param_1);
          pageDynParaPrintf(&local_128,3,param_1);
          pageDynParaPrintf(&local_128,4,param_1);
          httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
          HttpWebV4Head(param_1,0,1);
          iVar1 = httpRpmFsA(param_1,"/userRpm/SpecialAppRpm.htm");
          if (iVar1 == 2) {
            return 2;
          }
          iVar1 = 10;
        }
        sVar4 = HttpErrorPage(param_1,iVar1,0,0);
        goto LAB_004571fc;
      }
    }
    iVar1 = FUN_0045723c(param_1);
  }
  return iVar1;
}

