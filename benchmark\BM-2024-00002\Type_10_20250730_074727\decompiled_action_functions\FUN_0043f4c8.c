
undefined4 FUN_0043f4c8(int param_1)

{
  int iVar1;
  __pid_t _Var2;
  int iVar3;
  int iVar4;
  void *__ptr;
  char *local_54;
  char acStack_48 [64];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetFirmwareValidation");
  }
  else {
    iVar1 = FUN_00430448();
    if (iVar1 == 0) {
      local_54 = "false";
    }
    else {
      local_54 = "true";
      _Var2 = fork();
      if (_Var2 < 0) {
        puts("ERROR! StartFirmwareDownload fork");
      }
      else if (0 < _Var2) {
        sleep(3);
        memset(acStack_48,0,"@");
        FUN_0042c320(1);
        FUN_0042c320(4);
        snprintf(acStack_48,"@","/bin/mtn_upgrade %s 0","/tmp/fw.bin");
        system(acStack_48);
                    /* WARNING: Subroutine does not return */
        exit(0);
      }
    }
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar3 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar3 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar3 = mxmlNewElement(iVar3,"soap:Body");
        if (iVar3 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar3 = mxmlNewElement(iVar3,"GetFirmwareValidationResponse");
          if (iVar3 == 0) {
            mxmlDelete(iVar1);
            puts("GetFirmwareValidationResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
            iVar4 = mxmlNewElement(iVar3,"GetFirmwareValidationResult");
            if (iVar4 == 0) {
              mxmlDelete(iVar1);
              puts("GetFirmwareValidationResult=NULL");
            }
            else {
              mxmlNewText(iVar4,0,"O");
              iVar4 = mxmlNewElement(iVar3,"IsValid");
              if (iVar4 == 0) {
                mxmlDelete(iVar1);
                puts("IsValid=NULL");
              }
              else {
                mxmlNewText(iVar4,0,local_54);
                iVar3 = mxmlNewElement(iVar3,"CountDown");
                if (iVar3 == 0) {
                  mxmlDelete(iVar1);
                  puts("CountDown=NULL");
                }
                else {
                  mxmlNewText(iVar3,0,"120");
                  if ("" == 0) {
                    __ptr = mxmlSaveAllocString(iVar1,0);
                    if (__ptr != 0) {
                      FUN_0041ed70("",200,__ptr,"");
                      free(__ptr);
                    }
                  }
                  mxmlDelete(iVar1);
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

