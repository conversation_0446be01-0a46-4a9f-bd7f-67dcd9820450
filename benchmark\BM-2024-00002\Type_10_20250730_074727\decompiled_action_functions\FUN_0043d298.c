
undefined4 FUN_0043d298(int param_1)

{
  int iVar1;
  int iVar2;
  size_t sVar3;
  int iVar4;
  void *__ptr;
  char *local_124;
  char acStack_10c [260];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetPasswdSettings");
  }
  else {
    memset(acStack_10c,0,256);
    iVar1 = mxmlLoadString(0,param_1,0);
    if (iVar1 == 0) {
      puts("ERROR!  tree is NULL");
    }
    else {
      iVar2 = mxmlFindElement(iVar1,iVar1,"NewPassword",0,0,1);
      if (iVar2 == 0) {
        puts("apmib_set MIB_USER_PASSWORD  state is NULL");
        local_124 = "ERROR";
      }
      else {
        iVar2 = mxmlGetText(iVar2,0);
        if (iVar2 == 0) {
          puts("apmib_set MIB_USER_PASSWORD  strChangePassword is NULL");
          local_124 = "ERROR";
        }
        else {
          iVar2 = FUN_0045ca40(iVar2);
          if (iVar2 == 0) {
            iVar2 = apmib_set(183,"");
            if (iVar2 == 0) {
              puts("apmib_set MIB_USER_PASSWORD is error");
              local_124 = "ERROR";
            }
            else {
              local_124 = "OK";
            }
          }
          else {
            FUN_00426654(iVar2,acStack_10c);
            sVar3 = strlen(acStack_10c);
            if (sVar3 < "@") {
              iVar2 = apmib_set(183,acStack_10c);
              if (iVar2 == 0) {
                puts("apmib_set MIB_USER_PASSWORD is error");
                local_124 = "ERROR";
              }
              else {
                local_124 = "OK";
              }
            }
            else {
              puts("user password length is error!");
              local_124 = "ERROR";
            }
          }
        }
      }
      iVar2 = mxmlNewXML("1.0");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        printf("Create new xml erro!!!");
      }
      else {
        iVar4 = mxmlNewElement(iVar2,"soap:Envelope");
        if (iVar4 == 0) {
          mxmlDelete(iVar2);
          puts("soap_env=NULL");
        }
        else {
          mxmlElementSetAttr(iVar4,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
          mxmlElementSetAttr(iVar4,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
          mxmlElementSetAttr(iVar4,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
          iVar4 = mxmlNewElement(iVar4,"soap:Body");
          if (iVar4 == 0) {
            mxmlDelete(iVar1);
            mxmlDelete(iVar2);
            puts("body=NULL");
          }
          else {
            iVar4 = mxmlNewElement(iVar4,"SetPasswdSettingsResponse");
            if (iVar4 == 0) {
              mxmlDelete(iVar1);
              mxmlDelete(iVar2);
              puts("SetPasswdSettingsResponse=NULL");
            }
            else {
              mxmlElementSetAttr(iVar4,"xmlns","http://purenetworks.com/HNAP1/");
              iVar4 = mxmlNewElement(iVar4,"SetPasswdSettingsResult");
              if (iVar4 == 0) {
                mxmlDelete(iVar1);
                mxmlDelete(iVar2);
                puts("SetPasswdSettingsResult=NULL");
              }
              else {
                mxmlNewText(iVar4,0,local_124);
                if ("" == 0) {
                  __ptr = mxmlSaveAllocString(iVar2,0);
                  if (__ptr != 0) {
                    apmib_update(4);
                    FUN_0041ed70("",200,__ptr,"");
                    free(__ptr);
                  }
                }
                mxmlDelete(iVar1);
                mxmlDelete(iVar2);
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

