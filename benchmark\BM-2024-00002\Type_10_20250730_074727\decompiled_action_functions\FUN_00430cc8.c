
/* WARNING: Type propagation algorithm not settling */

undefined4 FUN_00430cc8(undefined4 param_1)

{
  int iVar1;
  int iVar2;
  undefined4 uVar3;
  char *__s1;
  int local_20 [6];
  
  local_20[0] = -1;
  local_20[1] = 0;
  local_20[2] = 0;
  local_20[3] = 0;
  local_20[4] = 0;
  iVar1 = mxmlLoadString(0,param_1,0);
  if (iVar1 != 0) {
    iVar2 = mxmlFindElement(iVar1,iVar1,"soap:Envelope",0,0,1);
    if ((iVar2 != 0) &&
       (iVar2 = mxmlFindElement(iVar2,iVar1,"SetQoSManagementType",0,0,1), iVar2 != 0)) {
      uVar3 = mxmlFindElement(iVar2,iVar1,"QoSManagementType",0,0,1);
      __s1 = mxmlGetText(uVar3,0);
      if (__s1 != 0) {
        iVar2 = strcmp(__s1,"ByDevice");
        if (iVar2 == 0) {
          local_20[0] = 1;
        }
        else {
          iVar2 = strcmp(__s1,"ByApplication");
          if (iVar2 == 0) {
            local_20[0] = 2;
          }
          else {
            iVar2 = strcmp(__s1,"Off");
            if (iVar2 == 0) {
              local_20[0] = 0;
            }
          }
        }
      }
    }
    mxmlDelete(iVar1);
    if (("" == 0) || (local_20[0] == -1)) {
      if (local_20[0] == -1) {
        memcpy(local_20 + 1,"ERROR",6);
      }
      else {
        memcpy(local_20 + 1,"O",3);
        apmib_set(0x1b5b,local_20);
        apmib_update(4);
        FUN_00421468("killall -9 loop");
        FUN_00421468("loop&");
      }
      FUN_004260e0("SetQoSManagementType",local_20 + 1);
    }
  }
  return 0;
}

