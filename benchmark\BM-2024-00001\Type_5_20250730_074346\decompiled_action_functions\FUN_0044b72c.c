
int FUN_0044b72c(undefined4 param_1)

{
  int iVar1;
  short sVar2;
  int iVar3;
  undefined4 local_250;
  undefined4 local_24c;
  undefined4 local_248;
  undefined auStack_244 [32];
  char local_224 [44];
  char local_1f8 [44];
  undefined auStack_1cc [44];
  undefined4 *local_1a0;
  undefined4 local_19c;
  undefined4 *local_198;
  undefined4 local_194;
  char *local_190;
  undefined4 local_18c;
  undefined *local_188;
  undefined4 local_184;
  char *local_180;
  undefined4 local_17c;
  undefined *local_178;
  undefined4 local_174;
  undefined *local_170;
  undefined4 local_16c;
  undefined4 *local_168;
  undefined4 local_164;
  char *local_160;
  undefined4 local_15c;
  undefined4 local_150;
  undefined4 local_14c;
  char acStack_148 [45];
  undefined auStack_11b [45];
  char acStack_ee [45];
  undefined auStack_c1 [45];
  undefined auStack_94 [48];
  undefined4 local_64;
  char acStack_60 [48];
  undefined *local_30;
  
  local_30 = auStack_244;
  memset(local_30,0," ");
  memset(local_224,0,",");
  memset(local_1f8,0,",");
  memset(auStack_1cc,0,",");
  local_250 = 0;
  local_24c = 0;
  local_248 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  local_1a0 = &local_150;
  if (iVar1 == 0) {
    sVar2 = HttpDenyPage(param_1);
  }
  else {
    memset(local_1a0,0,288);
    local_198 = &local_14c;
    local_180 = acStack_ee;
    local_168 = &local_64;
    local_160 = acStack_60;
    local_15c = ",";
    local_19c = 0;
    local_194 = 0;
    local_18c = ",";
    local_184 = ",";
    local_17c = ",";
    local_174 = ",";
    local_16c = ",";
    local_164 = 0;
    local_190 = acStack_148;
    local_188 = auStack_11b;
    local_178 = auStack_c1;
    local_170 = auStack_94;
    local_150 = swGetIPv6Enable();
    local_14c = swGetWanIpv6Type();
    swGetIpv6NetworkInfo
              (local_1f8,&local_250,local_30,auStack_11b,local_224,&local_24c,auStack_c1,auStack_94)
    ;
    if (local_1f8[0] == '\0') {
      strcpy(acStack_148,local_1f8);
    }
    else {
      sprintf(acStack_148,"%s/%d",local_1f8,local_250);
    }
    if ((local_224[0] == '\0') || (local_224[0] == '\0')) {
      strcpy(acStack_ee,"");
    }
    else {
      sprintf(acStack_ee,"%s/%d",local_224,local_24c);
    }
    local_64 = swGetLanIpv6ConfigType();
    swGetLanLinklocalIpv6Address(auStack_1cc,&local_248);
    sprintf(acStack_60,"%s/%d",auStack_1cc,local_248);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "IPv6Status");
    iVar1 = 0;
    do {
      iVar3 = iVar1 + 1;
      pageDynParaPrintf(&local_1a0,iVar1,param_1);
      iVar1 = iVar3;
    } while (iVar3 != 9);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/IPv6StatusRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar2 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar2;
}

