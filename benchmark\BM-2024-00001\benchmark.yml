version: '1'
info:
  serial: BM-2024-00001
  build_date: 2024-07-10
  firmware:
    architecture: MIPSEB
    sha256: c321933e4e5970ba7299fe21778dab9398994c22ca0ba0422c6cbc3fbb95ea26
    vendor: TP-Link
    name: TL-WR940N
    file_name: wr940nv4_us_3_16_9_up_boot_160617.bin
    version: V4
    release_date: 2012
    description: TL-WR940N V4.0 - 450Mbps Wireless N Router, 4x 10/100Mbps LAN, 1x
      10/100Mbps WAN, IEEE 802.11n, IEEE 802.11g, IEEE 802.11b, 3x 5dBi
emulation:
  context: ./emulation
  ip: ***********
  port: 80
  interface: tap
seed:
  directory: ./seed
  list:
  - userRpm_AccessCtrlAccessRulesRpm.htm
  - userRpm_AccessCtrlAccessTargetsRpm.htm
  - userRpm_AccessCtrlHostsListsRpm.htm
  - userRpm_AccessCtrlTimeSchedRpm.htm
  - userRpm_AdvScrRpm.htm
  - userRpm_AutoEmailRpm.htm
  - userRpm_BPACfgRpm.htm
  - userRpm_BasicSecurityRpm.htm
  - userRpm_ChangeLoginPwdRpm.htm
  - userRpm_CmxDdnsRpm.htm
  - userRpm_DMZRpm.htm
  - userRpm_DateTimeCfgRpm.htm
  - userRpm_DdnsAddRpm.htm
  - userRpm_DynDdnsRpm.htm
  - userRpm_FixMapCfgRpm.htm
  - userRpm_GetGMTRpm.htm
  - userRpm_Index.htm
  - userRpm_L2TPCfgRpm.htm
  - userRpm_LanArpBindingListRpm.htm
  - userRpm_LanArpBindingRpm.htm
  - userRpm_LanDhcpServerRpm.htm
  - userRpm_LocalManageControlRpm.htm
  - userRpm_LoginRpm.htm
  - userRpm_MacCloneCfgRpm.htm
  - userRpm_MailResultRpm.htm
  - userRpm_ManageControlRpm.htm
  - userRpm_MenuRpm.htm
  - userRpm_MiscShowRpm.htm
  - userRpm_NetworkCfgRpm.htm
  - userRpm_NoipDdnsRpm.htm
  - userRpm_OperationModeRpm.htm
  - userRpm_PPPoECfgAdvRpm.htm
  - userRpm_PPPoECfgRpm.htm
  - userRpm_PPPoEv6CfgRpm.htm
  - userRpm_PPTPCfgRpm.htm
  - userRpm_ParentCtrlRpm.htm
  - userRpm_PeanutHullDdnsRpm.htm
  - userRpm_PingIframeRpm.htm
  - userRpm_PingWatchDogRpm.htm
  - userRpm_QoSCfgRpm.htm
  - userRpm_QoSRuleListRpm.htm
  - userRpm_RestoreDefaultCfgRpm.htm
  - userRpm_SpecialAppAdvRpm.htm
  - userRpm_SpecialAppRpm.htm
  - userRpm_StaticRouteTableRpm.htm
  - userRpm_SysRebootRpm.htm
  - userRpm_SystemLogRpm.htm
  - userRpm_SystemStatisticRpm.htm
  - userRpm_UpnpCfgRpm.htm
  - userRpm_VirtualServerAdvRpm.htm
  - userRpm_VirtualServerRpm.htm
  - userRpm_Wan6to4TunnelCfgRpm.htm
  - userRpm_WanCfgRpm.htm
  - userRpm_WanDhcpPlusCfgRpm.htm
  - userRpm_WanDynamicIpCfgRpm.htm
  - userRpm_WanDynamicIpV6CfgRpm.htm
  - userRpm_WanIpv6CfgRpm.htm
  - userRpm_WanIpv6EnableRpm.htm
  - userRpm_WanSlaacCfgRpm.htm
  - userRpm_WanStaticIpCfgRpm.htm
  - userRpm_WanStaticIpV6CfgRpm.htm
  - userRpm_WzdAccessCtrlHostAddRpm.htm
  - userRpm_WzdAccessCtrlRuleAddRpm.htm
  - userRpm_WzdAccessCtrlSchedAddRpm.htm
  - userRpm_WzdAccessCtrlTargetAddRpm.htm
  - userRpm_WzdChangeLoginPwdRpm.htm
  - userRpm_WzdL2TPRpm.htm
  - userRpm_WzdNetworkRpm.htm
  - userRpm_WzdPPPoERpm.htm
  - userRpm_WzdPPTPRpm.htm
  - userRpm_WzdStaticIpRpm.htm
  - userRpm_WzdWanAutoTypeRpm.htm
  - userRpm_WzdWanMacRpm.htm
  - userRpm_WzdWanTroubleShootingRpm.htm
  - userRpm_WzdWanTypeRpm.htm
  - userRpm_WzdWlanApRpm.htm
  - userRpm_WzdWlanSiteSurveyRpm_AP.htm
  - userRpm_activeSessionRpm.htm
  - userRpm_popupSiteSurveyRpm.htm
  - userRpm_popupSiteSurveyRpm_AP.htm
authkeeper:
  method: update
  script_path: ./auth/update.py
  test_seed: seed_0
