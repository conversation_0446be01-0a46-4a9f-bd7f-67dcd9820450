
/* WARNING: Type propagation algorithm not settling */

int FUN_00470cd0(undefined4 param_1)

{
  int iVar1;
  short sVar3;
  int iVar2;
  char *pcVar4;
  char *pcVar5;
  undefined4 uVar6;
  undefined1 *puVar7;
  uint local_4a0;
  uint local_49c;
  uint local_498;
  uint local_494;
  uint local_490 [7];
  uint *local_474;
  undefined4 local_470;
  uint *local_46c;
  undefined4 local_468;
  uint *local_464;
  undefined4 local_460;
  uint *local_45c;
  undefined4 local_458;
  uint *local_454;
  undefined4 local_450;
  uint *local_44c;
  undefined4 local_448;
  uint *local_444;
  undefined4 local_440;
  uint *local_43c;
  undefined4 local_438;
  undefined4 local_434;
  undefined auStack_42c [296];
  undefined auStack_304 [4];
  uint local_300;
  undefined auStack_18c [4];
  int local_188;
  undefined4 local_180;
  
  local_4a0 = 0;
  local_49c = 0;
  local_498 = 0;
  local_494 = 0;
  local_490[0] = 0;
  local_490[1] = 0;
  local_490[2] = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar3 = HttpDenyPage(param_1);
    goto LAB_00471124;
  }
  local_490[6] = 0;
  local_490[5] = 0;
  local_490[3] = 0;
  local_490[4] = 0;
  swGetSystemMode(local_490 + 5);
  memset(auStack_304,0,376);
  memset(auStack_18c,0,376);
  swWlanBasicCfgGet(0,auStack_304);
  memcpy(auStack_18c,auStack_304,376);
  if (local_490[5] == 8) {
    if (local_300 != 3) {
      if (3 < local_300) {
        if (5 < local_300) goto LAB_00470e58;
        iVar1 = 6;
        goto LAB_00470e70;
      }
      iVar1 = 7;
      if (local_300 == 0) goto LAB_00470e70;
LAB_00470e58:
      pcVar4 = "workingMode/httpWorkingMode.c:452";
      pcVar5 = "ERROR: Should not be here: wlanBasicCfg.uApWlanOperMode=%d ";
LAB_00470fa8:
      HTTP_DEBUG_PRINT(pcVar4,pcVar5);
      goto LAB_00470fb8;
    }
    iVar1 = 3;
LAB_00470e70:
    local_474 = local_490 + 2;
    local_46c = &local_4a0;
    local_464 = &local_49c;
    local_45c = &local_498;
    local_454 = &local_494;
    local_44c = local_490;
    local_444 = local_490 + 1;
    local_43c = local_490 + 6;
    local_434 = 0;
    local_470 = 0;
    local_468 = 0;
    local_460 = 0;
    local_458 = 0;
    local_450 = 0;
    local_448 = 0;
    local_440 = 0;
    local_438 = 0;
    local_490[2] = iVar1;
    iVar2 = httpGetEnv(param_1,"Save");
    if (iVar2 != 0) {
      pcVar4 = httpGetEnv(param_1,"workMode");
      if (pcVar4 == 0) goto LAB_00470fb8;
      local_490[2] = atoi(pcVar4);
      msglogd(6,11,"pri_sysmode:%d, new_sysmode:%d\n",iVar1,local_490[2]);
      if (iVar1 != local_490[2]) {
        if (local_490[2] == 6) {
          local_188 = 5;
        }
        else if (local_490[2] == 7) {
          local_188 = 0;
        }
        else {
          if (local_490[2] != 3) {
            pcVar4 = "workingMode/httpWorkingMode.c:518";
            pcVar5 = "ERROR: Should not be here: newOperSystemMode=%d ";
            goto LAB_00470fa8;
          }
          local_188 = local_490[2];
        }
        swWlanModeConfigGet(0,auStack_42c);
        local_180 = 1;
        swWlanBasicCfgSet(0,auStack_18c);
        swWlanApBasicDynSet(0,auStack_304,auStack_18c,auStack_42c,auStack_42c);
      }
    }
    local_4a0 = getProductType();
    local_494 = local_4a0 >> 3 & 1;
    local_490[0] = local_4a0 >> 4 & 1;
    local_49c = local_4a0 >> 1 & 1;
    local_498 = local_4a0 >> 2 & 1;
    local_4a0 = local_4a0 & 1;
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "workModePara");
    pageDynParaListPrintf(&local_474,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/OperationModeRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    uVar6 = 10;
    puVar7 = 0;
  }
  else {
LAB_00470fb8:
    puVar7 = "";
    uVar6 = 0x7919;
  }
  sVar3 = HttpErrorPage(param_1,uVar6,puVar7,0);
LAB_00471124:
  return sVar3;
}

