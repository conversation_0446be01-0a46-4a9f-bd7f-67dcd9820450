
undefined4 FUN_00456f90(undefined4 param_1)

{
  int iVar1;
  undefined4 uVar2;
  char *__src;
  int iVar3;
  void *__ptr;
  undefined4 local_1c;
  char local_18 [16];
  
  local_18[0] = '\0';
  local_18[1] = '\0';
  local_18[2] = '\0';
  local_18[3] = '\0';
  local_18[4] = '\0';
  local_18[5] = '\0';
  local_18[6] = '\0';
  local_18[7] = '\0';
  local_18[8] = '\0';
  local_18[9] = '\0';
  local_18[10] = '\0';
  local_18[11] = '\0';
  local_18[12] = '\0';
  local_18[13] = '\0';
  local_18[14] = '\0';
  local_18[15] = '\0';
  iVar1 = mxmlLoadString(0,param_1,0);
  if (iVar1 != 0) {
    uVar2 = mxmlFindElement(iVar1,iVar1,"soap:Envelope",0,0,1);
    uVar2 = mxmlFindElement(uVar2,iVar1,"SetUpnpSettings",0,0,1);
    uVar2 = mxmlFindElement(uVar2,iVar1,"Enable",0,0,1);
    __src = mxmlGetText(uVar2,0);
    if (__src != 0) {
      strncpy(local_18,__src,16);
    }
    iVar1 = strncmp(local_18,"true",4);
    if (iVar1 == 0) {
      local_1c = 1;
      iVar1 = apmib_set(142,&local_1c);
      if (iVar1 == 0) {
        printf("Set MIB_UPNP_ENABLED error!");
      }
    }
    else {
      iVar1 = strncmp(local_18,"false",5);
      if (iVar1 == 0) {
        local_1c = 0;
        system("rm -rf /tmp/upnp_info");
        iVar1 = apmib_set(142,&local_1c);
        if (iVar1 == 0) {
          printf("Set MIB_UPNP_ENABLED error!");
        }
        system("iptables -t nat -F MINIUPNPD");
      }
      else {
        printf("Set SetUpnpSettings error!");
      }
    }
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("xml is NULL!");
      return 0;
    }
    iVar3 = mxmlNewElement(iVar1,"soap:Envelope");
    if (iVar3 == 0) {
      printf("soap_env is NULL!");
      return 0;
    }
    mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
    mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
    mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
    iVar3 = mxmlNewElement(iVar3,"soap:Body");
    if (iVar3 == 0) {
      printf("body is NULL!");
      return 0;
    }
    iVar3 = mxmlNewElement(iVar3,"SetUpnpSettingsResponse");
    if (iVar3 == 0) {
      printf("SetUpnpSettingsResponse is NULL!");
      return 0;
    }
    mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
    iVar3 = mxmlNewElement(iVar3,"SetUpnpSettingsResult");
    if (iVar3 == 0) {
      printf("SetUpnpSettingsResult is NULL!");
      return 0;
    }
    mxmlNewText(iVar3,0,"O");
    __ptr = mxmlSaveAllocString(iVar1,0);
    printf(" retstring =%s \n",__ptr);
    FUN_0041ed70("",200,__ptr,"");
    free(__ptr);
    mxmlDelete(iVar1);
  }
  system("firewall.sh");
  return 0;
}

