
/* WARNING: Removing unreachable block (ram,FUN_0044306c) */
/* WARNING: Removing unreachable block (ram,FUN_0044306c) */

int FUN_0044306c(undefined4 param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  short sVar4;
  int iVar5;
  undefined4 local_140;
  int local_13c;
  int local_138;
  undefined auStack_134 [8];
  undefined4 local_12c;
  undefined4 local_128;
  char acStack_124 [16];
  undefined auStack_114 [16];
  undefined auStack_104 [20];
  undefined4 *local_f0;
  undefined4 local_ec;
  undefined4 *local_e8;
  undefined4 local_e4;
  undefined4 local_e0;
  undefined auStack_d8 [32];
  undefined *local_b8;
  undefined4 local_b4;
  undefined *local_b0;
  undefined4 local_ac;
  undefined *local_a8;
  undefined4 local_a4;
  undefined *local_a0;
  undefined4 local_9c;
  undefined4 local_98;
  undefined auStack_90 [32];
  undefined auStack_70 [18];
  undefined auStack_5e [16];
  undefined auStack_4e [22];
  undefined *local_38;
  undefined *local_34;
  undefined4 *local_30;
  int *local_2c;
  
  memset(auStack_d8,0," ");
  local_128 = 0;
  local_12c = 0;
  memset(auStack_90,0,"R");
  local_e8 = &local_128;
  local_b0 = auStack_70;
  local_a8 = auStack_5e;
  local_a0 = auStack_4e;
  local_b4 = " ";
  local_9c = 16;
  local_a4 = 16;
  local_ac = 18;
  local_e0 = 0;
  local_ec = 0;
  local_e4 = 0;
  local_98 = 0;
  local_f0 = &local_12c;
  local_b8 = auStack_90;
  httpStatusSet(param_1,0);
  iVar5 = 0;
  local_38 = auStack_d8;
  httpHeaderGenerate(param_1);
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "DHCPDynList");
  local_138 = 4;
  pageParaSet(&local_f0,&local_138,1);
  local_34 = auStack_134;
  iVar1 = getDhcpsMaxEntrySize();
  local_30 = &local_140;
  iVar2 = getDhcpsStaticAddrSize();
  local_2c = &local_13c;
  while ((iVar5 < iVar2 + iVar1 &&
         (iVar3 = swGetDhcpsAssignedClientEntry(iVar5,local_38," ",local_34,local_30,local_2c),
         iVar3 == 1))) {
    pageParaSet(&local_b8,local_38,0);
    swMac2Str(local_34,auStack_104,0);
    pageParaSet(&local_b8,auStack_104,1);
    swIpAddr2Str(local_140,auStack_114);
    pageParaSet(&local_b8,auStack_114,2);
    if (local_13c == -1) {
      strcpy(acStack_124,"Permanent");
    }
    else {
      sprintf(acStack_124,"%02d:%02d:%02d",local_13c / 0xe10,(local_13c % 0xe10) / "<",
              (local_13c % 0xe10) % "<");
    }
    iVar5 = iVar5 + 1;
    pageParaSet(&local_b8,acStack_124,3);
    pageDynParaListPrintf(&local_b8,param_1);
  }
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "DHCPDynPara");
  local_138 = iVar5;
  pageParaSet(&local_f0,&local_138,0);
  pageDynParaPrintf(&local_f0,0,param_1);
  pageDynParaPrintf(&local_f0,1,param_1);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  HttpWebV4Head(param_1,0,1);
  iVar1 = httpRpmFsA(param_1,"/userRpm/AssignedIpAddrListRpm.htm");
  iVar2 = 2;
  if (iVar1 != 2) {
    sVar4 = HttpErrorPage(param_1,10,0,0);
    iVar2 = sVar4;
  }
  return iVar2;
}

