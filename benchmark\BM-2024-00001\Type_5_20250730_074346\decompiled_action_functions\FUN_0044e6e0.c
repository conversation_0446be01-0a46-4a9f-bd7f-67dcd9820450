
int FUN_0044e6e0(undefined4 param_1)

{
  int iVar1;
  short sVar4;
  char *pcVar2;
  undefined4 uVar3;
  int iVar5;
  int local_350;
  undefined4 local_34c;
  undefined4 local_348;
  char local_344;
  undefined auStack_343 [43];
  char local_318;
  undefined auStack_317 [43];
  undefined local_2ec;
  undefined auStack_2eb [43];
  undefined local_2c0;
  undefined auStack_2bf [43];
  undefined local_294;
  undefined auStack_293 [43];
  undefined auStack_268 [44];
  int local_23c;
  char acStack_238 [45];
  char acStack_20b [47];
  undefined *local_1dc;
  undefined4 local_1d8;
  undefined *local_1d4;
  undefined4 local_1d0;
  undefined *local_1cc;
  undefined4 local_1c8;
  undefined *local_1c4;
  undefined4 local_1c0;
  undefined *local_1bc;
  undefined4 local_1b8;
  undefined *local_1b4;
  undefined4 local_1b0;
  undefined *local_1ac;
  undefined4 local_1a8;
  undefined *local_1a4;
  undefined4 local_1a0;
  undefined *local_19c;
  undefined4 local_198;
  undefined *local_194;
  undefined4 local_190;
  undefined *local_18c;
  undefined4 local_188;
  undefined *local_184;
  undefined4 local_180;
  undefined4 local_17c;
  undefined4 local_178;
  undefined auStack_16c [4];
  undefined auStack_168 [4];
  undefined auStack_164 [45];
  undefined auStack_137 [45];
  undefined auStack_10a [46];
  undefined auStack_dc [4];
  undefined auStack_d8 [4];
  undefined auStack_d4 [45];
  undefined auStack_a7 [45];
  undefined auStack_7a [45];
  undefined auStack_4d [45];
  undefined auStack_20 [8];
  
  swGetWanIpv6Type();
  local_350 = 0;
  local_344 = '\0';
  memset(auStack_343,0,"+");
  local_318 = '\0';
  memset(auStack_317,0,"+");
  local_2ec = 0;
  memset(auStack_2eb,0,"+");
  local_2c0 = 0;
  memset(auStack_2bf,0,"+");
  local_294 = 0;
  memset(auStack_293,0,"+");
  local_34c = 0;
  local_348 = 0;
  memset(auStack_268,0,",");
  memset(auStack_16c,0,336);
  memset(&local_23c,0,"`");
  local_1d4 = auStack_168;
  local_1cc = auStack_164;
  local_1c4 = auStack_137;
  local_1bc = auStack_10a;
  local_1b4 = auStack_dc;
  local_1ac = auStack_d8;
  local_1a4 = auStack_d4;
  local_19c = auStack_a7;
  local_194 = auStack_7a;
  local_18c = auStack_4d;
  local_184 = auStack_20;
  local_188 = "-";
  local_1c8 = "-";
  local_1c0 = "-";
  local_1b8 = "-";
  local_1a0 = "-";
  local_198 = "-";
  local_190 = "-";
  local_17c = 0;
  local_1d8 = 0;
  local_1d0 = 0;
  local_1b0 = 0;
  local_1a8 = 0;
  local_180 = 0;
  local_178 = 0;
  local_1dc = auStack_16c;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar4 = HttpDenyPage(param_1);
  }
  else {
    swGetSlaacCfg(&local_23c);
    iVar1 = httpGetEnv(param_1,"Save");
    if ((iVar1 != 0) || (iVar1 = httpGetEnv(param_1,"Connect"), iVar1 != 0)) {
      iVar1 = httpGetEnv(param_1,"ipv6Enable");
      if (iVar1 == 0) {
        IPV6_ECHO("ucSetIPv6Enable(FALSE);");
        ucSetIPv6Enable(0);
        setSlaacIpv6AddressStatus(0);
      }
      else {
        IPV6_ECHO("ucSetIPv6Enable(TRUE);");
        ucSetIPv6Enable(1);
      }
      pcVar2 = httpGetEnv(param_1,"dnsType");
      if (pcVar2 != 0) {
        local_23c = atoi(pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"dnsserver1");
      if (pcVar2 == 0) {
        memset(acStack_238,0,"-");
      }
      else {
        strcpy(acStack_238,pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"dnsserver2");
      if (pcVar2 == 0) {
        memset(acStack_20b,0,"-");
      }
      else {
        strcpy(acStack_20b,pcVar2);
      }
      stopCurrentConnection(1);
      swSetSlaacCfg(&local_23c);
      uVar3 = getWanIpv6IfName();
      swSlaacStartConnect(uVar3);
      setUserStop(0);
    }
    iVar1 = httpGetEnv(param_1,"Disconnect");
    if (iVar1 != 0) {
      setUserStop(1);
      uVar3 = getWanIpv6IfName();
      swSlaacStopConnect(uVar3);
    }
    uVar3 = getWanIpv6IfName();
    LanIpv6RpmHtm(param_1,1);
    local_350 = 1;
    pageParaSet(&local_1dc,&local_350,0);
    local_350 = getSlaacIpv6AddressStatus();
    pageParaSet(&local_1dc,&local_350,1);
    iVar1 = swGetWanIpv6Type();
    if ((iVar1 == 1) && (local_350 == 1)) {
      getSlaacParameters(uVar3,&local_344,auStack_268,&local_34c);
      simplify_slaac_addr(&local_344,",");
    }
    pageParaSet(&local_1dc,&local_344,2);
    memset(auStack_268,0,",");
    if (((local_344 != '\0') && (local_344 != '\0')) &&
       (iVar1 = swGetDhcpv6Prefix(auStack_268,&local_34c,1), iVar1 == 0)) {
      sprintf(&local_318,"%s/%d",auStack_268,local_34c);
    }
    pageParaSet(&local_1dc,&local_318,3);
    swGetRunningGateway(&local_2ec,1);
    pageParaSet(&local_1dc,&local_2ec,4);
    pageParaSet(&local_1dc,&local_348,5);
    pageParaSet(&local_1dc,&local_23c,6);
    pageParaSet(&local_1dc,acStack_238,7);
    pageParaSet(&local_1dc,acStack_20b,8);
    swGetDhcpv6Dns(&local_2c0,&local_294,1);
    pageParaSet(&local_1dc,&local_2c0,9);
    pageParaSet(&local_1dc,&local_294,10);
    local_350 = swGetIPv6Enable();
    pageParaSet(&local_1dc,&local_350,11);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "slaacInfo");
    iVar1 = 0;
    do {
      iVar5 = iVar1 + 1;
      pageDynParaPrintf(&local_1dc,iVar1,param_1);
      iVar1 = iVar5;
    } while (iVar5 != 13);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintfWanIpv6TypeInfo(param_1);
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/WanSlaacCfgRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar4 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar4;
}

