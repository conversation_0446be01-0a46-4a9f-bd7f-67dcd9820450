
undefined4 FUN_0046423c(int param_1)

{
  undefined4 uVar1;
  undefined4 uVar2;
  int iVar3;
  int iVar4;
  undefined4 uVar5;
  char *pcVar6;
  int iVar7;
  int local_24;
  uint local_c;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetWLanRadioSettings");
  }
  else {
    local_c = 0;
    iVar3 = mxmlLoadString(0,param_1,0);
    iVar4 = mxmlFindElement(iVar3,iVar3,"soap:Envelope",0,0,1);
    uVar2 = wlan_idx;
    uVar1 = vwlan_idx;
    if ((iVar3 == 0) || (iVar4 == 0)) {
      fwrite("Build the tree faile\n",1,21,stderr);
    }
    else {
      if ("" == 1) {
        local_24 = mxmlFindElement(iVar4,iVar3,"SetMultipleActions",0,0,1);
        uVar2 = wlan_idx;
        uVar1 = vwlan_idx;
        while (local_24 != 0) {
          vwlan_idx = uVar1;
          wlan_idx = uVar2;
          local_24 = mxmlFindElement(local_24,iVar3,"SetWLanRadioSettings",0,0,1);
          if (local_24 != 0) {
            uVar5 = mxmlFindElement(local_24,iVar3,"RadioID",0,0,1);
            pcVar6 = mxmlGetText(uVar5,0);
            iVar4 = strncmp(pcVar6,"RADIO_2.4GHz",12);
            if (iVar4 != 0) {
              puts("+====================================5G wifi param set");
            }
            else {
              puts("+====================================2.4G wifi param set");
            }
            local_c = (uint)(iVar4 == 0);
            FUN_004633d4(local_24,iVar3,&local_c);
          }
        }
      }
      else {
        iVar4 = mxmlFindElement(iVar3,iVar3,"SetWLanRadioSettings",0,0,1);
        if (iVar4 != 0) {
          uVar5 = mxmlFindElement(iVar4,iVar3,"RadioID",0,0,1);
          pcVar6 = mxmlGetText(uVar5,0);
          iVar7 = strncmp(pcVar6,"RADIO_2.4GHz",12);
          if (iVar7 != 0) {
            puts("+====================================5G wifi param set");
          }
          else {
            puts("+====================================2.4G wifi param set");
          }
          local_c = (uint)(iVar7 == 0);
          FUN_004633d4(iVar4,iVar3,&local_c);
        }
      }
      wlan_idx = uVar2;
      vwlan_idx = uVar1;
      if ("" == 0) {
        FUN_0045d21c();
        apmib_update(4);
        system("sysconf init gw all");
      }
      mxmlDelete(iVar3);
    }
  }
  return 0;
}

