
int httpConfUploadTemp(undefined4 param_1)

{
  undefined4 uVar1;
  int iVar2;
  void *__ptr;
  short sVar3;
  int local_2d18;
  undefined auStack_2d14 [11524];
  
  local_2d18 = 10;
  uVar1 = uploadFileSizeGet();
  iVar2 = HttpIsAccessFromLAN(param_1);
  if ((iVar2 == 0) && (iVar2 = getForbiddenWanUpgrade(), iVar2 != 0)) {
    return 2;
  }
  __ptr = uploadBufGet(param_1,0,0);
  sVar3 = tddp_UploadFlashData(__ptr,uVar1,auStack_2d14,&local_2d18);
  if (sVar3 == 0) {
    if (local_2d18 == 0) {
      uVar1 = 3;
    }
    else {
      uVar1 = 14;
    }
    HttpRestartRpmHtm(param_1,uVar1);
    if (__ptr != 0) {
      free(__ptr);
    }
    swReboot(1);
    iVar2 = 2;
  }
  else {
    sVar3 = HttpErrorPage(param_1,sVar3,"../userRpm/BakNRestoreRpm.htm",0);
    iVar2 = sVar3;
  }
  return iVar2;
}

