
int httpFirmwareUploadRpmHtm(void *param_1)

{
  int iVar1;
  int iVar2;
  char *pcVar3;
  short sVar4;
  undefined4 uVar5;
  undefined auStack_a0 [4];
  undefined4 local_9c;
  undefined4 local_98;
  uint32_t local_94;
  undefined auStack_90 [4];
  undefined4 local_8c;
  pthread_t pStack_88;
  sched_param local_84;
  char acStack_80 [20];
  char acStack_6c [36];
  pthread_attr_t pStack_48;
  
  local_98 = 1;
  memcpy(acStack_6c,"../userRpm/FirmwareUpdateTemp.htm",""");
  iVar1 = swGetUpgradeMode();
  local_84.__sched_priority = sched_get_priority_max(2);
  pthread_attr_init(&pStack_48);
  pthread_attr_setschedpolicy(&pStack_48,2);
  pthread_attr_setschedparam(&pStack_48,&local_84);
  HTTP_DEBUG_PRINT("softwareUpgrade/httpSoftUpload.c:121","httpFirmwareUploadRpmHtm....");
  swGetLanIpMask(&local_94,auStack_90);
  local_94 = ntohl(local_94);
  sprintf(acStack_80,"%d.%d.%d.%d",local_94 >> 24,local_94 >> 16 & 255,(local_94 & -256) >> 8
          ,local_94 & 255);
  "" = &local_9c;
  "" = &local_98;
  "" = auStack_a0;
  "" = 0;
  "" = 0;
  "" = 0;
  "" = acStack_6c;
  "" = acStack_80;
  "" = strlen(acStack_6c);
  "" = strlen(acStack_80);
  "" = 0;
  iVar2 = httpRpmUpload(param_1,1);
  if (iVar2 == -1) {
    if (iVar1 != 1) {
      pcVar3 = "../userRpm/SoftwareUpgradeRpm.htm";
      iVar1 = 0x4651;
      goto LAB_004561c4;
    }
    uVar5 = 13;
LAB_00455d50:
    HttpRestartRpmHtm(param_1,uVar5);
  }
  else if (iVar2 == -2) {
    if (iVar1 != 1) {
      pcVar3 = "../userRpm/SoftwareUpgradeRpm.htm";
      iVar1 = 0x59da;
      goto LAB_004561c4;
    }
    uVar5 = 12;
    goto LAB_00455d50;
  }
  if (((iVar2 + 2U & -1) < 2) && (iVar1 == 1)) {
    "" = uploadBufGet(param_1,0,0);
    if ("" != 0) {
      free("");
      "" = 0;
    }
    swReboot(2);
    return 2;
  }
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  local_9c = sysGetUpgradeTime();
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "tempPageInf");
  pageParaSet("",&local_9c,0);
  local_98 = 1;
  pageParaSet("",&local_98,1);
  pageParaSet("",acStack_6c,2);
  pageParaSet("",acStack_80,3);
  local_8c = swGetFirewallHttpCtrlPort();
  pageParaSet("",&local_8c,4);
  pageDynParaPrintf("",0,param_1);
  pageDynParaPrintf("",1,param_1);
  pageDynParaPrintf("",2,param_1);
  pageDynParaPrintf("",3,param_1);
  pageDynParaPrintf("",4,param_1);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  HttpWebV4Head(param_1,0,1);
  copyFile("/web/userRpm/restart.htm","/tmp/userRpm/restart.htm");
  copyFile("/web/dynaform/css_main.css","/tmp/dynaform/css_main.css");
  copyFile("/web/dynaform/common.js","/tmp/dynaform/common.js");
  copyFile("/web/images/blue.jpg","/tmp/images/blue.jpg");
  iVar1 = getIsFirmwareBetaEdition();
  if (iVar1 == 1) {
    pcVar3 = getLanguage();
    iVar1 = strcmp(pcVar3,"CN");
    if (iVar1 != 0) {
      copyFile("/web/images/tp-beta-mark.png","/tmp/images/tp-beta-mark.png");
    }
  }
  "" = uploadFileSizeGet();
  "" = uploadBufGet(param_1,1,0);
  0xffffffff = swSoftCheck("","");
  if (0xffffffff == 0) {
    iVar1 = httpRpmFsA(param_1,"/userRpm/UpdateTemp.htm");
    if (iVar1 == 2) {
      iVar1 = pthread_create(&pStack_88,&pStack_48,FUN_00456208,param_1);
      if (iVar1 == 0) {
        return 2;
      }
      return -1;
    }
    pcVar3 = 0;
    iVar1 = 10;
  }
  else {
    if ("" != 0) {
      freeUploadBuf();
      "" = 0;
    }
    0x00000001 = 1;
    if (0xffffffff == 0) {
      0x00000001 = 1;
      return 2;
    }
    iVar1 = swGetUpgradeMode();
    if (iVar1 != 0) {
      return 2;
    }
    pcVar3 = "../userRpm/SoftwareUpgradeRpm.htm";
    iVar1 = 0xffffffff;
  }
LAB_004561c4:
  sVar4 = HttpErrorPage(param_1,iVar1,pcVar3,0);
  return sVar4;
}

