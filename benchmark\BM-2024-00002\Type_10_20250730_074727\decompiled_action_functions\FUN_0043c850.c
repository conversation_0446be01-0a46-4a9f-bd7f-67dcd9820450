
undefined4 FUN_0043c850(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  void *__ptr;
  int local_30;
  char *local_2c;
  char local_20 [8];
  undefined4 local_18;
  undefined4 local_14;
  int local_10;
  int local_c;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetAutoUpgradeFirmware");
  }
  else {
    local_20[0] = '\0';
    local_20[1] = '\0';
    local_20[2] = '\0';
    local_20[3] = '\0';
    local_20[4] = '\0';
    local_20[5] = '\0';
    local_20[6] = '\0';
    local_20[7] = '\0';
    local_18 = 0;
    local_14 = 0;
    local_10 = 0;
    local_c = 0;
    local_30 = 0;
    iVar1 = apmib_get(0x1b61,&local_10);
    if (iVar1 == 0) {
      puts("get auto upgrade error");
    }
    iVar1 = apmib_get(0x1b60,&local_c);
    if (iVar1 == 0) {
      puts("get auto upgrade  StartTime error");
    }
    else {
      local_30 = (local_c + 2) % 24;
    }
    if (local_10 == 0) {
      local_2c = "false";
    }
    else {
      local_2c = "true";
    }
    snprintf(local_20,3,"%d",local_c);
    snprintf(&local_18,3,"%d",local_30);
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar2 = mxmlNewElement(iVar2,"soap:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"GetAutoUpgradeFirmwareResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("GetAutoUpgradeFirmwareResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar3 = mxmlNewElement(iVar2,"GetAutoUpgradeFirmwareResult");
            if (iVar3 == 0) {
              mxmlDelete(iVar1);
              puts("GetAutoUpgradeFirmwareResult=NULL");
            }
            else {
              mxmlNewText(iVar3,0,"O");
              iVar3 = mxmlNewElement(iVar2,"AutoUpgrade");
              if (iVar3 == 0) {
                mxmlDelete(iVar1);
                puts("AutoUpgrade_xml=NULL");
              }
              else {
                mxmlNewText(iVar3,0,local_2c);
                iVar3 = mxmlNewElement(iVar2,"StartTime");
                if (iVar3 == 0) {
                  mxmlDelete(iVar1);
                  puts("StartTime_xml=NULL");
                }
                else {
                  mxmlNewText(iVar3,0,local_20);
                  iVar2 = mxmlNewElement(iVar2,"StopTime");
                  if (iVar2 == 0) {
                    mxmlDelete(iVar1);
                    puts("StopTime_xml=NULL");
                  }
                  else {
                    mxmlNewText(iVar2,0,&local_18);
                    if ("" == 0) {
                      __ptr = mxmlSaveAllocString(iVar1,0);
                      if (__ptr != 0) {
                        FUN_0041ed70("",200,__ptr,"");
                        free(__ptr);
                      }
                    }
                    mxmlDelete(iVar1);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

