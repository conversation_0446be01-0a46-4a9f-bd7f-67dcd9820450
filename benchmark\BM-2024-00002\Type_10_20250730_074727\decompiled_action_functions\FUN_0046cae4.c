
undefined4 FUN_0046cae4(undefined4 param_1)

{
  int iVar1;
  int iVar2;
  int local_14;
  int local_10 [2];
  
  local_14 = 0;
  local_10[0] = 0;
  iVar1 = mxmlLoadString(0,param_1,0);
  if (iVar1 == 0) {
    fwrite("ParControl: tree is NULL,  exit\n",1," ",stderr);
  }
  else {
    iVar2 = mxmlFindElement(iVar1,iVar1,"soap:Envelope",0,0,1);
    if (iVar2 == 0) {
      fwrite("ParControl: state is NULL,  exit\n",1,"!",stderr);
    }
    else {
      iVar2 = mxmlFindElement(iVar2,iVar1,"GetParentsControlInfo",0,0,1);
      if (iVar2 == 0) {
        fwrite("ParControl: GetParentsControlInfo build the tree failed\n",1,"8",stderr);
        mxmlDelete(iVar1);
      }
      else {
        iVar2 = FUN_004687ec();
        if ((iVar2 == -1) || (iVar2 == 0)) {
          fwrite("ParControl: there is no  par control info, exit!\n",1,"1",stderr);
          FUN_00469380();
          mxmlDelete(iVar1);
        }
        else {
          "" = FUN_00468708();
          local_14 = "";
          FUN_00468bc8();
          for (local_10[0] = 1; local_10[0] <= local_14; local_10[0] = local_10[0] + 1) {
            iVar2 = FUN_00468ef4(local_10);
            if (iVar2 != 0) {
              fwrite("ParControl: GetParamInfoByGroupIdFromFlash error! exit! \n",1,"9",stderr);
              mxmlDelete(iVar1);
              return 0;
            }
          }
          iVar2 = FUN_0046956c("","",&local_14);
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
          }
          else {
            fwrite("Make XML for par control failed\n",1," ",stderr);
            mxmlDelete(iVar1);
          }
        }
      }
    }
  }
  return 0;
}

