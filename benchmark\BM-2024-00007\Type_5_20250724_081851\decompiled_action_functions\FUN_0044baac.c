
int FUN_0044baac(undefined4 param_1)

{
  int iVar1;
  short sVar3;
  undefined4 uVar2;
  undefined4 local_20;
  undefined4 *local_1c;
  undefined4 local_18;
  undefined4 local_14;
  
  local_20 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar3 = HttpDenyPage(param_1);
  }
  else {
    local_1c = &local_20;
    local_18 = 0;
    local_14 = 0;
    iVar1 = httpGetEnv(param_1,"Save");
    if (iVar1 != 0) {
      uVar2 = httpGetEnv(param_1,"stat");
      local_20 = str2Int(uVar2);
      swSecSetStat(local_20);
    }
    swSecGetStat(&local_20);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "secParam");
    pageDynParaPrintf(&local_1c,0,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/BasicSecurityRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar3 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar3;
}

