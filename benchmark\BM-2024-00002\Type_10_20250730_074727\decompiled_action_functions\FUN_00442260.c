
undefined4 FUN_00442260(int param_1)

{
  int iVar1;
  size_t sVar2;
  FILE *__stream;
  int iVar3;
  int iVar4;
  char *__s;
  undefined auStack_c1a8 [49152];
  char acStack_1a8 [128];
  char local_128 [272];
  undefined4 local_18;
  undefined4 local_14 [2];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetNetworkTomographyResult");
  }
  else {
    memset(auStack_c1a8,0,0xc000);
    memset(acStack_1a8,0,128);
    local_128[0] = '\0';
    local_128[1] = '\0';
    local_128[2] = '\0';
    local_128[3] = '\0';
    local_128[4] = '\0';
    local_128[5] = '\0';
    local_128[6] = '\0';
    local_128[7] = '\0';
    local_128[8] = '\0';
    local_128[9] = '\0';
    local_128[10] = '\0';
    local_128[11] = '\0';
    local_128[12] = '\0';
    local_128[13] = '\0';
    local_128[14] = '\0';
    local_128[15] = '\0';
    memset(local_128 + 16,0,256);
    local_18 = 0;
    local_14[0] = 0;
    iVar1 = apmib_get(0x1b72,acStack_1a8);
    if (iVar1 == 0) {
      puts("error, apmib get MIB_PING_ADDRESS");
    }
    else {
      snprintf(local_128 + 16,6,"ping  ");
      sVar2 = strlen(acStack_1a8);
      strncat(local_128 + 16,acStack_1a8,sVar2);
      iVar1 = apmib_get(0x1b73,&local_18);
      if (iVar1 == 0) {
        puts("error, apmib get MIB_PING_NUMBER");
      }
      else {
        strcat(local_128 + 16," -c ");
        sprintf(local_128,"%d -w %d",local_18,local_18);
        sVar2 = strlen(local_128);
        strncat(local_128 + 16,local_128,sVar2);
        iVar1 = apmib_get(0x1b74,local_14);
        if (iVar1 == 0) {
          puts("error, apmib get MIB_PING_SIZE");
        }
        else {
          strcat(local_128 + 16," -s ");
          sprintf(local_128 + 8,"%d",local_14[0]);
          sVar2 = strlen(local_128 + 8);
          strncat(local_128 + 16,local_128 + 8,sVar2);
          strcat(local_128 + 16," > /tmp/ping.txt 2>>/tmp/ping.txt");
          puts(local_128 + 16);
          system(local_128 + 16);
          __stream = fopen("/tmp/ping.txt","r");
          if (__stream == 0) {
            puts("/tmp/ping.txt is NULL");
          }
          else {
            sVar2 = fread(auStack_c1a8,1,0xc000,__stream);
            if (sVar2 == 0) {
              puts("fread ping.txt is error ");
              fclose(__stream);
            }
            else {
              fclose(__stream);
            }
          }
        }
      }
    }
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar3 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar3 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar3 = mxmlNewElement(iVar3,"soap:Body");
        if (iVar3 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar3 = mxmlNewElement(iVar3,"GetNetworkTomographyResultResponse");
          if (iVar3 == 0) {
            mxmlDelete(iVar1);
            puts("GetNetworkTomographyResultResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
            iVar4 = mxmlNewElement(iVar3,"GetNetworkTomographyResultResult");
            if (iVar4 == 0) {
              mxmlDelete(iVar1);
              puts("GetNetworkTomographyResultResult=NULL");
            }
            else {
              mxmlNewText(iVar4,0,"O");
              iVar3 = mxmlNewElement(iVar3,"PingResult");
              if (iVar3 == 0) {
                mxmlDelete(iVar1);
                puts("PingResult=NULL");
              }
              else {
                mxmlNewText(iVar3,0,auStack_c1a8);
                if ("" == 0) {
                  __s = mxmlSaveAllocString(iVar1,0);
                  if (__s != 0) {
                    puts(__s);
                    FUN_0041ed70("",200,__s,"");
                    free(__s);
                  }
                }
                mxmlDelete(iVar1);
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

