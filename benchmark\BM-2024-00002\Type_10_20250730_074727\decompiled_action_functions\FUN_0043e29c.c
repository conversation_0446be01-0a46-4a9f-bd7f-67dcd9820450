
undefined4 FUN_0043e29c(int param_1)

{
  int iVar1;
  char *pcVar2;
  int iVar3;
  int iVar4;
  char local_6c [32];
  undefined4 local_4c;
  undefined4 local_48;
  undefined4 local_44;
  undefined4 local_40;
  undefined4 local_3c;
  undefined4 local_38;
  undefined4 local_34;
  undefined4 local_30;
  in_addr local_2c;
  undefined4 local_28;
  undefined4 local_24;
  undefined4 local_20;
  undefined4 local_1c;
  undefined4 local_18;
  undefined4 local_14;
  undefined4 local_10;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetDeviceDomainName");
  }
  else {
    local_6c[0] = '\0';
    local_6c[1] = '\0';
    local_6c[2] = '\0';
    local_6c[3] = '\0';
    local_6c[4] = '\0';
    local_6c[5] = '\0';
    local_6c[6] = '\0';
    local_6c[7] = '\0';
    local_6c[8] = '\0';
    local_6c[9] = '\0';
    local_6c[10] = '\0';
    local_6c[11] = '\0';
    local_6c[12] = '\0';
    local_6c[13] = '\0';
    local_6c[14] = '\0';
    local_6c[15] = '\0';
    local_6c[16] = '\0';
    local_6c[17] = '\0';
    local_6c[18] = '\0';
    local_6c[19] = '\0';
    local_6c[20] = '\0';
    local_6c[21] = '\0';
    local_6c[22] = '\0';
    local_6c[23] = '\0';
    local_6c[24] = '\0';
    local_6c[25] = '\0';
    local_6c[26] = '\0';
    local_6c[27] = '\0';
    local_6c[28] = '\0';
    local_6c[29] = '\0';
    local_6c[30] = '\0';
    local_6c[31] = '\0';
    local_4c = 0;
    local_48 = 0;
    local_44 = 0;
    local_40 = 0;
    local_3c = 0;
    local_38 = 0;
    local_34 = 0;
    local_30 = 0;
    local_2c.s_addr = 0;
    local_28 = 0;
    local_24 = 0;
    local_20 = 0;
    local_1c = 0;
    local_18 = 0;
    local_14 = 0;
    local_10 = 0;
    iVar1 = apmib_get(170,&local_2c);
    if (iVar1 == 0) {
      puts("get IP_ADDR error");
    }
    pcVar2 = inet_ntoa(local_2c);
    strcpy(local_6c,pcVar2);
    memset(&local_2c,0," ");
    iVar1 = apmib_getDef(170,&local_2c);
    if (iVar1 == 0) {
      puts("get IP_ADDR error");
    }
    pcVar2 = inet_ntoa(local_2c);
    strcpy(&local_4c,pcVar2);
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar3 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar3 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar3 = mxmlNewElement(iVar3,"soap:Body");
        if (iVar3 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar3 = mxmlNewElement(iVar3,"GetDeviceDomainNameResponse");
          if (iVar3 == 0) {
            mxmlDelete(iVar1);
            puts("GetDeviceDomainNameResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
            iVar4 = mxmlNewElement(iVar3,"GetDeviceDomainNameResult");
            if (iVar4 == 0) {
              mxmlDelete(iVar1);
              puts("GetDeviceDomainNameResult=NULL");
            }
            else {
              mxmlNewText(iVar4,0,"O");
              iVar4 = mxmlNewElement(iVar3,"DomainName");
              if (iVar4 == 0) {
                mxmlDelete(iVar1);
                puts("DomainName=NULL");
              }
              else {
                mxmlNewText(iVar4,0,local_6c);
                iVar3 = mxmlNewElement(iVar3,"DefaultDomainName");
                if (iVar3 == 0) {
                  mxmlDelete(iVar1);
                  puts("DefaultDomainName=NULL");
                }
                else {
                  mxmlNewText(iVar3,0,&local_4c);
                  if ("" == 0) {
                    pcVar2 = mxmlSaveAllocString(iVar1,0);
                    if (pcVar2 != 0) {
                      FUN_0041ed70("",200,pcVar2,"");
                      puts(pcVar2);
                      free(pcVar2);
                    }
                  }
                  mxmlDelete(iVar1);
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

