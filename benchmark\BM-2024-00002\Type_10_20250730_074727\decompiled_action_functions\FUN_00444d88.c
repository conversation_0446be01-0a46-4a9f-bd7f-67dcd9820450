
void FUN_00444d88(void)

{
  char *__s1;
  FILE *__stream;
  int iVar1;
  char *pcVar2;
  undefined4 uVar3;
  undefined4 uVar4;
  undefined4 uVar5;
  void *__ptr;
  char acStack_17c [256];
  char local_7c [40];
  undefined auStack_54 [8];
  char acStack_4c [32];
  char acStack_2c [36];
  
  memset(acStack_17c,0,256);
  local_7c[0] = '\0';
  local_7c[1] = '\0';
  local_7c[2] = '\0';
  local_7c[3] = '\0';
  local_7c[4] = '\0';
  local_7c[5] = '\0';
  local_7c[6] = '\0';
  local_7c[7] = '\0';
  local_7c[8] = '\0';
  local_7c[9] = '\0';
  local_7c[10] = '\0';
  local_7c[11] = '\0';
  local_7c[12] = '\0';
  local_7c[13] = '\0';
  local_7c[14] = '\0';
  local_7c[15] = '\0';
  local_7c[16] = '\0';
  local_7c[17] = '\0';
  local_7c[18] = '\0';
  local_7c[19] = '\0';
  local_7c[20] = '\0';
  local_7c[21] = '\0';
  local_7c[22] = '\0';
  local_7c[23] = '\0';
  local_7c[24] = '\0';
  local_7c[25] = '\0';
  local_7c[26] = '\0';
  local_7c[27] = '\0';
  local_7c[28] = '\0';
  local_7c[29] = '\0';
  local_7c[30] = '\0';
  local_7c[31] = '\0';
  __s1 = FUN_00423814();
  if (__s1 != 0) {
    __stream = fopen("/proc/net/arp","r");
    if (__stream == 0) {
      puts("no proc fs mounted!");
      return;
    }
    memcpy(local_7c,"00:00:00:00:00:00",18);
    do {
      pcVar2 = fgets(acStack_17c,256,__stream);
      if (pcVar2 == 0) goto LAB_00444ef4;
      sscanf(acStack_17c,"%31s %7s %7s %31s",acStack_4c,local_7c + " ",auStack_54,acStack_2c);
      iVar1 = strcmp(__s1,acStack_4c);
    } while (iVar1 != 0);
    strcpy(local_7c,acStack_2c);
LAB_00444ef4:
    fclose(__stream);
  }
  uVar3 = mxmlNewXML("1.0");
  uVar4 = mxmlNewElement(uVar3,"soap:Envelope");
  mxmlElementSetAttr(uVar4,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
  mxmlElementSetAttr(uVar4,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
  mxmlElementSetAttr(uVar4,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
  uVar4 = mxmlNewElement(uVar4,"soap:Body");
  uVar4 = mxmlNewElement(uVar4,"GetLocalMacAddressResponse");
  mxmlElementSetAttr(FUN_00444d88,"xmlns","http://purenetworks.com/HNAP1/");
  uVar5 = mxmlNewElement(uVar4,"GetLocalMacAddressResult");
  mxmlNewText(uVar5,0,"o");
  uVar4 = mxmlNewElement(uVar4,"localMac");
  mxmlNewText(uVar4,0,local_7c);
  __ptr = mxmlSaveAllocString(uVar3,0);
  FUN_0041ed70("",200,__ptr,"");
  free(__ptr);
  mxmlDelete(uVar3);
  return;
}

