
int FUN_004377a4(undefined4 param_1)

{
  byte bVar1;
  undefined4 uVar2;
  char *__s1;
  int iVar3;
  short sVar7;
  int iVar4;
  size_t sVar5;
  char *__s1_00;
  int iVar6;
  
  iVar6 = "";
  uVar2 = httpReqMemPartIdGet();
  __s1 = httpAuthorizationGet(param_1,0);
  httpStatusSet(param_1,0);
  iVar3 = HttpAccessPermit(param_1);
  if (iVar3 == 0) {
    sVar7 = HttpDenyPage(param_1);
  }
  else {
    httpHeaderGenerate(param_1);
    iVar3 = swGetFirstState();
    bVar1 = 0;
    if (iVar3 != 0) {
      bVar1 = 1;
      iVar3 = swSystemModeIsNotAP();
      if (iVar3 != 0) {
        bVar1 = 0;
      }
    }
    iVar3 = httpGetEnv(param_1,"WzdStepOnly");
    iVar4 = strncmp(__s1,"Basic ",6);
    if (iVar4 == 0) {
      __s1 = __s1 + 6;
    }
    sVar5 = strlen(__s1);
    __s1_00 = memPoolAlloc(uVar2,(sVar5 & -1) + 1);
    if (__s1_00 != 0) {
      httpPwdDecode(__s1,__s1_00,(int)sVar5);
      memPoolFree(uVar2,__s1_00);
    }
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "visibleMenuList");
    if ((bool)(bVar1 & iVar3 == 0)) {
      httpPrintf(param_1,"\"WzdStartRpm\",\n");
    }
    else {
      iVar3 = isSupportAcountEnable();
      if ((iVar3 == 1) && (iVar3 = strcmp(__s1_00,"Support"), iVar3 == 0)) {
        httpPrintf(param_1,"\"%s\",\n","StatusRpm");
        httpPrintf(param_1,"\"%s\",\n","WlanNetworkRpm");
        httpPrintf(param_1,"\"%s\",\n","WlanAdvRpm");
        httpPrintf(param_1,"\"%s\",\n","WlanStationRpm");
        httpPrintf(param_1,"\"%s\",\n","WlanMacFilterRpm");
        httpPrintf(param_1,"\"%s\",\n","WlanSecurityRpm");
      }
      else {
        for (; iVar6 != 0; iVar6 = *(int *)(iVar6 + "@")) {
          httpPrintf(param_1,"\"%s\",\n",iVar6);
        }
      }
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,4,1);
    iVar6 = httpRpmFsA(param_1,"/userRpm/MenuRpm.htm");
    if (iVar6 == 2) {
      return 2;
    }
    sVar7 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar7;
}

