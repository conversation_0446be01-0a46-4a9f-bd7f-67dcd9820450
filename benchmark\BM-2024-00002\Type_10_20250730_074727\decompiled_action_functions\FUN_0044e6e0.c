
code * FUN_0044e6e0(int param_1)

{
  code *pcVar1;
  int iVar2;
  int iVar3;
  char *__s1;
  void *pvVar4;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetTriggerADIC");
    pcVar1 = 0;
  }
  else {
    iVar2 = mxmlLoadString(0,param_1,0);
    if (iVar2 == 0) {
      puts("tree=NULL");
      pcVar1 = 0;
    }
    else {
      iVar3 = mxmlFindElement(iVar2,iVar2,"soap:Envelope",0,0,1);
      if (iVar3 == 0) {
        puts("state=NULL");
        pcVar1 = 0;
      }
      else {
        iVar3 = mxmlFindElement(iVar2,iVar2,"SetTriggerADIC",0,0,1);
        if (iVar3 != 0) {
          iVar2 = mxmlFindElement(iVar3,iVar2,"TriggerADIC",0,0,1);
          if (iVar2 == 0) {
            puts("tree=NULL");
            return 0;
          }
          __s1 = mxmlGetText(iVar2,0);
          if (__s1 != 0) {
            iVar2 = strncmp(__s1,"true",4);
            if (iVar2 == 0) {
              "" = '\x01';
            }
            else {
              "" = '\0';
            }
          }
        }
        iVar2 = mxmlNewXML("1.0");
        if (iVar2 == 0) {
          puts("xml=NULL");
          pcVar1 = 0;
        }
        else {
          iVar3 = mxmlNewElement(iVar2,"soap:Envelope");
          if (iVar3 == 0) {
            puts("soap_env=NULL");
            pcVar1 = 0;
          }
          else {
            mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
            mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
            mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
            iVar3 = mxmlNewElement(iVar3,"soap:Body");
            if (iVar3 == 0) {
              puts("body=NULL");
              pcVar1 = 0;
            }
            else {
              iVar3 = mxmlNewElement(iVar3,"SetTriggerADICResponse");
              if (iVar3 == 0) {
                puts("SetTriggerADICResponse=NULL");
                pcVar1 = 0;
              }
              else {
                mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
                iVar3 = mxmlNewElement(iVar3,"SetTriggerADICResult");
                if (iVar3 == 0) {
                  puts("SetTriggerADICResult_xml=NULL");
                  pcVar1 = 0;
                }
                else {
                  if ("" == '\x01') {
                    FUN = s_OK_DETECTING_2_004ae20c;
                    mxmlNewText(iVar3,0,"OK_DETECTING_2");
                    pvVar4 = mxmlSaveAllocString(iVar2,0);
                    if (pvVar4 == 0) {
                      puts("retstring=NULL");
                    }
                    else {
                      FUN_0041ed70("",200,pvVar4,"");
                      free(pvVar4);
                    }
                    mxmlDelete(iVar2);
                    iVar2 = FUN_00426c54();
                    if (iVar2 == 0) {
                      iVar2 = FUN_00451ef0("eth1",3);
                      if (iVar2 == 1) {
                        puts("WAN is DHCP");
                        FUN = s_OK_DHCP_004ae238;
                      }
                      else if (iVar2 == 2) {
                        puts("WAN is PPPoE");
                        FUN = s_OK_PPPOE_004ae250;
                      }
                      else if (iVar2 == 0) {
                        puts("WAN is NULL");
                        FUN = s_ERROR_004ae268;
                      }
                      else {
                        puts("WAN is DISCOVER_ERROR");
                        FUN = s_ERROR_004ae268;
                      }
                    }
                    else {
                      FUN = s_ERROR_WANLinkDown_004ae288;
                    }
                  }
                  else {
                    mxmlNewText(iVar3,0,FUN);
                    pvVar4 = mxmlSaveAllocString(iVar2,0);
                    if (pvVar4 == 0) {
                      puts("retstring=NULL");
                    }
                    else {
                      FUN_0041ed70("",200,pvVar4,"");
                      free(pvVar4);
                    }
                    mxmlDelete(iVar2);
                  }
                  pcVar1 = FUN_004ad49c;
                }
              }
            }
          }
        }
      }
    }
  }
  return pcVar1;
}

