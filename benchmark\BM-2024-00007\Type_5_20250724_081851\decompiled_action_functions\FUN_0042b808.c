
int FUN_0042b808(undefined4 param_1)

{
  int iVar1;
  short sVar8;
  char *pcVar2;
  uint uVar3;
  uint32_t uVar4;
  uint32_t uVar5;
  uint32_t uVar6;
  uint32_t uVar7;
  int iVar9;
  uint uVar10;
  uint local_4f0;
  undefined auStack_4ec [8];
  char acStack_4e4 [16];
  char acStack_4d4 [64];
  char acStack_494 [63];
  undefined local_455;
  uint32_t local_454;
  uint32_t local_450;
  uint32_t local_44c;
  in_addr_t local_448;
  in_addr_t local_444;
  uint local_440;
  uint local_43c;
  uint local_438;
  uint local_434;
  uint local_430;
  undefined *local_42c;
  undefined4 local_428;
  undefined *local_424;
  undefined4 local_420;
  undefined *local_41c;
  undefined4 local_418;
  undefined *local_414;
  undefined4 local_410;
  undefined *local_40c;
  undefined4 local_408;
  undefined *local_404;
  undefined4 local_400;
  undefined *local_3fc;
  undefined4 local_3f8;
  undefined *local_3f4;
  undefined4 local_3f0;
  undefined *local_3ec;
  undefined4 local_3e8;
  undefined *local_3e4;
  undefined4 local_3e0;
  undefined *local_3dc;
  undefined4 local_3d8;
  undefined *local_3d4;
  undefined4 local_3d0;
  undefined *local_3cc;
  undefined4 local_3c8;
  undefined *local_3c4;
  undefined4 local_3c0;
  undefined *local_3bc;
  undefined4 local_3b8;
  undefined *local_3b4;
  undefined4 local_3b0;
  undefined *local_3ac;
  undefined4 local_3a8;
  undefined *local_3a4;
  undefined4 local_3a0;
  undefined *local_39c;
  undefined4 local_398;
  undefined *local_394;
  undefined4 local_390;
  undefined *local_38c;
  undefined4 local_388;
  undefined *local_384;
  undefined4 local_380;
  undefined *local_37c;
  undefined4 local_378;
  undefined *local_374;
  undefined4 local_370;
  undefined *local_36c;
  undefined4 local_368;
  undefined *local_364;
  undefined4 local_360;
  undefined *local_35c;
  undefined4 local_358;
  undefined *local_354;
  undefined4 local_350;
  undefined4 local_34c;
  undefined auStack_344 [4];
  undefined auStack_340 [4];
  undefined auStack_33c [4];
  undefined auStack_338 [4];
  undefined auStack_334 [4];
  undefined auStack_330 [4];
  undefined auStack_32c [4];
  undefined auStack_328 [4];
  undefined auStack_324 [256];
  undefined auStack_224 [256];
  undefined auStack_124 [4];
  undefined auStack_120 [4];
  undefined auStack_11c [64];
  undefined auStack_dc [16];
  undefined auStack_cc [16];
  undefined auStack_bc [16];
  undefined auStack_ac [4];
  undefined auStack_a8 [4];
  undefined auStack_a4 [4];
  undefined auStack_a0 [4];
  undefined auStack_9c [16];
  undefined auStack_8c [4];
  undefined auStack_88 [16];
  undefined auStack_78 [4];
  undefined auStack_74 [4];
  undefined auStack_70 [4];
  undefined auStack_6c [64];
  undefined auStack_2c [4];
  
  memset(acStack_494,0,"h");
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar8 = HttpDenyPage(param_1);
    goto LAB_0042c800;
  }
  pcVar2 = httpGetEnv(param_1,"wan");
  uVar10 = 0;
  if (((pcVar2 != 0) && (uVar10 = atoi(pcVar2), uVar10 < 0)) ||
     (uVar3 = getMaxWanPortNumber(), uVar3 <= uVar10)) {
    uVar10 = 0;
  }
  local_42c = auStack_344;
  local_424 = auStack_340;
  local_41c = auStack_33c;
  local_414 = auStack_338;
  local_40c = auStack_334;
  local_404 = auStack_330;
  local_3fc = auStack_32c;
  local_3f4 = auStack_328;
  local_3ec = auStack_324;
  local_3e4 = auStack_224;
  local_3dc = auStack_124;
  local_3d4 = auStack_120;
  local_3cc = auStack_11c;
  local_3c4 = auStack_dc;
  local_3bc = auStack_cc;
  local_3b4 = auStack_bc;
  local_3ac = auStack_ac;
  local_3a4 = auStack_a8;
  local_39c = auStack_a4;
  local_394 = auStack_a0;
  local_38c = auStack_9c;
  local_384 = auStack_8c;
  local_37c = auStack_88;
  local_374 = auStack_78;
  local_36c = auStack_74;
  local_364 = auStack_70;
  local_35c = auStack_6c;
  local_354 = auStack_2c;
  local_34c = 0;
  local_428 = 0;
  local_420 = 0;
  local_418 = 0;
  local_410 = 0;
  local_408 = 0;
  local_358 = "@";
  local_3e8 = "@";
  local_3e0 = "@";
  local_3c8 = "@";
  local_378 = 16;
  local_400 = 0;
  local_3f8 = 0;
  local_3f0 = 0;
  local_3d8 = 0;
  local_3d0 = 0;
  local_3c0 = 16;
  local_3b8 = 16;
  local_3b0 = 16;
  local_3a8 = 0;
  local_3a0 = 0;
  local_398 = 0;
  local_390 = 0;
  local_388 = 16;
  local_380 = 0;
  local_370 = 0;
  local_368 = 0;
  local_360 = 0;
  local_350 = 0;
  iVar1 = httpGetEnv(param_1,"Save");
  if (iVar1 == 0) {
    iVar1 = httpGetEnv(param_1,"RenewIp");
    if ((iVar1 != 0) && (iVar1 = wanIsConnected(0), iVar1 == 1)) {
      swDhcpcStart(uVar10);
    }
    iVar1 = httpGetEnv(param_1,"ReleaseIp");
    if (iVar1 != 0) {
      swDhcpcStop(uVar10);
    }
LAB_0042be6c:
    swGetDhcpcCfg(uVar10,acStack_494);
    iVar1 = swGetWanType(uVar10);
    if (iVar1 != 0) {
      local_440 = 1;
      local_454 = 0;
      local_450 = 0;
      local_44c = 0;
      local_448 = 0;
      local_444 = 0;
    }
    if ((local_448 == 0) && (local_444 == 0)) {
      local_440 = 1;
    }
    local_4f0 = 0;
    pageParaSet(&local_42c,&local_4f0,1);
    pageParaSet(&local_42c,&local_4f0,3);
    pageParaSet(&local_42c,&local_4f0,5);
    pageParaSet(&local_42c,&local_4f0,10);
    pageParaSet(&local_42c,&local_4f0,11);
    memset(acStack_4d4,0,"@");
    pageParaSet(&local_42c,acStack_4d4,8);
    pageParaSet(&local_42c,acStack_4d4,9);
    pageParaSet(&local_42c,acStack_4d4,12);
    local_4f0 = uVar10;
    pageParaSet(&local_42c,&local_4f0,6);
    local_4f0 = local_434;
    pageParaSet(&local_42c,&local_4f0,24);
    local_4f0 = local_430;
    pageParaSet(&local_42c,&local_4f0,25);
    pageParaSet(&local_42c,acStack_494,26);
    local_4f0 = getMaxWanPortNumber();
    pageParaSet(&local_42c,&local_4f0,0);
    iVar1 = swGetDhcpcMode(uVar10);
    local_4f0 = (uint)(iVar1 != 0);
    pageParaSet(&local_42c,&local_4f0,2);
    iVar1 = swGetProductId();
    iVar9 = 1;
    if ((iVar1 == 0x25430001) || (iVar1 = swGetProductId(), iVar1 == 0x25430002)) {
LAB_0042c134:
      iVar1 = swGetWanType(uVar10);
      if (iVar1 == 0) {
        iVar1 = swDhcpcStateIsProcess(uVar10);
        uVar3 = 1;
        if (iVar1 != 0) goto LAB_0042c178;
      }
      local_4f0 = 0;
      uVar3 = local_4f0;
    }
    else {
      iVar9 = swIsWanConnected(uVar10);
      uVar3 = 2;
      if (iVar9 != 0) goto LAB_0042c134;
    }
LAB_0042c178:
    local_4f0 = uVar3;
    pageParaSet(&local_42c,&local_4f0,4);
    local_4f0 = 0;
    pageParaSet(&local_42c,&local_4f0,7);
    uVar4 = ntohl(local_454);
    uVar5 = ntohl(local_454);
    uVar6 = ntohl(local_454);
    uVar7 = ntohl(local_454);
    sprintf(acStack_4e4,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
            (int)(uVar6 & -256) >> 8,uVar7 & 255);
    pageParaSet(&local_42c,acStack_4e4,13);
    uVar4 = ntohl(local_450);
    uVar5 = ntohl(local_450);
    uVar6 = ntohl(local_450);
    uVar7 = ntohl(local_450);
    sprintf(acStack_4e4,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
            (int)(uVar6 & -256) >> 8,uVar7 & 255);
    pageParaSet(&local_42c,acStack_4e4,14);
    uVar4 = ntohl(local_44c);
    uVar5 = ntohl(local_44c);
    uVar6 = ntohl(local_44c);
    uVar7 = ntohl(local_44c);
    sprintf(acStack_4e4,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
            (int)(uVar6 & -256) >> 8,uVar7 & 255);
    pageParaSet(&local_42c,acStack_4e4,15);
    iVar1 = swGetDhcpcMode(uVar10);
    if ((iVar1 == 0) || (iVar1 = swDhcpcStateIsSuspend(uVar10), iVar1 == 0)) {
      local_4f0 = 1;
    }
    else {
      local_4f0 = 0;
    }
    if (iVar9 == 0) {
      local_4f0 = 1;
    }
    pageParaSet(&local_42c,&local_4f0,16);
    uVar3 = 1;
    if (local_454 != 0) {
      iVar1 = swGetDhcpcMode(uVar10);
      uVar3 = 0;
      if (iVar1 == 0) {
        uVar3 = 1;
      }
    }
    local_4f0 = uVar3;
    if (iVar9 == 0) {
      local_4f0 = 1;
    }
    pageParaSet(&local_42c,&local_4f0,17);
    local_4f0 = local_43c;
    pageParaSet(&local_42c,&local_4f0,18);
    local_4f0 = (uint)(local_440 == 0);
    pageParaSet(&local_42c,&local_4f0,19);
    uVar4 = ntohl(local_448);
    uVar5 = ntohl(local_448);
    uVar6 = ntohl(local_448);
    uVar7 = ntohl(local_448);
    sprintf(acStack_4e4,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
            (int)(uVar6 & -256) >> 8,uVar7 & 255);
    pageParaSet(&local_42c,acStack_4e4,20);
    local_4f0 = (uint)(local_440 == 1);
    pageParaSet(&local_42c,&local_4f0,21);
    uVar4 = ntohl(local_444);
    uVar5 = ntohl(local_444);
    uVar6 = ntohl(local_444);
    uVar7 = ntohl(local_444);
    sprintf(acStack_4e4,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
            (int)(uVar6 & -256) >> 8,uVar7 & 255);
    pageParaSet(&local_42c,acStack_4e4,22);
    local_4f0 = (uint)(local_438 == 1);
    pageParaSet(&local_42c,&local_4f0,23);
    httpGetDetectedWanType(uVar10);
    iVar1 = httpGetEnv(param_1,"detect");
    if (iVar1 != 0) {
      httpOnWanTypeDetect(uVar10);
    }
    httpPrintfWanTypeDetectInfo(param_1);
    swGetSystemMode(auStack_4ec);
    local_4f0 = 3;
    pageParaSet(&local_42c,&local_4f0,27);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "dhcpInf");
    iVar1 = 0;
    do {
      iVar9 = iVar1 + 1;
      pageDynParaPrintf(&local_42c,iVar1,param_1);
      iVar1 = iVar9;
    } while (iVar9 != 28);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintfWanTypeInfo(param_1);
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/WanDynamicIpCfgRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    iVar1 = 10;
    pcVar2 = 0;
  }
  else {
    pcVar2 = httpGetEnv(param_1,"mtu");
    if (pcVar2 != 0) {
      local_43c = atoi(pcVar2);
    }
    pcVar2 = httpGetEnv(param_1,"manual");
    if (pcVar2 == 0) {
      local_440 = 1;
      local_448 = 0;
      local_444 = 0;
LAB_0042bc34:
      pcVar2 = httpGetEnv(param_1,"flagMode");
      if (pcVar2 != 0) {
        iVar1 = atoi(pcVar2);
        local_438 = (uint)(iVar1 == 2);
      }
      pcVar2 = httpGetEnv(param_1,"downBandwidth");
      if (pcVar2 != 0) {
        local_434 = atoi(pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"upBandwidth");
      if (pcVar2 != 0) {
        local_430 = atoi(pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"hostName");
      if (pcVar2 != 0) {
        iVar1 = swChkHostName(pcVar2);
        if (iVar1 == 0) {
          pcVar2 = acStack_4d4;
          sprintf(pcVar2,"../userRpm/WanDynamicIpCfgRpm.htm?wan=%d",uVar10);
          iVar1 = 0xfb1;
          goto LAB_0042c7f8;
        }
        local_455 = 0;
        strncpy(acStack_494,pcVar2,"?");
      }
      iVar1 = swChkDhcpcCfg(uVar10,acStack_494);
      if (iVar1 == 0) {
        swSetDhcpcCfg(uVar10,acStack_494,0);
        goto LAB_0042be6c;
      }
      pcVar2 = acStack_4d4;
      sprintf(pcVar2,"../userRpm/WanDynamicIpCfgRpm.htm?wan=%d",uVar10);
    }
    else {
      iVar1 = atoi(pcVar2);
      local_440 = (uint)(iVar1 != 2);
      if (local_440 != 0) goto LAB_0042bc34;
      pcVar2 = httpGetEnv(param_1,"dnsserver");
      if (pcVar2 == 0) {
LAB_0042bb9c:
        pcVar2 = httpGetEnv(param_1,"dnsserver2");
        if (pcVar2 != 0) {
          iVar1 = swChkDotIpAddr(pcVar2);
          if (iVar1 == 0) {
            pcVar2 = acStack_4d4;
            sprintf(pcVar2,"../userRpm/WanDynamicIpCfgRpm.htm?wan=%d",uVar10);
            iVar1 = 0x138e;
            goto LAB_0042c7f8;
          }
          local_444 = inet_addr(pcVar2);
        }
        goto LAB_0042bc34;
      }
      iVar1 = swChkDotIpAddr(pcVar2);
      if (iVar1 != 0) {
        local_448 = inet_addr(pcVar2);
        goto LAB_0042bb9c;
      }
      pcVar2 = acStack_4d4;
      sprintf(pcVar2,"../userRpm/WanDynamicIpCfgRpm.htm?wan=%d",uVar10);
      iVar1 = 0x138d;
    }
  }
LAB_0042c7f8:
  sVar8 = HttpErrorPage(param_1,iVar1,pcVar2,0);
LAB_0042c800:
  return sVar8;
}

