
int FUN_0044fe04(undefined4 param_1)

{
  int iVar1;
  short sVar4;
  size_t sVar2;
  char *pcVar3;
  int iVar5;
  char acStack_1a0 [32];
  undefined *local_180;
  undefined4 local_17c;
  undefined *local_178;
  undefined4 local_174;
  undefined *local_170;
  undefined4 local_16c;
  undefined4 *local_168;
  undefined4 local_164;
  undefined *local_160;
  undefined4 local_15c;
  undefined4 *local_158;
  undefined4 local_154;
  undefined4 *local_150;
  undefined4 local_14c;
  undefined4 *local_148;
  undefined4 local_144;
  undefined4 *local_140;
  undefined4 local_13c;
  undefined4 local_138;
  undefined auStack_130 [64];
  undefined auStack_f0 [64];
  undefined auStack_b0 [64];
  undefined4 local_70;
  undefined auStack_6c [32];
  char acStack_4c [32];
  undefined4 local_2c;
  undefined4 local_28;
  undefined4 local_24;
  undefined4 local_20 [3];
  
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar4 = HttpDenyPage(param_1);
  }
  else {
    local_178 = auStack_f0;
    local_170 = auStack_b0;
    local_168 = &local_70;
    local_160 = auStack_6c;
    local_158 = &local_2c;
    local_150 = &local_28;
    local_148 = &local_24;
    local_140 = local_20;
    local_16c = "@";
    local_15c = " ";
    local_17c = "@";
    local_174 = "@";
    local_138 = 0;
    local_164 = 0;
    local_154 = 0;
    local_14c = 0;
    local_144 = 0;
    local_13c = 0;
    local_180 = auStack_130;
    swGetAutoMailCfg(auStack_130);
    iVar1 = httpGetEnv(param_1,"Save");
    if (iVar1 != 0) {
      sVar2 = strlen(acStack_4c);
      strcpy(acStack_1a0,acStack_4c);
      acStack_1a0[sVar2] = '\0';
      memset(auStack_130,0,276);
      pcVar3 = httpGetEnv(param_1,"FromAddr");
      if (pcVar3 != 0) {
        sVar2 = strlen(pcVar3);
        if ("?" < sVar2) {
          sVar2 = "?";
        }
        memcpy(auStack_130,pcVar3,sVar2);
      }
      pcVar3 = httpGetEnv(param_1,"ToAddr");
      if (pcVar3 != 0) {
        sVar2 = strlen(pcVar3);
        if ("?" < sVar2) {
          sVar2 = "?";
        }
        memcpy(auStack_f0,pcVar3,sVar2);
      }
      pcVar3 = httpGetEnv(param_1,"SMTPAddr");
      if (pcVar3 != 0) {
        sVar2 = strlen(pcVar3);
        if ("?" < sVar2) {
          sVar2 = "?";
        }
        memcpy(auStack_b0,pcVar3,sVar2);
      }
      iVar1 = httpGetEnv(param_1,"Verify");
      if (iVar1 == 0) {
        local_70 = 0;
      }
      else {
        local_70 = 1;
        pcVar3 = httpGetEnv(param_1,"User");
        if (pcVar3 != 0) {
          sVar2 = strlen(pcVar3);
          if (31 < sVar2) {
            sVar2 = 31;
          }
          memcpy(auStack_6c,pcVar3,sVar2);
        }
        pcVar3 = httpGetEnv(param_1,"Password");
        if (pcVar3 != 0) {
          iVar1 = strcmp(pcVar3,"\t\t\t\t\t\t");
          if (iVar1 == 0) {
            sVar2 = strlen(acStack_1a0);
            strcpy(acStack_4c,acStack_1a0);
            acStack_4c[sVar2] = '\0';
          }
          else {
            sVar2 = strlen(pcVar3);
            if (31 < sVar2) {
              sVar2 = 31;
            }
            memcpy(acStack_4c,pcVar3,sVar2);
          }
        }
      }
      iVar1 = httpGetEnv(param_1,"AutoMail");
      if (iVar1 == 0) {
        local_2c = 0;
        swSetAutoMailCfg(auStack_130,0);
      }
      else {
        local_2c = 1;
        iVar1 = getEnvToInt(param_1,"TimeType",0,1);
        if (iVar1 == 0) {
          local_24 = getEnvToInt(param_1,"Hour",0,23);
          local_20[0] = getEnvToInt(param_1,"Minute",0,";");
          local_28 = 0;
        }
        else {
          local_28 = getEnvToInt(param_1,"TimeLen",1,99);
        }
        swSetAutoMailCfg(auStack_130,1);
        puts("set AutoMail");
      }
    }
    swGetAutoMailCfg(auStack_130);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "autoEmailConf");
    iVar1 = 0;
    do {
      iVar5 = iVar1 + 1;
      pageDynParaPrintf(&local_180,iVar1,param_1);
      iVar1 = iVar5;
    } while (iVar5 != 9);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/AutoEmailRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar4 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar4;
}

