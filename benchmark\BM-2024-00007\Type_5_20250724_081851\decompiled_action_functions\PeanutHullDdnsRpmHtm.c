
int PeanutHullDdnsRpmHtm(undefined4 param_1)

{
  char *pcVar1;
  int iVar2;
  uint uVar3;
  short sVar4;
  char *pcVar5;
  undefined *puVar6;
  undefined *puVar7;
  int iVar8;
  code *pcVar9;
  int local_c08;
  undefined4 local_c04;
  uint local_c00;
  uint local_bfc;
  char acStack_bf8 [63];
  undefined local_bb9;
  char acStack_bb8 [63];
  undefined local_b79;
  int local_b74;
  undefined *local_b70;
  undefined4 local_b6c;
  undefined *local_b68;
  undefined4 local_b64;
  undefined *local_b60;
  undefined4 local_b5c;
  undefined *local_b58;
  undefined4 local_b54;
  undefined *local_b50;
  undefined4 local_b4c;
  undefined *local_b48;
  undefined4 local_b44;
  undefined *local_b40;
  undefined4 local_b3c;
  undefined *local_b38;
  undefined4 local_b34;
  undefined *local_b30;
  undefined4 local_b2c;
  undefined *local_b28;
  undefined4 local_b24;
  undefined *local_b20;
  undefined4 local_b1c;
  undefined *local_b18;
  undefined4 local_b14;
  undefined *local_b10;
  undefined4 local_b0c;
  undefined *local_b08;
  undefined4 local_b04;
  undefined *local_b00;
  undefined4 local_afc;
  undefined *local_af8;
  undefined4 local_af4;
  undefined *local_af0;
  undefined4 local_aec;
  undefined *local_ae8;
  undefined4 local_ae4;
  undefined *local_ae0;
  undefined4 local_adc;
  undefined *local_ad8;
  undefined4 local_ad4;
  undefined4 local_ad0;
  undefined local_ac0 [128];
  undefined local_a40 [128];
  undefined local_9c0 [128];
  undefined local_940 [128];
  undefined local_8c0 [128];
  undefined local_840 [128];
  undefined local_7c0 [128];
  undefined local_740 [128];
  undefined local_6c0 [128];
  undefined local_640 [128];
  undefined auStack_5c0 [4];
  undefined auStack_5bc [64];
  undefined auStack_57c [64];
  undefined auStack_53c [4];
  undefined auStack_538 [4];
  undefined auStack_534 [4];
  undefined auStack_530 [4];
  undefined auStack_52c [128];
  undefined auStack_4ac [128];
  undefined auStack_42c [128];
  undefined auStack_3ac [128];
  undefined auStack_32c [128];
  undefined auStack_2ac [128];
  undefined auStack_22c [128];
  undefined auStack_1ac [128];
  undefined auStack_12c [128];
  undefined auStack_ac [128];
  undefined auStack_2c [4];
  undefined auStack_28 [4];
  undefined auStack_24 [8];
  
  local_c08 = 0;
  local_c04 = 0;
  memset(local_ac0,0,0x500);
  memset(&local_bfc,0,140);
  memset(&local_b70,0,176);
  memset(local_ac0,0,0x500);
  local_b70 = auStack_5c0;
  local_b68 = auStack_5bc;
  local_b60 = auStack_57c;
  local_b58 = auStack_53c;
  local_b50 = auStack_538;
  local_b48 = auStack_534;
  local_b40 = auStack_530;
  local_b38 = auStack_52c;
  local_b30 = auStack_4ac;
  local_b28 = auStack_42c;
  local_b20 = auStack_3ac;
  local_b18 = auStack_32c;
  local_b10 = auStack_2ac;
  local_b08 = auStack_22c;
  local_b00 = auStack_1ac;
  local_af8 = auStack_12c;
  local_af0 = auStack_ac;
  local_ae8 = auStack_2c;
  local_ae0 = auStack_28;
  local_ad8 = auStack_24;
  local_b34 = 128;
  local_b2c = 128;
  local_b24 = 128;
  local_b1c = 128;
  local_b14 = 128;
  local_b5c = "@";
  local_b64 = "@";
  local_ad0 = 0;
  local_b6c = 0;
  local_b54 = 0;
  local_b4c = 0;
  local_b44 = 0;
  local_b3c = 0;
  local_b0c = 128;
  local_aec = 128;
  local_b04 = 128;
  local_afc = 128;
  local_af4 = 128;
  local_ae4 = 0;
  local_adc = 0;
  local_ad4 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  memset(&local_bfc,0,140);
  pcVar1 = httpGetEnv(param_1,"wan");
  if (pcVar1 == 0) {
    local_c08 = 0;
  }
  else {
    local_c08 = atoi(pcVar1);
  }
  iVar2 = httpGetEnv(param_1,"Save");
  if ((iVar2 == 0) && (iVar2 = httpGetEnv(param_1,"Login"), iVar2 == 0)) {
    iVar2 = httpGetEnv(param_1,"Logout");
    if (iVar2 == 0) goto LAB_00442290;
    pcVar1 = httpGetEnv(param_1,"wan");
    if (pcVar1 == 0) {
      local_c08 = 0;
    }
    else {
      local_c08 = atoi(pcVar1);
    }
    pcVar9 = swDdnsStop;
  }
  else {
    pcVar1 = httpGetEnv(param_1,"username");
    if (pcVar1 != 0) {
      do {
        pcVar5 = pcVar1;
        pcVar1 = pcVar5 + 1;
      } while (*pcVar5 == ' ');
      if (pcVar5 != 0) {
        local_bb9 = 0;
        strncpy(acStack_bf8,pcVar5,"?");
      }
    }
    pcVar1 = httpGetEnv(param_1,"pwd");
    if (pcVar1 != 0) {
      do {
        pcVar5 = pcVar1;
        pcVar1 = pcVar5 + 1;
      } while (*pcVar5 == ' ');
      if (pcVar5 != 0) {
        local_b79 = 0;
        strncpy(acStack_bb8,pcVar5,"?");
      }
    }
    pcVar1 = httpGetEnv(param_1,"EnDdns");
    if (pcVar1 == 0) {
      local_bfc = 0;
    }
    else {
      iVar2 = atoi(pcVar1);
      local_bfc = (uint)(iVar2 == 2);
    }
    pcVar1 = httpGetEnv(param_1,"wan");
    if (pcVar1 == 0) {
      local_b74 = 0;
    }
    else {
      local_b74 = atoi(pcVar1);
    }
    local_c08 = local_b74;
    swSetPheanutHullDdnsCfg(local_b74,&local_bfc);
    if (local_bfc == 0) {
      pcVar9 = swDdnsStop;
    }
    else {
      pcVar9 = swDdnsStart;
    }
  }
  (*pcVar9)(local_c08,0);
LAB_00442290:
  swGetPheanutHullDdnsCfg(local_c08,&local_bfc);
  uVar3 = swGetPhDdnsDomain(local_c08,local_ac0,local_a40,local_9c0,local_940,local_8c0,local_840,
                            local_7c0,local_740,local_6c0,local_640);
  if (10 < uVar3) {
    local_ac0[0] = 0;
    local_a40[0] = 0;
    local_9c0[0] = 0;
    local_940[0] = 0;
    local_8c0[0] = 0;
    local_840[0] = 0;
    local_7c0[0] = 0;
    local_740[0] = 0;
    local_6c0[0] = 0;
    local_640[0] = 0;
    uVar3 = 0;
  }
  local_c00 = 0;
  pageParaSet(&local_b70,&local_c00,0);
  iVar8 = 1;
  pageParaSet(&local_b70,acStack_bf8,1);
  pageParaSet(&local_b70,acStack_bb8,2);
  local_c00 = local_bfc;
  pageParaSet(&local_b70,&local_c00,3);
  iVar2 = swPhDdnsIsConnecting(local_c08);
  if (iVar2 == 0) {
    iVar8 = 2;
    iVar2 = swPhDdnsIsConnected(local_c08);
    if (iVar2 == 0) {
      iVar8 = 0;
      swPhDdnsIsDisconnected(local_c08);
    }
  }
  iVar2 = phIsCurrentUserPwdAuthFail(local_c08);
  if (iVar2 != 0) {
    iVar8 = 3;
  }
  local_c00 = ("")[iVar8];
  pageParaSet(&local_b70,&local_c00,4);
  local_c00 = (uint)(iVar8 == 1);
  pageParaSet(&local_b70,&local_c00,5);
  local_c00 = uVar3;
  pageParaSet(&local_b70,&local_c00,6);
  iVar2 = 7;
  puVar6 = local_ac0;
  do {
    puVar7 = puVar6 + 128;
    pageParaSet(&local_b70,puVar6,iVar2);
    iVar2 = iVar2 + 1;
    puVar6 = puVar7;
  } while (puVar7 != auStack_5c0);
  if (iVar8 == 2) {
    iVar2 = phIsCurrentUserProType(local_c08);
    local_c00 = (uint)(iVar2 == 0);
  }
  else {
    local_c00 = 2;
  }
  pageParaSet(&local_b70,&local_c00,17);
  pageParaSet(&local_b70,&local_c08,18);
  local_c04 = getMaxWanPortNumber();
  pageParaSet(&local_b70,&local_c04,19);
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "serInf");
  iVar2 = 0;
  do {
    iVar8 = iVar2 + 1;
    pageDynParaPrintf(&local_b70,iVar2,param_1);
    iVar2 = iVar8;
  } while (iVar8 != 20);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  httpPrintfDdnsTypeInfo(param_1);
  HttpWebV4Head(param_1,0,1);
  iVar2 = httpRpmFsA(param_1,"/userRpm/PeanutHullDdnsRpm.htm");
  iVar8 = 2;
  if (iVar2 != 2) {
    sVar4 = HttpErrorPage(param_1,10,0,0);
    iVar8 = sVar4;
  }
  return iVar8;
}

