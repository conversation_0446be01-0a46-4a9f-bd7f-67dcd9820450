
/* WARNING: Type propagation algorithm not settling */

int FUN_0044bcdc(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  short sVar3;
  char *__s2;
  char *pcVar4;
  undefined4 uVar5;
  undefined1 *puVar6;
  uint uVar7;
  int iVar8;
  char *__s1;
  uint uVar9;
  int *piVar10;
  char *__dest;
  undefined auStack_120 [8];
  char local_118 [25];
  undefined local_ff;
  int local_fc [14];
  int *local_c4;
  undefined4 local_c0;
  int *local_bc;
  undefined4 local_b8;
  int *local_b4;
  undefined4 local_b0;
  undefined4 local_ac;
  int local_a4 [10];
  char acStack_7c [76];
  char *local_30;
  char *local_2c;
  
  pcVar4 = acStack_7c;
  local_118[0] = '\0';
  local_118[1] = '\0';
  local_118[2] = '\0';
  local_118[3] = '\0';
  local_118[4] = '\0';
  local_118[5] = '\0';
  local_118[6] = '\0';
  local_118[7] = '\0';
  local_fc[7] = 0;
  local_fc[8] = 0;
  local_fc[9] = 0;
  local_fc[10] = 0;
  local_fc[11] = 0;
  local_fc[12] = 0;
  local_fc[13] = 0;
  memset(pcVar4,0,"H");
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    sVar3 = HttpDenyPage(param_1);
  }
  else {
    local_bc = local_fc + 8;
    local_b8 = 18;
    local_c0 = 0;
    piVar10 = local_a4;
    do {
      *piVar10 = pcVar4;
      piVar10 = piVar10 + 2;
      pcVar4 = pcVar4 + 18;
    } while (piVar10 != local_a4 + 8);
    local_a4[8] = 0;
    piVar10 = local_a4 + 1;
    do {
      *piVar10 = 18;
      piVar10 = piVar10 + 2;
    } while (piVar10 != local_a4 + 9);
    local_b4 = local_fc + 13;
    local_b0 = 0;
    local_ac = 0;
    local_c4 = local_fc + 7;
    iVar2 = httpGetEnv(param_1,"Save");
    if (iVar2 != 0) {
      local_fc[6] = 0;
      local_fc[0] = 0;
      local_fc[1] = 0;
      local_fc[2] = 0;
      local_fc[3] = 0;
      local_fc[4] = 0;
      local_fc[5] = 0;
      iVar2 = getEnvToInt(param_1,"enableWhitelist",0,1);
      iVar8 = iVar2;
      if (iVar2 == -128) {
        perror("Wrong env\n");
        iVar2 = local_fc[0];
        iVar8 = local_fc[13];
      }
      local_fc[13] = iVar8;
      local_fc[0] = iVar2;
      pcVar4 = acStack_7c;
      local_30 = local_118;
      piVar10 = local_fc + 1;
      uVar9 = 0;
      __dest = local_118 + 8;
      local_2c = pcVar4;
      do {
        sprintf(local_30,"mac%d",uVar9);
        __s2 = httpGetEnv(param_1,local_30);
        if (__s2 != 0) {
          uVar7 = 0;
          __s1 = local_2c;
          do {
            bVar1 = uVar9 <= uVar7;
            uVar7 = uVar7 + 1;
            if (bVar1) {
              local_ff = 0;
              strncpy(__dest,__s2,17);
              iVar2 = swChkStrMacAddr(__dest);
              if (iVar2 != 0) {
                swMacStr2Eth(__dest,auStack_120);
                iVar2 = swChkLegalMacAddr(auStack_120);
                if (iVar2 != 0) {
                  pcVar4[17] = '\0';
                  strncpy(pcVar4,__dest,17);
                  memcpy(piVar10,auStack_120,6);
                  break;
                }
              }
              puVar6 = "";
              uVar5 = 0x1399;
              goto LAB_0044c1f8;
            }
            iVar2 = strcasecmp(__s1,__s2);
            __s1 = __s1 + 18;
          } while (iVar2 != 0);
        }
        uVar9 = uVar9 + 1;
        pcVar4 = pcVar4 + 18;
        piVar10 = (int *)(piVar10 + 6);
      } while (uVar9 != 4);
      swSetLocalMgmtMacWhitelist(local_fc);
    }
    swGetLocalMgmtMacWhitelist(local_fc);
    local_fc[7] = 4;
    pageParaSet(&local_c4,local_fc + 7,0);
    piVar10 = local_fc + 1;
    HttpClientMacGet(param_1,local_fc + 8);
    pageParaSet(&local_c4,local_fc + 8,1);
    local_fc[13] = local_fc[0];
    pageParaSet(&local_c4,local_fc + 13,2);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "controlParam");
    pageDynParaPrintf(&local_c4,0,param_1);
    pageDynParaPrintf(&local_c4,1,param_1);
    pcVar4 = acStack_7c;
    pageDynParaPrintf(&local_c4,2,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "macWhitelist");
    iVar2 = 0;
    do {
      swMac2Str(piVar10,pcVar4,0);
      piVar10 = (int *)(piVar10 + 6);
      pageParaSet(local_a4,pcVar4,iVar2);
      iVar8 = iVar2 + 1;
      pageDynParaPrintf(local_a4,iVar2,param_1);
      pcVar4 = pcVar4 + 18;
      iVar2 = iVar8;
    } while (iVar8 != 4);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar2 = httpRpmFsA(param_1,"/userRpm/LocalManageControlRpm.htm");
    if (iVar2 == 2) {
      return 2;
    }
    uVar5 = 10;
    puVar6 = 0;
LAB_0044c1f8:
    sVar3 = HttpErrorPage(param_1,uVar5,puVar6,0);
  }
  return sVar3;
}

