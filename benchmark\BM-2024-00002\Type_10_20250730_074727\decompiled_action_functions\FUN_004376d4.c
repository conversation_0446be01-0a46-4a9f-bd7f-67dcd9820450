
/* WARNING: Type propagation algorithm not settling */

undefined4 FUN_004376d4(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  int iVar3;
  undefined4 uVar4;
  char *pcVar5;
  int local_1c [5];
  
  bVar1 = true;
  local_1c[1] = 0;
  local_1c[2] = 0;
  local_1c[3] = 0;
  local_1c[4] = 0;
  iVar2 = mxmlLoadString(0,param_1,0);
  if (iVar2 == 0) {
    return 0;
  }
  iVar3 = mxmlFindElement(iVar2,iVar2,"soap:Envelope",0,0,1);
  if ((iVar3 != 0) && (iVar3 = mxmlFindElement(iVar3,iVar2,"SetFirewallSettings",0,0,1), iVar3 != 0)
     ) {
    uVar4 = mxmlFindElement(iVar3,iVar2,"SPIIPv4",0,0,1);
    pcVar5 = mxmlGetText(uVar4,0);
    if (pcVar5 != 0) {
      iVar3 = strcmp(pcVar5,"true");
      if (iVar3 == 0) {
        local_1c[0] = 1;
      }
      else {
        iVar3 = strcmp(pcVar5,"false");
        if (iVar3 != 0) {
          bVar1 = false;
          goto LAB_00437d90;
        }
        local_1c[0] = 0;
      }
      apmib_set(0x1b59,local_1c);
    }
    uVar4 = mxmlFindElement(uVar4,iVar2,"AntiSpoof",0,0,1);
    pcVar5 = mxmlGetText(uVar4,0);
    if (pcVar5 != 0) {
      iVar3 = strcmp(pcVar5,"true");
      if (iVar3 == 0) {
        local_1c[0] = 0x87ffff;
      }
      else {
        iVar3 = strcmp(pcVar5,"false");
        if (iVar3 != 0) {
          bVar1 = false;
          goto LAB_00437d90;
        }
        local_1c[0] = 0;
      }
      apmib_set(229,local_1c);
      if (0 < local_1c[0]) {
        local_1c[0] = "2";
        apmib_set(230,local_1c);
        apmib_set(231,local_1c);
        apmib_set(232,local_1c);
        apmib_set(233,local_1c);
        apmib_set(234,local_1c);
        apmib_set(235,local_1c);
        apmib_set(236,local_1c);
        apmib_set(237,local_1c);
      }
    }
    uVar4 = mxmlFindElement(uVar4,iVar2,"WANPing",0,0,1);
    pcVar5 = mxmlGetText(uVar4,0);
    if (pcVar5 != 0) {
      iVar3 = strcmp(pcVar5,"true");
      if (iVar3 == 0) {
        local_1c[0] = 1;
      }
      else {
        iVar3 = strcmp(pcVar5,"false");
        if (iVar3 != 0) {
          bVar1 = false;
          goto LAB_00437d90;
        }
        local_1c[0] = 0;
      }
      apmib_set(195,local_1c);
    }
    uVar4 = mxmlFindElement(uVar4,iVar2,"ALGPPTP",0,0,1);
    pcVar5 = mxmlGetText(uVar4,0);
    if (pcVar5 != 0) {
      iVar3 = strcmp(pcVar5,"true");
      if (iVar3 == 0) {
        local_1c[0] = 1;
      }
      else {
        iVar3 = strcmp(pcVar5,"false");
        if (iVar3 != 0) {
          bVar1 = false;
          goto LAB_00437d90;
        }
        local_1c[0] = 0;
      }
      apmib_set(246,local_1c);
    }
    uVar4 = mxmlFindElement(uVar4,iVar2,"ALGIPSec",0,0,1);
    pcVar5 = mxmlGetText(uVar4,0);
    if (pcVar5 != 0) {
      iVar3 = strcmp(pcVar5,"true");
      if (iVar3 == 0) {
        local_1c[0] = 1;
      }
      else {
        iVar3 = strcmp(pcVar5,"false");
        if (iVar3 != 0) {
          bVar1 = false;
          goto LAB_00437d90;
        }
        local_1c[0] = 0;
      }
      apmib_set(245,local_1c);
    }
    uVar4 = mxmlFindElement(uVar4,iVar2,"ALGRTSP",0,0,1);
    pcVar5 = mxmlGetText(uVar4,0);
    if (pcVar5 != 0) {
      iVar3 = strcmp(pcVar5,"true");
      if (iVar3 == 0) {
        local_1c[0] = 1;
      }
      else {
        iVar3 = strcmp(pcVar5,"false");
        if (iVar3 != 0) {
          bVar1 = false;
          goto LAB_00437d90;
        }
        local_1c[0] = 0;
      }
    }
    uVar4 = mxmlFindElement(uVar4,iVar2,"ALGRSPI",0,0,1);
    pcVar5 = mxmlGetText(uVar4,0);
    if (pcVar5 != 0) {
      iVar3 = strcmp(pcVar5,"true");
      if (iVar3 == 0) {
        local_1c[0] = 1;
      }
      else {
        iVar3 = strcmp(pcVar5,"false");
        if (iVar3 == 0) {
          local_1c[0] = 0;
        }
        else {
          bVar1 = false;
        }
      }
    }
  }
LAB_00437d90:
  mxmlDelete(iVar2);
  if (("" == 0) || (!bVar1)) {
    if (bVar1) {
      memcpy(local_1c + 1,"O",3);
      apmib_update(4);
      FUN_00421468("firewall.sh",0,0);
    }
    else {
      memcpy(local_1c + 1,"ERROR",6);
    }
    FUN_004260e0("SetFirewallSettings",local_1c + 1);
  }
  return 0;
}

