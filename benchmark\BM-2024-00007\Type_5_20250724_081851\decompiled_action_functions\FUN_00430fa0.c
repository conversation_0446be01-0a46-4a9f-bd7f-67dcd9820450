
int FUN_00430fa0(undefined4 param_1)

{
  int iVar1;
  short sVar4;
  char *pcVar2;
  uint uVar3;
  undefined1 *puVar5;
  int iVar6;
  uint uVar7;
  uint local_410;
  undefined auStack_40c [8];
  undefined auStack_404 [16];
  in_addr_t local_3f4;
  in_addr_t local_3f0;
  in_addr_t local_3ec;
  in_addr_t local_3e8;
  in_addr_t local_3e4;
  undefined4 local_3e0;
  uint local_3dc;
  uint local_3d8;
  uint local_3d4;
  undefined auStack_3d0 [64];
  undefined *local_390;
  undefined4 local_38c;
  undefined *local_388;
  undefined4 local_384;
  undefined *local_380;
  undefined4 local_37c;
  undefined *local_378;
  undefined4 local_374;
  undefined *local_370;
  undefined4 local_36c;
  undefined *local_368;
  undefined4 local_364;
  undefined *local_360;
  undefined4 local_35c;
  undefined *local_358;
  undefined4 local_354;
  undefined *local_350;
  undefined4 local_34c;
  undefined *local_348;
  undefined4 local_344;
  undefined *local_340;
  undefined4 local_33c;
  undefined *local_338;
  undefined4 local_334;
  undefined *local_330;
  undefined4 local_32c;
  undefined *local_328;
  undefined4 local_324;
  undefined *local_320;
  undefined4 local_31c;
  undefined *local_318;
  undefined4 local_314;
  undefined *local_310;
  undefined4 local_30c;
  undefined *local_308;
  undefined4 local_304;
  undefined *local_300;
  undefined4 local_2fc;
  undefined *local_2f8;
  undefined4 local_2f4;
  undefined *local_2f0;
  undefined4 local_2ec;
  undefined4 local_2e8;
  undefined auStack_2e0 [4];
  undefined auStack_2dc [4];
  undefined auStack_2d8 [4];
  undefined auStack_2d4 [4];
  undefined auStack_2d0 [4];
  undefined auStack_2cc [4];
  undefined auStack_2c8 [4];
  undefined auStack_2c4 [256];
  undefined auStack_1c4 [256];
  undefined auStack_c4 [4];
  undefined auStack_c0 [4];
  undefined auStack_bc [64];
  undefined auStack_7c [16];
  undefined auStack_6c [16];
  undefined auStack_5c [16];
  undefined auStack_4c [4];
  undefined auStack_48 [16];
  undefined auStack_38 [16];
  undefined auStack_28 [4];
  undefined auStack_24 [4];
  undefined auStack_20 [8];
  
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar4 = HttpDenyPage(param_1);
    goto LAB_00431954;
  }
  pcVar2 = httpGetEnv(param_1,"wan");
  uVar7 = 0;
  if (((pcVar2 != 0) && (uVar7 = atoi(pcVar2), uVar7 < 0)) ||
     (uVar3 = getMaxWanPortNumber(), uVar3 <= uVar7)) {
    uVar7 = 0;
  }
  local_390 = auStack_2e0;
  local_388 = auStack_2dc;
  local_380 = auStack_2d8;
  local_378 = auStack_2d4;
  local_370 = auStack_2d0;
  local_368 = auStack_2cc;
  local_360 = auStack_2c8;
  local_358 = auStack_2c4;
  local_350 = auStack_1c4;
  local_348 = auStack_c4;
  local_340 = auStack_c0;
  local_338 = auStack_bc;
  local_330 = auStack_7c;
  local_328 = auStack_6c;
  local_320 = auStack_5c;
  local_318 = auStack_4c;
  local_310 = auStack_48;
  local_308 = auStack_38;
  local_300 = auStack_28;
  local_2f8 = auStack_24;
  local_2f0 = auStack_20;
  local_354 = "@";
  local_34c = "@";
  local_2e8 = 0;
  local_38c = 0;
  local_384 = 0;
  local_37c = 0;
  local_374 = 0;
  local_36c = 0;
  local_364 = 0;
  local_35c = 0;
  local_344 = 0;
  local_33c = 0;
  local_334 = "@";
  local_304 = 16;
  local_32c = 16;
  local_324 = 16;
  local_31c = 16;
  local_30c = 16;
  local_314 = 0;
  local_2fc = 0;
  local_2f4 = 0;
  local_2ec = 0;
  memset(&local_3f4,0,"$");
  iVar1 = httpGetEnv(param_1,"Save");
  if (iVar1 == 0) {
LAB_004314d8:
    swGetStaticIpCfg(uVar7,&local_3f4);
    local_410 = 0;
    pageParaSet(&local_390,&local_410,2);
    pageParaSet(&local_390,&local_410,3);
    pageParaSet(&local_390,&local_410,4);
    pageParaSet(&local_390,&local_410,9);
    pageParaSet(&local_390,&local_410,10);
    local_410 = 0;
    pageParaSet(&local_390,&local_410,1);
    local_410 = uVar7;
    pageParaSet(&local_390,&local_410,5);
    local_410 = local_3d8;
    pageParaSet(&local_390,&local_410,18);
    local_410 = local_3d4;
    pageParaSet(&local_390,&local_410,19);
    memset(auStack_3d0,0,"@");
    pageParaSet(&local_390,auStack_3d0,7);
    pageParaSet(&local_390,auStack_3d0,8);
    pageParaSet(&local_390,auStack_3d0,11);
    local_410 = getMaxWanPortNumber();
    pageParaSet(&local_390,&local_410,0);
    local_410 = 1;
    pageParaSet(&local_390,&local_410,6);
    swIpAddr2Str(local_3f4,auStack_404);
    pageParaSet(&local_390,auStack_404,12);
    swIpAddr2Str(local_3f0,auStack_404);
    pageParaSet(&local_390,auStack_404,13);
    swIpAddr2Str(local_3ec,auStack_404);
    pageParaSet(&local_390,auStack_404,14);
    local_410 = local_3dc;
    pageParaSet(&local_390,&local_410,15);
    swIpAddr2Str(local_3e8,auStack_404);
    pageParaSet(&local_390,auStack_404,16);
    swIpAddr2Str(local_3e4,auStack_404);
    pageParaSet(&local_390,auStack_404,17);
    swGetSystemMode(auStack_40c);
    local_410 = 3;
    pageParaSet(&local_390,&local_410,20);
    httpGetDetectedWanType(uVar7);
    iVar1 = httpGetEnv(param_1,"detect");
    if (iVar1 != 0) {
      httpOnWanTypeDetect(uVar7);
    }
    httpPrintfWanTypeDetectInfo(param_1);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "staticIpInf");
    iVar1 = 0;
    do {
      iVar6 = iVar1 + 1;
      pageDynParaPrintf(&local_390,iVar1,param_1);
      iVar1 = iVar6;
    } while (iVar6 != 21);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintfWanTypeInfo(param_1);
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/WanStaticIpCfgRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    iVar1 = 10;
    puVar5 = 0;
  }
  else {
    pcVar2 = httpGetEnv(param_1,"i");
    if (pcVar2 == 0) {
LAB_00431234:
      pcVar2 = httpGetEnv(param_1,"mask");
      if (pcVar2 != 0) {
        iVar1 = swChkDotIpAddr(pcVar2);
        if (iVar1 == 0) {
          puVar5 = "";
          iVar1 = 0x138c;
          goto LAB_0043194c;
        }
        local_3f0 = inet_addr(pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"gateway");
      if (pcVar2 != 0) {
        iVar1 = swChkDotIpAddr(pcVar2);
        if (iVar1 == 0) {
          puVar5 = "";
          iVar1 = 0x138f;
          goto LAB_0043194c;
        }
        local_3ec = inet_addr(pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"mtu");
      if (pcVar2 != 0) {
        local_3dc = atoi(pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"dnsserver");
      if (pcVar2 != 0) {
        iVar1 = swChkDotIpAddr(pcVar2);
        if (iVar1 == 0) {
          puVar5 = "";
          iVar1 = 0x138d;
          goto LAB_0043194c;
        }
        local_3e8 = inet_addr(pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"dnsserver2");
      if (pcVar2 != 0) {
        iVar1 = swChkDotIpAddr(pcVar2);
        if (iVar1 == 0) {
          puVar5 = "";
          iVar1 = 0x138e;
          goto LAB_0043194c;
        }
        local_3e4 = inet_addr(pcVar2);
      }
      local_3e0 = 0;
      pcVar2 = httpGetEnv(param_1,"downBandwidth");
      if (pcVar2 != 0) {
        local_3d8 = atoi(pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"upBandwidth");
      if (pcVar2 != 0) {
        local_3d4 = atoi(pcVar2);
      }
      sVar4 = swChkStaticIpCfg(uVar7,&local_3f4);
      iVar1 = sVar4;
      if (iVar1 == 0) {
        swSetStaticIpCfg(uVar7,&local_3f4,1);
        goto LAB_004314d8;
      }
      puVar5 = "";
    }
    else {
      iVar1 = swChkDotIpAddr(pcVar2);
      if (iVar1 != 0) {
        local_3f4 = inet_addr(pcVar2);
        goto LAB_00431234;
      }
      puVar5 = "";
      iVar1 = 0x138b;
    }
  }
LAB_0043194c:
  sVar4 = HttpErrorPage(param_1,iVar1,puVar5,0);
LAB_00431954:
  return sVar4;
}

