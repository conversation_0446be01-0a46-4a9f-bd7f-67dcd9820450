
undefined4 FUN_0045b32c(int param_1)

{
  undefined4 uVar1;
  int iVar2;
  int iVar3;
  void *__ptr;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetAdvNetworkSettings");
    uVar1 = 0;
  }
  else {
    iVar2 = mxmlNewXML("1.0");
    if (iVar2 == 0) {
      puts("xml=NULL");
      uVar1 = 0;
    }
    else {
      iVar3 = mxmlNewElement(iVar2,"soap:Envelope");
      if (iVar3 == 0) {
        mxmlDelete(iVar2);
        puts("soap_env=NULL");
        uVar1 = 0;
      }
      else {
        mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar3 = mxmlNewElement(iVar3,"soap:Body");
        if (iVar3 == 0) {
          mxmlDelete(iVar2);
          puts("body=NULL");
          uVar1 = 0;
        }
        else {
          iVar3 = mxmlNewElement(iVar3,"SetAdvNetworkSettingsResponse");
          if (iVar3 == 0) {
            mxmlDelete(iVar2);
            puts("SetAdvNetworkSettingsResponse_xml=NULL");
            uVar1 = 0;
          }
          else {
            mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
            iVar3 = mxmlNewElement(iVar3,"SetAdvNetworkSettingsResult");
            if (iVar3 == 0) {
              mxmlDelete(iVar2);
              puts("SetAdvNetworkSettingsResult_xml=NULL");
              uVar1 = 0;
            }
            else {
              mxmlNewText(iVar3,0,"O");
              if ("" == 0) {
                apmib_update(4);
                __ptr = mxmlSaveAllocString(iVar2,0);
                if (__ptr == 0) {
                  puts("retstring=NULL");
                }
                else {
                  FUN_0041ed70("",200,__ptr,"");
                  free(__ptr);
                }
                uVar1 = mxmlDelete(iVar2);
              }
              else {
                uVar1 = mxmlDelete(iVar2);
              }
            }
          }
        }
      }
    }
  }
  return uVar1;
}

