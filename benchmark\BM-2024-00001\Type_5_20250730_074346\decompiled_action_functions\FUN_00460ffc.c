
/* WARNING: Removing unreachable block (ram,FUN_00460ffc) */
/* WARNING: Type propagation algorithm not settling */

int FUN_00460ffc(undefined4 param_1)

{
  int iVar1;
  short sVar10;
  undefined4 uVar2;
  uint32_t uVar3;
  uint32_t uVar4;
  uint32_t uVar5;
  uint32_t uVar6;
  int iVar7;
  char *pcVar8;
  size_t __n;
  uint uVar9;
  byte *pbVar11;
  uint uStack_c98;
  uint uStack_c94;
  uint auStack_c90 [2];
  uint32_t auStack_c88 [4];
  char acStack_c78 [16];
  char acStack_c68 [20];
  char acStack_c54 [20];
  undefined auStack_c40 [28];
  char acStack_c24 [36];
  char acStack_c00 [36];
  uint uStack_bdc;
  int iStack_bd8;
  int iStack_bd4;
  int aiStack_bd0 [5];
  undefined uStack_bbc;
  byte bStack_bbb;
  byte abStack_b8e [2902];
  uint uStack_38;
  undefined *puStack_34;
  uint *puStack_30;
  
  uStack_c98 = 0;
  uStack_c94 = 0;
  auStack_c90[0] = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar10 = HttpDenyPage(param_1);
    goto LAB_00460fbc;
  }
  memset(&uStack_bbc,0,0xb82);
  iVar1 = getProductId();
  if ((iVar1 == 0x9010004) || (iVar1 = getProductId(), iVar1 == 0x7010002)) {
    iVar1 = httpGetEnv(param_1,"getWdsResult");
    if (iVar1 != 0) {
      swWlanWDSScan(0,&uStack_bbc,0);
      goto LAB_0045fb00;
    }
    memcpy(auStack_c40,"WzdWlanSiteSurveyRpm_AP.htm",28);
    auStack_c90[1] = 25;
    uVar2 = httpGetEnv(param_1,"QUERY_STRING");
    swGetLanCfg(auStack_c88);
    uVar3 = ntohl(auStack_c88[0]);
    uVar4 = ntohl(auStack_c88[0]);
    uVar5 = ntohl(auStack_c88[0]);
    uVar6 = ntohl(auStack_c88[0]);
    sprintf(acStack_c78,"%d.%d.%d.%d",uVar3 >> 24,uVar4 >> 16 & 255,(int)(uVar5 & -256) >> 8,
            uVar6 & 255);
    swWlanWDSScan(0,&uStack_bbc,1);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "waitWdsInf");
    writePageParamSet(param_1,""%s",",auStack_c40,0);
    writePageParamSet(param_1,""%s",",uVar2,1);
    writePageParamSet(param_1,"%d,",auStack_c90 + 1,2);
    writePageParamSet(param_1,""%s",",acStack_c78,3);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    pcVar8 = "/userRpm/WaitForWdsScanResult.htm";
  }
  else {
    swWlanActivateScan(0,&uStack_bbc);
LAB_0045fb00:
    uStack_38 = bStack_bbb;
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "siteSurveyPara");
    httpPrintf(param_1,"%d,%d,",2,uStack_38);
    iVar1 = httpGetEnv(param_1,"iMAC");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"iSSID");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"iWdsChan");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"modeSelecte");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"indexOfBssid");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"wdsStatus");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "wzdSurvey");
    iVar1 = httpGetEnv(param_1,"ssid_client");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"mac_client");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"rptSsid");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"rptBssid");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"mptBssid1");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"mptBssid2");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"mptBssid3");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"mptBssid4");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"channel");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"wds_enable");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"Region");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"uni_wds_r");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"secType");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"wepSecret");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"wepSecOpt");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"keytype");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"keynum");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"key1");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"key2");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"key3");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"key4");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"length1");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"length2");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"length3");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"length4");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"pskSecOpt");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"pskCipher");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"pskSecret");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    iVar1 = httpGetEnv(param_1,"interval");
    if (iVar1 == 0) {
      httpPrintf(param_1,""",");
    }
    else {
      httpPrintf(param_1,""%s",",iVar1);
    }
    pbVar11 = abStack_b8e;
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "siteList");
    puStack_34 = &uStack_bbc;
    puStack_30 = &uStack_c98;
    for (iVar1 = 0; iVar1 < uStack_38; iVar1 = iVar1 + 1) {
      sprintf(acStack_c68,"%02X-%02X-%02X-%02X-%02X-%02X",pbVar11[-43],pbVar11[-42],
              pbVar11[-41],pbVar11[-40],pbVar11[-39],pbVar11[-38]);
      strncpy(acStack_c54,acStack_c68,18);
      writePageParamSet(param_1,""%s",",acStack_c54,0);
      strncpy(acStack_c24,puStack_34 + iVar1 * "8","!");
      writePageParamSet(param_1,""%s",",acStack_c24,1);
      uStack_c98 = pbVar11[-3];
      writePageParamSet(param_1,"%d,",puStack_30,2);
      iVar7 = getProductId();
      if (((((iVar7 == 0x9010004) || (iVar7 = getProductId(), iVar7 == 0x8010003)) ||
           (iVar7 = getProductId(), iVar7 == 0x8430002)) ||
          ((iVar7 = getProductId(), iVar7 == 0x8100001 ||
           (iVar7 = getProductId(), iVar7 == 0x8100002)))) ||
         ((iVar7 = getProductId(), iVar7 == 0x8100001 ||
          ((iVar7 = getProductId(), iVar7 == 0x8100002 ||
           (iVar7 = getProductId(), iVar7 == 0x8020002)))))) {
        uStack_c94 = (int)(*(ushort *)(pbVar11 + -2) - 0x967) / 5;
        if (14 < uStack_c94) {
          
        }
      }
      else {
        uStack_c94 = (uint)*(ushort *)(pbVar11 + -2);
      }
      writePageParamSet(param_1,"%d,",&uStack_c94,3);
      auStack_c90[0] = (uint)*pbVar11;
      writePageParamSet(param_1,"%d,",auStack_c90,4);
      pbVar11 = pbVar11 + ".";
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    memset(acStack_c00,0,"D");
    pcVar8 = httpGetEnv(param_1,"ssid");
    if (pcVar8 == 0) {
      acStack_c00[0] = '\0';
    }
    else {
      __n = strlen(pcVar8);
      strncpy(acStack_c00,pcVar8,__n);
    }
    pcVar8 = httpGetEnv(param_1,"curRegion");
    if (pcVar8 == 0) {
      
    }
    else {
      uVar9 = atoi(pcVar8);
      if (uVar9 < "l") {
        uStack_bdc = uVar9;
      }
    }
    pcVar8 = httpGetEnv(param_1,"channel");
    if (pcVar8 == 0) {
      iStack_bd8 = 6;
    }
    else {
      iVar1 = atoi(pcVar8);
      if (iVar1 - 1U < 15) {
        iStack_bd8 = iVar1;
      }
    }
    pcVar8 = httpGetEnv(param_1,"chanWidth");
    if (pcVar8 == 0) {
      iStack_bd4 = 2;
    }
    else {
      iVar1 = atoi(pcVar8);
      if (iVar1 - 1U < 3) {
        iStack_bd4 = iVar1;
      }
    }
    pcVar8 = httpGetEnv(param_1,"mode");
    if (pcVar8 == 0) {
      aiStack_bd0[0] = 1;
    }
    else {
      iVar1 = atoi(pcVar8);
      if (iVar1 - 1U < 7) {
        aiStack_bd0[0] = iVar1;
      }
    }
    pcVar8 = httpGetEnv(param_1,"wrr");
    if (pcVar8 != 0) {
      iVar1 = strcmp(pcVar8,"true");
      if ((iVar1 == 0) || (iVar1 = atoi(pcVar8), iVar1 == 1)) {
        aiStack_bd0[1] = 1;
      }
      else {
        aiStack_bd0[1] = 0;
      }
    }
    pcVar8 = httpGetEnv(param_1,"s");
    if (pcVar8 != 0) {
      iVar1 = strcmp(pcVar8,"true");
      if ((iVar1 == 0) || (iVar1 = atoi(pcVar8), iVar1 == 1)) {
        aiStack_bd0[2] = 1;
      }
      else {
        aiStack_bd0[2] = 0;
      }
    }
    pcVar8 = httpGetEnv(param_1,"select");
    if (pcVar8 != 0) {
      iVar1 = strcmp(pcVar8,"true");
      if ((iVar1 == 0) || (iVar1 = atoi(pcVar8), iVar1 == 1)) {
        aiStack_bd0[3] = 1;
      }
      else {
        aiStack_bd0[3] = 0;
      }
    }
    pcVar8 = httpGetEnv(param_1,"rate");
    if (pcVar8 != 0) {
      aiStack_bd0[4] = atoi(pcVar8);
    }
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "pagePara");
    writePageParamSet(param_1,""%s",",acStack_c00,0);
    writePageParamSet(param_1,"%d,",&uStack_bdc,1);
    writePageParamSet(param_1,"%d,",&iStack_bd8,2);
    writePageParamSet(param_1,"%d,",&iStack_bd4,3);
    writePageParamSet(param_1,"%d,",aiStack_bd0,4);
    writePageParamSet(param_1,"%d,",aiStack_bd0 + 1,5);
    writePageParamSet(param_1,"%d,",aiStack_bd0 + 2,6);
    writePageParamSet(param_1,"%d,",aiStack_bd0 + 3,7);
    writePageParamSet(param_1,"%d,",aiStack_bd0 + 4,8);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,"<script language=JavaScript>\nvar isInScanning = 0;\n</script>");
    HttpWebV4Head(param_1,0,0);
    pcVar8 = "/userRpm/WzdWlanSiteSurveyRpm_AP.htm";
  }
  iVar1 = httpRpmFsA(param_1,pcVar8);
  if (iVar1 == 2) {
    return 2;
  }
  sVar10 = HttpErrorPage(param_1,10,0,0);
LAB_00460fbc:
  return sVar10;
}

