POST /HNAP1/ HTTP/1.1
Host: **********
User-Agent: Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/110.0
Accept: application/json
Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2
Accept-Encoding: gzip, deflate
Content-Type: application/json
SOAPACTION: "http://purenetworks.com/HNAP1/GetMultipleHNAPs"
HNAP_AUTH: E5C14446F7156A0DE9E56D8ED83DAA45 1677385974303
Content-Length: 2643
Origin: http://**********
Connection: keep-alive
Referer: http://**********/Network.html?t=1677385907815
Cookie: PHPSESSID=03a12b4b0d9ace632e47b3bb41124651; timeout=0

{"GetMultipleHNAPs": {"act_val": "", "lan(0)_ipaddr": "", "wan_wan(0)_hwaddr": "", "wan_wan(0)_pppoe_type": "normal", "wl(0).channel": "", "wl(1).channel": "", "wl(0).(0)_enable": "0", "wl(0).(0)_crypto": "", "wl(0).(0)_ssid": "", "wl(1).(0)_enable": "0", "wl(1).(0)_crypto": "", "wl(1).(0)_ssid": "", "wl(0).(1)_enable": "1", "wl(0).(1)_crypto": "", "wl(0).(1)_ssid": "", "wl(1).(1)_enable": "1", "wl(1).(1)_crypto": "", "wl(1).(1)_ssid": "", "wl(0)_tx_power": "", "wl(0)_bandwidth": "", "wl(1)_bandwidth": "", "wl(1)_tx_power": "", "software_version": "", "product_name": "", "serial_number": "", "up_time": "0", "lan(0)_mac": "", "wan_wan(0)_proto": "", "wan_wan(0)_gateway": "", "wan_wan(0)_netmask": "", "wan_wan(0)_dns": "", "wan_wan(0)_dns_manual": "0", "wan_wan(0)_ipaddr": "", "wan_wan(0)_pppoe_status": "", "current_firmware_version": "", "portforward_rule": "", "lan(0)_netmask": "", "lan(0)_dhcps_enable": "1", "lan(0)_dhcps_start": "", "lan(0)_dhcps_end": "", "lan(0)_dhcps_lease": "", "dmz_enable": "0", "dmz_ipaddr": "", "firewall_spi_enable": "", "antiattack_antidos_enable": "", "acl_wan_ping_enable": "", "wl(0).(0)_maclist": "", "wl(0).(0)_maclist_mode": "off", "wl(0).(0)_lasttime_maclist_mode": "", "wl(0).(0)_associated_sta_info": "", "wl(0).(0)_back_sta_info": "", "staticroute_list": "", "wl(0)_guest(0)_ipaddr": "", "wl(0)_guest(0)_netmask": "", "wl(0)_guest(0)_ssid": "", "wl(0)_guest(1)_ssid": "", "lan(0)_dhcps_staticlist": "", "lan(0)_dhcps_clientinfo": "", "stad_info": "", "current_client_mac": "", "wan_wan(0)_enable": "1", "wan_wan(0)_pppoe_username": "", "wan_wan(0)_pppoe_passwd": "", "wan_wan(0)_pppoe_service": "", "wan_wan(0)_clone_mac": "", "wan_wan(0)_mac_clone_enable": "0", "wan_wan(0)_mtu": "", "wan_wan_speed": "Auto", "wan_wan_duplex": "Auto", "online_client_number": "0", "network_connect_status": "0", "qrurl": "", "wan_status": "0", "wl(0).(0)_preshared_key": "", "wl(1).(0)_preshared_key": "", "wl(0)_bandsteering": "0", "wl(1)_bandsteering": "0", "wl(0).(1)_local_access": "", "wl(0).(1)_local_access_timeout": "", "wl(0).(1)_preshared_key": "", "wl(1).(1)_preshared_key": "", "wire_sta_list": "", "wireless_sta_2g_list": "", "wireless_sta_2g_guest_list": "", "wireless_sta_5g_list": "", "wireless_sta_5g_guest_list": "", "offline_sta_list": "", "wireless_maclist_mode": "off", "wireless_maclist": "", "parent_control_rule": "", "smartqos_enable": "", "smartqos_downstream_shapingrate": "", "smartqos_upstream_shapingrate": "", "smartqos_type": "", "smartqos_mode": "", "smartqos_priority_devices": "", "smartqos_express_devices": "", "smartqos_normal_devices": "", "wan_connect_status": "0"}}