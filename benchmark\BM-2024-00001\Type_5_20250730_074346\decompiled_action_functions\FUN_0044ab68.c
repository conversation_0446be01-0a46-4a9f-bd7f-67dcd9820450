
/* WARNING: Type propagation algorithm not settling */

int FUN_0044ab68(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  char *pcVar3;
  ulong uVar4;
  in_addr_t iVar5;
  char *pcVar6;
  char *pcVar7;
  char *__nptr;
  int iVar8;
  undefined4 uVar9;
  int iVar10;
  short sVar11;
  char *pcVar12;
  int iVar13;
  undefined auStack_1d8 [4];
  undefined auStack_1d4 [4];
  uint local_1d0;
  int local_1cc;
  int local_1c8;
  int local_1c4 [3];
  undefined auStack_1b8 [16];
  int local_1a8;
  int local_1a4;
  int local_1a0;
  int local_19c;
  undefined auStack_198 [32];
  ulong local_178;
  undefined4 local_174;
  undefined4 local_170;
  undefined4 local_16c;
  in_addr_t local_168;
  in_addr_t local_164;
  undefined4 local_160;
  uint local_15c;
  int *local_158;
  undefined4 local_154;
  int *local_150;
  undefined4 local_14c;
  int *local_148;
  undefined4 local_144;
  int *local_140;
  undefined4 local_13c;
  int *local_138;
  undefined4 local_134;
  uint *local_130;
  undefined4 local_12c;
  undefined4 local_128;
  char local_120 [64];
  ulong *local_e0;
  undefined4 local_dc;
  char *local_d8;
  undefined4 local_d4;
  undefined4 *local_d0;
  undefined4 local_cc;
  int *local_c8;
  undefined4 local_c4;
  int *local_c0;
  undefined4 local_bc;
  int *local_b8;
  undefined4 local_b4;
  int *local_b0;
  undefined4 local_ac;
  undefined4 *local_a8;
  undefined4 local_a4;
  undefined4 local_a0;
  ulong local_98;
  char acStack_94 [64];
  undefined4 local_54;
  int local_50;
  int local_4c;
  int local_48;
  int local_44;
  undefined4 local_40 [2];
  ulong *local_38;
  int *local_34;
  char *local_30;
  ulong **local_2c;
  
  iVar2 = httpGetEnv(param_1,"DelAll");
  if (iVar2 != 0) {
    swDelQoSRuleAll(0,1);
  }
  pcVar3 = httpGetEnv(param_1,"Del");
  if (pcVar3 != 0) {
    uVar4 = strtoul(pcVar3,(char **)0,10);
    iVar2 = swDelQoSRule(uVar4,1);
    if (iVar2 != 0) goto LAB_0044b694;
  }
  pcVar3 = httpGetEnv(param_1,"enable");
  if (pcVar3 != 0) {
    iVar2 = atoi(pcVar3);
    pcVar3 = httpGetEnv(param_1,"enableId");
    if (pcVar3 != 0) {
      uVar4 = strtoul(pcVar3,(char **)0,10);
      iVar2 = swSetQoSRuleStatus(uVar4,iVar2 == 1,1);
      if (iVar2 != 0) goto LAB_0044b694;
    }
  }
  pcVar3 = httpGetEnv(param_1,"curEditId");
  if (pcVar3 != 0) {
    memset(&local_178,0," ");
    uVar4 = strtoul(pcVar3,(char **)0,10);
    local_178 = uVar4;
    pcVar3 = httpGetEnv(param_1,"enable");
    if ((pcVar3 == 0) || (iVar2 = strcmp(pcVar3,"true"), iVar2 != 0)) {
      local_174 = 0;
    }
    else {
      local_174 = 1;
    }
    local_170 = 0;
    pcVar3 = httpGetEnv(param_1,"start_ip_addr");
    if (pcVar3 != 0) {
      iVar5 = inet_addr(pcVar3);
      iVar2 = swChkSameLanSubnet(iVar5);
      if (iVar2 != 0) goto LAB_0044adb8;
LAB_0044ae18:
      iVar2 = 0x65fa;
      goto LAB_0044b694;
    }
LAB_0044adb8:
    pcVar6 = httpGetEnv(param_1,"end_ip_addr");
    pcVar7 = pcVar6;
    if ((pcVar6 != 0) || (pcVar7 = pcVar3, pcVar3 != 0)) {
      iVar5 = inet_addr(pcVar7);
      iVar2 = swChkSameLanSubnet(iVar5);
      pcVar6 = pcVar7;
      if (iVar2 == 0) goto LAB_0044ae18;
    }
    pcVar7 = httpGetEnv(param_1,"start_port");
    __nptr = httpGetEnv(param_1,"end_port");
    if (__nptr == 0) {
      __nptr = pcVar7;
    }
    pcVar12 = pcVar3;
    if (pcVar3 == 0) {
      pcVar12 = pcVar6;
      if (pcVar6 != 0) goto LAB_0044aeac;
      if ((pcVar7 == 0) && (pcVar7 = __nptr, pcVar12 = pcVar3, __nptr == 0))
      goto LAB_0044b11c;
    }
    else {
      if (pcVar6 == 0) {
        pcVar6 = pcVar3;
      }
LAB_0044aeac:
      if (pcVar7 == 0) {
        pcVar7 = __nptr;
      }
    }
    if (__nptr == 0) {
      __nptr = pcVar7;
    }
    local_15c = 0;
    local_168 = 0;
    local_164 = 0;
    local_160 = 0;
    if (pcVar12 != 0) {
      local_168 = inet_addr(pcVar12);
      local_164 = inet_addr(pcVar6);
    }
    if (pcVar7 != 0) {
      iVar2 = atoi(pcVar7);
      local_160._0_2_ = iVar2;
      iVar2 = atoi(__nptr);
      local_160 = CONCAT22(local_160._0_2_,iVar2);
    }
    pcVar3 = httpGetEnv(param_1,"protocol");
    iVar2 = atoi(pcVar3);
    if ((iVar2 == 1) || (iVar2 == 2)) {
      local_15c = CONCAT13(iVar2,local_15c._1_3_);
    }
    else {
      local_15c = local_15c & 0xffffff;
    }
    local_1a0 = 0;
    local_19c = 0;
    local_1a8 = 0;
    pcVar3 = httpGetEnv(param_1,"min_up_band_width");
    if (pcVar3 != 0) {
      local_1a0 = atoi(pcVar3);
    }
    pcVar3 = httpGetEnv(param_1,"max_up_band_width");
    if (pcVar3 != 0) {
      local_19c = atoi(pcVar3);
    }
    pcVar3 = httpGetEnv(param_1,"min_down_band_width");
    if (pcVar3 != 0) {
      local_1a8 = atoi(pcVar3);
    }
    pcVar3 = httpGetEnv(param_1,"max_down_band_width");
    if (pcVar3 != 0) {
      local_1a4 = atoi(pcVar3);
    }
    swGetQoSCfg(auStack_1d8,auStack_1d4,auStack_1b8,1);
    local_16c = 0;
    iVar2 = swCheckQosBw(&local_1a8,uVar4);
    if ((iVar2 != 0) || (iVar2 = swSetQoSRule(&local_178,&local_1a8,1,uVar4 != 0,1), iVar2 != 0))
    goto LAB_0044b694;
  }
LAB_0044b11c:
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  HttpWebV4Head(param_1,0,1);
  local_e0 = &local_98;
  local_d8 = acStack_94;
  local_d0 = &local_54;
  local_c8 = &local_50;
  local_c0 = &local_4c;
  local_b8 = &local_48;
  local_b0 = &local_44;
  local_a8 = local_40;
  local_d4 = "@";
  local_158 = &local_1cc;
  local_150 = &local_1c8;
  local_148 = local_1c4;
  local_140 = local_1c4 + 1;
  local_138 = local_1c4 + 2;
  local_130 = &local_1d0;
  local_1c8 = 1;
  local_1c4[2] = 8;
  local_1cc = 1;
  local_1c4[1] = 8;
  local_1c4[0] = 0;
  local_a0 = 0;
  local_dc = 0;
  local_cc = 0;
  local_c4 = 0;
  local_bc = 0;
  local_b4 = 0;
  local_ac = 0;
  local_a4 = 0;
  local_128 = 0;
  local_154 = 0;
  local_14c = 0;
  local_144 = 0;
  local_13c = 0;
  local_134 = 0;
  local_12c = 0;
  pcVar3 = httpGetEnv(param_1,"Page");
  if (pcVar3 != 0) {
    local_1cc = atoi(pcVar3);
  }
  iVar2 = swGetQoSMaxRuleNum();
  iVar8 = swGetQoSRuleNum();
  if (local_1c4[2] == 0) {
    trap(0x1c00);
  }
  local_1c8 = (uint)(iVar8 % local_1c4[2] != 0) + iVar8 / local_1c4[2];
  if (local_1c8 == 0) {
    local_1c8 = 1;
  }
  if (local_1c8 < local_1cc) {
    local_1cc = local_1c8;
  }
  iVar13 = (local_1cc + -1) * local_1c4[2];
  local_178 = 0;
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "QoSRuleListArray");
  local_38 = &local_178;
  local_34 = &local_1a8;
  local_30 = acStack_94;
  local_2c = &local_e0;
  do {
    while( true ) {
      iVar10 = swGetNextQoSRule(local_178,local_38,local_34,1);
      if (iVar10 != 0) goto LAB_0044b55c;
      if (iVar13 < 1) break;
      iVar13 = iVar13 + -1;
    }
    local_98 = local_178;
    bVar1 = local_160 != 0;
    if (local_168 == 0 && local_164 == 0) {
      pcVar3 = local_120;
      if (bVar1) {
LAB_0044b420:
        iVar10 = sprintf(pcVar3,"%d",local_160 >> 16);
        pcVar3 = pcVar3 + iVar10;
        if (local_160 >> 16 != (local_160 & -1)) {
          iVar10 = sprintf(pcVar3," - %d");
          pcVar3 = pcVar3 + iVar10;
        }
        goto LAB_0044b468;
      }
      local_120[0] = '\0';
    }
    else {
      uVar9 = QoSRuleIP2Str(local_168,auStack_198);
      iVar10 = sprintf(local_120,"%s",uVar9);
      pcVar3 = local_120 + iVar10;
      if (local_168 != local_164) {
        uVar9 = QoSRuleIP2Str(local_164,auStack_198);
        iVar10 = sprintf(pcVar3," - %s",uVar9);
        pcVar3 = pcVar3 + iVar10;
      }
      if (bVar1) {
        strcpy(pcVar3,"/");
        pcVar3 = pcVar3 + 1;
        goto LAB_0044b420;
      }
LAB_0044b468:
      if (local_15c._0_1_ != '\0') {
        strcpy(pcVar3,"/");
        pcVar6 = "UDP";
        if (local_15c._0_1_ == '\x01') {
          pcVar6 = "TCP";
        }
        strcpy(pcVar3 + 1,pcVar6);
      }
    }
    strcpy(local_30,local_120);
    local_54 = local_16c;
    local_50 = local_19c;
    local_4c = local_1a0;
    local_48 = local_1a4;
    local_44 = local_1a8;
    local_40[0] = local_174;
    pageDynParaListPrintf(local_2c,param_1);
    local_1c4[0] = local_1c4[0] + 1;
  } while (local_1c4[0] != local_1c4[2]);
LAB_0044b55c:
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  local_1d0 = (uint)(iVar2 != iVar8);
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "QoSRuleListParam");
  pageDynParaPrintf(&local_158,0,param_1);
  pageDynParaPrintf(&local_158,1,param_1);
  pageDynParaPrintf(&local_158,2,param_1);
  pageDynParaPrintf(&local_158,3,param_1);
  pageDynParaPrintf(&local_158,4,param_1);
  pageDynParaPrintf(&local_158,5,param_1);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  iVar2 = httpRpmFsA(param_1,"/userRpm/QoSRuleListRpm.htm");
  if (iVar2 == 2) {
    return 2;
  }
  iVar2 = 10;
LAB_0044b694:
  sVar11 = HttpErrorPage(param_1,iVar2,0,0);
  return sVar11;
}

