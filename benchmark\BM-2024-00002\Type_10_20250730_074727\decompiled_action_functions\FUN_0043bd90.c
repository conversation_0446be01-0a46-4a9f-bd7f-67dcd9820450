
undefined4 FUN_0043bd90(void)

{
  int iVar1;
  undefined4 uVar2;
  int iVar3;
  int iVar4;
  void *__ptr;
  
  iVar1 = mxmlNewXML("1.0");
  if (iVar1 == 0) {
    puts("xml=NULL");
    uVar2 = 0;
  }
  else {
    iVar3 = mxmlNewElement(iVar1,"soap:Envelope");
    if (iVar3 == 0) {
      mxmlDelete(iVar1);
      puts("soap_env=NULL");
      uVar2 = 0;
    }
    else {
      mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
      mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
      mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
      iVar3 = mxmlNewElement(iVar3,"soap:Body");
      if (iVar3 == 0) {
        mxmlDelete(iVar1);
        puts("body=NULL");
        uVar2 = 0;
      }
      else {
        iVar3 = mxmlNewElement(iVar3,"GetMACFilters2Response");
        if (iVar3 == 0) {
          mxmlDelete(iVar1);
          puts("GetMACFilters2Response_xml=NULL");
          uVar2 = 0;
        }
        else {
          mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
          iVar4 = mxmlNewElement(iVar3,"GetMACFilters2Result");
          if (iVar4 == 0) {
            mxmlDelete(iVar1);
            puts("GetMACFilters2Result_xml=NULL");
            uVar2 = 0;
          }
          else {
            mxmlNewText(iVar4,0,"O");
            iVar4 = mxmlNewElement(iVar3,"Enabled");
            if (iVar4 == 0) {
              mxmlDelete(iVar1);
              puts("Enabled_xml=NULL");
              uVar2 = 0;
            }
            else {
              mxmlNewText(iVar4,0,"false");
              iVar3 = mxmlNewElement(iVar3,"IsAllowList");
              if (iVar3 == 0) {
                mxmlDelete(iVar1);
                puts("IsAllowList_xml=NULL");
                uVar2 = 0;
              }
              else {
                mxmlNewText(iVar3,0,"false");
                __ptr = mxmlSaveAllocString(iVar1,0);
                if (__ptr != 0) {
                  FUN_0041ed70("",200,__ptr,"");
                  free(__ptr);
                }
                uVar2 = mxmlDelete(iVar1);
              }
            }
          }
        }
      }
    }
  }
  return uVar2;
}

