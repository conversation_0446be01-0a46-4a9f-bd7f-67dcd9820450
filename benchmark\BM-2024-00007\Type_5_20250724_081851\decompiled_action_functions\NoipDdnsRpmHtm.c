
int NoipDdnsRpmHtm(undefined4 param_1)

{
  char cVar1;
  int iVar2;
  uint uVar3;
  char *pcVar4;
  int iVar5;
  short sVar6;
  char *pcVar7;
  uint uVar8;
  code *pcVar9;
  uint local_308;
  undefined *local_304;
  undefined4 local_300;
  undefined *local_2fc;
  undefined4 local_2f8;
  undefined *local_2f4;
  undefined4 local_2f0;
  undefined *local_2ec;
  undefined4 local_2e8;
  undefined *local_2e4;
  undefined4 local_2e0;
  undefined *local_2dc;
  undefined4 local_2d8;
  undefined *local_2d4;
  undefined4 local_2d0;
  undefined *local_2cc;
  undefined4 local_2c8;
  undefined *local_2c4;
  undefined4 local_2c0;
  undefined4 local_2bc;
  uint local_2b4;
  char local_2b0 [63];
  undefined local_271;
  char local_270 [63];
  undefined local_231;
  char local_230 [127];
  undefined local_1b1;
  undefined auStack_1b0 [64];
  undefined auStack_170 [64];
  undefined auStack_130 [128];
  undefined auStack_b0 [4];
  undefined auStack_ac [128];
  undefined auStack_2c [4];
  undefined auStack_28 [4];
  undefined auStack_24 [4];
  undefined auStack_20 [8];
  
  iVar2 = swGetDdnsProviderIndex(0);
  uVar3 = getMaxWanPortNumber();
  memset(&local_2b4,0,260);
  local_304 = auStack_1b0;
  local_2fc = auStack_170;
  local_2f4 = auStack_130;
  local_2ec = auStack_b0;
  local_2e4 = auStack_ac;
  local_2dc = auStack_2c;
  local_2d4 = auStack_28;
  local_2cc = auStack_24;
  local_2c4 = auStack_20;
  local_2f0 = "@";
  local_300 = "@";
  local_2f8 = "@";
  local_2bc = 0;
  local_2e8 = 0;
  local_2e0 = 0;
  local_2d8 = 0;
  local_2d0 = 0;
  local_2c8 = 0;
  local_2c0 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  pcVar4 = httpGetEnv(param_1,"provider");
  if (pcVar4 != 0) {
    do {
      cVar1 = *pcVar4;
      pcVar4 = pcVar4 + 1;
    } while (cVar1 == ' ');
  }
  pcVar4 = httpGetEnv(param_1,"wan");
  uVar8 = 0;
  if (pcVar4 != 0) {
    uVar8 = atoi(pcVar4);
  }
  swGetNoipDdnsCfg(uVar8,&local_2b4);
  iVar5 = httpGetEnv(param_1,"Save");
  if ((iVar5 == 0) && (iVar5 = httpGetEnv(param_1,"Login"), iVar5 == 0)) {
    iVar2 = httpGetEnv(param_1,"Logout");
    if (iVar2 == 0) goto LAB_004414bc;
LAB_0044148c:
    pcVar9 = swDdnsStop;
  }
  else {
    pcVar4 = httpGetEnv(param_1,"username");
    if (pcVar4 == 0) {
      local_2b0[0] = '\0';
    }
    else {
      do {
        pcVar7 = pcVar4;
        pcVar4 = pcVar7 + 1;
      } while (*pcVar7 == ' ');
      local_271 = 0;
      strncpy(local_2b0,pcVar7,"?");
    }
    pcVar4 = httpGetEnv(param_1,"pwd");
    if (pcVar4 == 0) {
      local_270[0] = '\0';
    }
    else {
      do {
        pcVar7 = pcVar4;
        pcVar4 = pcVar7 + 1;
      } while (*pcVar7 == ' ');
      local_231 = 0;
      strncpy(local_270,pcVar7,"?");
    }
    pcVar4 = httpGetEnv(param_1,"cliUrl");
    if (pcVar4 == 0) {
      local_230[0] = '\0';
    }
    else {
      do {
        pcVar7 = pcVar4;
        pcVar4 = pcVar7 + 1;
      } while (*pcVar7 == ' ');
      local_1b1 = 0;
      strncpy(local_230,pcVar7,127);
    }
    pcVar4 = httpGetEnv(param_1,"EnDdns");
    local_2b4 = 0;
    if (pcVar4 != 0) {
      iVar5 = atoi(pcVar4);
      local_2b4 = (uint)(iVar5 == 2);
    }
    if (iVar2 != 3) {
      swDdnsStop(uVar8,iVar2);
    }
    swSetNoipDdnsCfg(uVar8,&local_2b4);
    if (local_2b4 == 0) goto LAB_0044148c;
    if (((local_230[0] == '\0') || (local_270[0] == '\0')) || (local_2b0[0] == '\0'))
    goto LAB_004414bc;
    pcVar9 = swDdnsStart;
  }
  (*pcVar9)(uVar8,3);
  usleep(500000);
LAB_004414bc:
  swGetNoipDdnsCfg(uVar8,&local_2b4);
  pageParaSet(&local_304,local_2b0,0);
  pageParaSet(&local_304,local_270,1);
  pageParaSet(&local_304,local_230,2);
  local_308 = local_2b4;
  pageParaSet(&local_304,&local_308,3);
  "" = swGetNoipDnsState(0);
  if ("" == 5) {
    "" = 0;
  }
  local_308 = ("")[0x00000000];
  pageParaSet(&local_304,&local_308,4);
  local_308 = 3;
  pageParaSet(&local_304,&local_308,5);
  local_308 = 2;
  pageParaSet(&local_304,&local_308,6);
  local_308 = uVar8;
  pageParaSet(&local_304,&local_308,7);
  local_308 = uVar3;
  pageParaSet(&local_304,&local_308,8);
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "serInf");
  iVar2 = 0;
  do {
    iVar5 = iVar2 + 1;
    pageDynParaPrintf(&local_304,iVar2,param_1);
    iVar2 = iVar5;
  } while (iVar5 != 9);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  httpPrintfDdnsTypeInfo(param_1);
  HttpWebV4Head(param_1,0,1);
  iVar2 = httpRpmFsA(param_1,"/userRpm/NoipDdnsRpm.htm");
  iVar5 = 2;
  if (iVar2 != 2) {
    sVar6 = HttpErrorPage(param_1,10,0,0);
    iVar5 = sVar6;
  }
  return iVar5;
}

