
int FUN_00452d0c(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  short sVar8;
  char *pcVar3;
  uint32_t uVar4;
  uint32_t uVar5;
  uint32_t uVar6;
  uint32_t uVar7;
  int iVar9;
  uint uVar10;
  uint uVar11;
  uint local_118;
  undefined auStack_114 [10];
  ushort local_10a;
  char acStack_108 [16];
  char local_f8 [16];
  uint32_t local_e8;
  undefined4 local_e4;
  undefined4 local_e0;
  uint local_dc;
  uint local_d8;
  short local_d4;
  short local_d2;
  undefined2 local_d0;
  undefined2 local_ce;
  uint32_t local_cc;
  undefined2 local_c8;
  uint local_c4;
  undefined auStack_c0 [16];
  undefined auStack_b0 [16];
  undefined auStack_a0 [16];
  undefined4 local_90;
  undefined4 local_8c;
  undefined auStack_88 [4];
  undefined auStack_84 [4];
  uint local_80;
  uint local_7c;
  undefined *local_78;
  undefined4 local_74;
  undefined *local_70;
  undefined4 local_6c;
  undefined *local_68;
  undefined4 local_64;
  undefined4 *local_60;
  undefined4 local_5c;
  undefined4 *local_58;
  undefined4 local_54;
  undefined *local_50;
  undefined4 local_4c;
  undefined *local_48;
  undefined4 local_44;
  uint *local_40;
  undefined4 local_3c;
  uint *local_38;
  undefined4 local_34;
  undefined4 local_30;
  
  local_78 = auStack_c0;
  local_70 = auStack_b0;
  local_68 = auStack_a0;
  local_60 = &local_90;
  local_58 = &local_8c;
  local_50 = auStack_88;
  local_48 = auStack_84;
  local_40 = &local_80;
  local_38 = &local_7c;
  local_64 = 16;
  local_74 = 16;
  local_6c = 16;
  local_30 = 0;
  local_5c = 0;
  local_54 = 0;
  local_4c = 0;
  local_44 = 0;
  local_3c = 0;
  local_34 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    sVar8 = HttpDenyPage(param_1);
    goto LAB_004532d0;
  }
  local_d8 = 0;
  local_e8 = 0;
  local_e4 = 0;
  local_e0 = 0;
  local_dc = 0;
  pcVar3 = httpGetEnv(param_1,"Page");
  uVar11 = 1;
  if (pcVar3 != 0) {
    uVar11 = atoi(pcVar3);
    iVar2 = getDefaultVSTblSize();
    if (((int)(((uint)(iVar2 >> 31) >> 29) + iVar2) >> 3 < uVar11) || (uVar11 < 1)) {
      uVar11 = 1;
    }
  }
  iVar2 = httpGetEnv(param_1,"Add");
  uVar10 = 0;
  if (iVar2 == 0) {
    pcVar3 = httpGetEnv(param_1,"Modify");
    if (pcVar3 == 0) {
      iVar2 = FUN_0045205c(param_1);
      return iVar2;
    }
    uVar10 = atoi(pcVar3);
    if (-1 < uVar10) {
      bVar1 = true;
      iVar2 = getDefaultVSTblSize();
      if (uVar10 < iVar2) goto LAB_00452f4c;
    }
    uVar10 = 0;
    bVar1 = true;
  }
  else {
    bVar1 = false;
  }
LAB_00452f4c:
  memset(auStack_c0,0,"H");
  swGetFirewallHttpCtrl(auStack_114);
  if (bVar1) {
    swGetVsEntry(uVar10,&local_d4);
    local_dc = local_c4;
    local_d8 = CONCAT22(local_c8,local_d8._2_2_);
    local_e0 = CONCAT22(local_d0,local_ce);
    local_e8 = local_cc;
    local_e4 = CONCAT22(local_d4,local_d2);
    if (local_d4 == local_d2) {
      sprintf(local_f8,"%u");
    }
    else {
      sprintf(local_f8,"%u-%u");
    }
    pageParaSet(&local_78,local_f8,0);
    if (local_e4 == local_e0) {
      local_f8[12] = '\0';
      local_f8[13] = '\0';
      local_f8[14] = '\0';
      local_f8[15] = '\0';
      local_f8[0] = '\0';
      local_f8[1] = '\0';
      local_f8[2] = '\0';
      local_f8[3] = '\0';
      local_f8[4] = '\0';
      local_f8[5] = '\0';
      local_f8[6] = '\0';
      local_f8[7] = '\0';
      local_f8[8] = '\0';
      local_f8[9] = '\0';
      local_f8[10] = '\0';
      local_f8[11] = '\0';
    }
    else {
      local_f8[0] = '\0';
      local_f8[1] = '\0';
      local_f8[2] = '\0';
      local_f8[3] = '\0';
      local_f8[4] = '\0';
      local_f8[5] = '\0';
      local_f8[6] = '\0';
      local_f8[7] = '\0';
      local_f8[8] = '\0';
      local_f8[9] = '\0';
      local_f8[10] = '\0';
      local_f8[11] = '\0';
      local_f8[12] = '\0';
      local_f8[13] = '\0';
      local_f8[14] = '\0';
      local_f8[15] = '\0';
      sprintf(local_f8,"%u",local_e0 >> 16);
    }
    pageParaSet(&local_78,local_f8,1);
    uVar4 = ntohl(local_e8);
    uVar5 = ntohl(local_e8);
    uVar6 = ntohl(local_e8);
    uVar7 = ntohl(local_e8);
    sprintf(acStack_108,"%d.%d.%d.%d",uVar4 >> 24,uVar5 >> 16 & 255,(int)(uVar6 & -256) >> 8,
            uVar7 & 255);
    pageParaSet(&local_78,acStack_108,2);
    local_118 = local_d8 >> 16;
    pageParaSet(&local_78,&local_118,3);
    local_118 = local_dc;
    pageParaSet(&local_78,&local_118,4);
    local_118 = 1;
    pageParaSet(&local_78,&local_118,5);
    local_118 = uVar10;
    pageParaSet(&local_78,&local_118,6);
    local_118 = uVar11;
    pageParaSet(&local_78,&local_118,7);
    local_118 = local_10a;
    pageParaSet(&local_78,&local_118,8);
  }
  else {
    local_7c = local_10a;
    local_90 = 1;
    local_8c = 1;
    local_80 = uVar11;
  }
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "vsEditInf");
  iVar2 = 0;
  do {
    iVar9 = iVar2 + 1;
    pageDynParaPrintf(&local_78,iVar2,param_1);
    iVar2 = iVar9;
  } while (iVar9 != 9);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  HttpWebV4Head(param_1,0,1);
  iVar2 = httpRpmFsA(param_1,"/userRpm/VirtualServerAdvRpm.htm");
  if (iVar2 == 2) {
    return 2;
  }
  sVar8 = HttpErrorPage(param_1,10,0,0);
LAB_004532d0:
  return sVar8;
}

