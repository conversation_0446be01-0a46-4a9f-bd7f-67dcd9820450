
int LoginRpmHtm(undefined4 param_1)

{
  int iVar1;
  int iVar2;
  short sVar4;
  void *__s;
  undefined4 uVar3;
  
  iVar1 = httpGetEnv(param_1,"Save");
  iVar2 = httpStatusGet(param_1);
  if ((iVar2 == 200) && (iVar1 != 0)) {
    sVar4 = GoIndex(param_1,0);
  }
  else {
    httpStatusSet(param_1,0);
    httpHeaderGenerate(param_1);
    HttpWebV4Head(param_1,0xffffffff,0xffffffff);
    __s = malloc(0x800);
    memset(__s,0,0x800);
    getWholeHostAddr(param_1,__s);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "httpAutErrorArray");
    uVar3 = getAuthErrorReason();
    httpPrintf(param_1,"%d, ",uVar3);
    httpPrintf(param_1,"%d, ",1);
    httpPrintf(param_1,""%s", ",__s);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    iVar1 = needRedirect(param_1);
    if (iVar1 != 0) {
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">window.parent.location.href = httpAutErrorArray[2];</SCRIPT>"
                );
    }
    free(__s);
    iVar1 = httpRpmFsA(param_1,"/userRpm/LoginRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar4 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar4;
}

