
int FUN_0042585c(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  short sVar3;
  undefined4 *puVar4;
  int iVar5;
  int local_b0;
  undefined auStack_ac [4];
  undefined auStack_a8 [4];
  undefined auStack_a4 [16];
  char acStack_94 [20];
  undefined *local_80;
  undefined4 local_7c;
  undefined *local_78;
  undefined4 local_74;
  undefined4 local_70;
  undefined *local_68;
  undefined4 local_64;
  undefined *local_60;
  undefined4 local_5c;
  undefined4 local_58;
  undefined auStack_50 [16];
  undefined auStack_40 [28];
  
  local_80 = auStack_ac;
  local_78 = auStack_a8;
  local_68 = auStack_50;
  local_60 = auStack_40;
  local_64 = 16;
  local_5c = 18;
  local_70 = 0;
  local_7c = 0;
  local_74 = 0;
  local_58 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    sVar3 = HttpDenyPage(param_1);
  }
  else {
    iVar2 = httpGetEnv(param_1,"Clear");
    if (iVar2 == 0) {
      iVar2 = getEnvToInt(param_1,"Del",0,0x7fffffff);
      if (iVar2 != -128) {
        firewallDosFilterDeleteOne(iVar2 + -1);
      }
    }
    else {
      swFirewallDosFilterClear();
    }
    iVar2 = swGetFirewallDosHost();
    local_b0 = iVar2;
    pageParaSet(&local_80,&local_b0,0);
    local_b0 = 2;
    pageParaSet(&local_80,&local_b0,1);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "dosPara");
    pageDynParaPrintf(&local_80,0,param_1);
    pageDynParaPrintf(&local_80,1,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "dosList");
    puVar4 = &dosEntries;
    iVar5 = 0;
    while (bVar1 = iVar5 < iVar2, iVar5 = iVar5 + 1, bVar1) {
      inet_ntoa_b(*puVar4,auStack_a4);
      pageParaSet(&local_68,auStack_a4,0);
      sprintf(acStack_94,"%02X-%02X-%02X-%02X-%02X-%02X",(uint)*(byte *)(puVar4 + 1),
              (uint)*(byte *)(puVar4 + 5),(uint)*(byte *)(puVar4 + 6),
              (uint)*(byte *)(puVar4 + 7),(uint)*(byte *)(puVar4 + 2),
              (uint)*(byte *)(puVar4 + 9));
      puVar4 = puVar4 + 4;
      pageParaSet(&local_68,acStack_94,1);
      pageDynParaListPrintf(&local_68,param_1);
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar2 = httpRpmFsA(param_1,"/userRpm/MiscShowRpm.htm");
    if (iVar2 == 2) {
      return 2;
    }
    sVar3 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar3;
}

