
int FUN_0045cbfc(undefined4 param_1)

{
  int iVar1;
  short sVar9;
  char *pcVar2;
  int iVar3;
  int iVar4;
  uint32_t uVar5;
  uint32_t uVar6;
  uint32_t uVar7;
  uint32_t uVar8;
  undefined1 *puVar10;
  char *__cp;
  int iVar11;
  uint local_90;
  int local_8c;
  in_addr_t local_88;
  undefined4 local_84;
  char acStack_80 [16];
  ushort local_70;
  ushort local_6e;
  int local_60;
  undefined auStack_5c [4];
  undefined auStack_58 [16];
  undefined auStack_48 [4];
  undefined *local_44;
  undefined4 local_40;
  undefined *local_3c;
  undefined4 local_38;
  undefined *local_34;
  undefined4 local_30;
  undefined4 local_2c;
  
  local_44 = auStack_5c;
  local_3c = auStack_58;
  local_34 = auStack_48;
  local_38 = 16;
  local_2c = 0;
  local_40 = 0;
  local_30 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar9 = HttpDenyPage(param_1);
    goto LAB_0045d134;
  }
  local_8c = 0;
  local_88 = 0;
  local_84 = 0;
  if ("" == 0) {
    swGetFirewallHttpCtrl();
    "" = local_84._2_2_;
  }
  iVar1 = httpGetEnv(param_1,"Save");
  if (iVar1 == 0) {
LAB_0045cf30:
    swGetFirewallHttpCtrl(&local_8c);
    local_90 = local_84 & -1;
    pageParaSet(&local_44,&local_90,0);
    uVar5 = ntohl(local_88);
    uVar6 = ntohl(local_88);
    uVar7 = ntohl(local_88);
    uVar8 = ntohl(local_88);
    sprintf(acStack_80,"%d.%d.%d.%d",uVar5 >> 24,uVar6 >> 16 & 255,(int)(uVar7 & -256) >> 8,
            uVar8 & 255);
    pageParaSet(&local_44,acStack_80,1);
    local_90 = (uint)("" != local_84._2_2_);
    pageParaSet(&local_44,&local_90,2);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "managementPara");
    pageDynParaPrintf(&local_44,0,param_1);
    pageDynParaPrintf(&local_44,1,param_1);
    pageDynParaPrintf(&local_44,2,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/ManageControlRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    iVar1 = 10;
    puVar10 = 0;
  }
  else {
    swGetFirewallHttpCtrl(&local_8c);
    pcVar2 = httpGetEnv(param_1,"i");
    if (pcVar2 == 0) {
      local_88 = 0;
LAB_0045cdd0:
      if (local_88 == 0) {
        local_8c = 0;
      }
      iVar1 = getEnvToInt(param_1,"port",0x80000000,0x7fffffff);
      if (iVar1 == -128) {
        local_84 = CONCAT22(local_84._0_2_,"P");
LAB_0045cee4:
        iVar1 = swChkFirewallHttpCtrl(&local_8c);
        if (iVar1 == 0) {
          swSetFirewallHttpCtrl(&local_8c);
          goto LAB_0045cf30;
        }
        puVar10 = "";
      }
      else if (iVar1 - 1U < -1) {
        if (local_8c != 1) {
LAB_0045cee0:
          local_84 = CONCAT22(local_84._0_2_,iVar1);
          goto LAB_0045cee4;
        }
        iVar3 = 0;
        do {
          iVar4 = getDefaultVSTblSize();
          iVar11 = iVar3 + 1;
          if ((iVar4 <= iVar3) || (iVar3 = swGetVsEntry(iVar3,&local_70), iVar3 != 0))
          goto LAB_0045cee0;
          iVar3 = iVar11;
        } while (((iVar1 < (int)local_70) || ((int)local_6e < iVar1)) || (local_60 != 1)
                );
        puVar10 = "";
        iVar1 = 0x2ee4;
      }
      else {
        puVar10 = "";
        iVar1 = 0x2ee2;
      }
    }
    else {
      do {
        __cp = pcVar2;
        pcVar2 = __cp + 1;
      } while (*__cp == ' ');
      if ((__cp == 0) || (*__cp == '\0')) {
        local_88 = 0;
        goto LAB_0045cdd0;
      }
      iVar1 = swChkDotIpAddr(__cp);
      if (iVar1 != 0) {
        local_88 = inet_addr(__cp);
        local_8c = 1;
        goto LAB_0045cdd0;
      }
      puVar10 = "";
      iVar1 = 12000;
    }
  }
  sVar9 = HttpErrorPage(param_1,iVar1,puVar10,0);
LAB_0045d134:
  return sVar9;
}

