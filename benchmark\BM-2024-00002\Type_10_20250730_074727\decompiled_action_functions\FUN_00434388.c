
undefined4 FUN_00434388(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  void *__ptr;
  sysinfo local_58;
  char local_18 [16];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetSystemUpTime");
  }
  else {
    local_18[0] = '\0';
    local_18[1] = '\0';
    local_18[2] = '\0';
    local_18[3] = '\0';
    local_18[4] = '\0';
    local_18[5] = '\0';
    local_18[6] = '\0';
    local_18[7] = '\0';
    local_18[8] = '\0';
    local_18[9] = '\0';
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"SOAP-ENV:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:SOAP-ENV","http://schemas.xmlsoap.org/soap/envelope/");
        mxmlElementSetAttr(iVar2,"SOAP-ENV:encodingStyle",
                           "http://schemas.xmlsoap.org/soap/encoding/");
        iVar2 = mxmlNewElement(iVar2,"SOAP-ENV:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"GetSystemUpTimeResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("GetSystemUpTimeResponse_xml=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar3 = mxmlNewElement(iVar2,"GetSystemUpTimeResult");
            if (iVar3 == 0) {
              mxmlDelete(iVar1);
              puts("GetSystemUpTimeResult_xml=NULL");
            }
            else {
              mxmlNewText(iVar3,0,"O");
              iVar2 = mxmlNewElement(iVar2,"SystemUpTime");
              if (iVar2 == 0) {
                mxmlDelete(iVar1);
                puts("SystemUpTime_xml=NULL");
              }
              else {
                sysinfo(&local_58);
                snprintf(local_18,9,"%d",local_58.uptime);
                mxmlNewText(iVar2,0,local_18);
                __ptr = mxmlSaveAllocString(iVar1,0);
                if (__ptr != 0) {
                  FUN_0041ed70("",200,__ptr,"");
                  free(__ptr);
                }
                mxmlDelete(iVar1);
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

