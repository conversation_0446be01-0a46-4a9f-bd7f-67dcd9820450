
undefined4 FUN_00449bc8(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  FILE *__stream;
  char *pcVar4;
  int iVar5;
  int iVar6;
  void *__ptr;
  char *local_388;
  char *local_384;
  undefined4 local_374;
  undefined4 local_370;
  undefined4 local_36c;
  char local_368 [48];
  undefined4 local_338;
  undefined4 local_334;
  undefined4 local_330;
  undefined4 local_32c;
  undefined4 local_328;
  undefined4 local_324;
  undefined4 local_320;
  undefined4 local_31c;
  undefined4 local_318;
  undefined4 local_314;
  undefined4 local_310;
  undefined4 local_30c;
  undefined4 local_308;
  undefined4 local_304;
  undefined4 local_300;
  undefined4 local_2fc;
  char acStack_2f8 [128];
  char local_278 [32];
  undefined4 local_258;
  undefined4 local_254;
  undefined4 local_250;
  undefined4 local_24c;
  undefined4 local_248;
  undefined4 local_244;
  undefined4 local_240;
  undefined4 local_23c;
  char local_238 [32];
  undefined4 local_218;
  undefined4 local_214;
  undefined4 local_210;
  undefined4 local_20c;
  undefined4 local_208;
  undefined4 local_204;
  undefined4 local_200;
  undefined4 local_1fc;
  char local_1f8 [32];
  undefined4 local_1d8;
  undefined4 local_1d4;
  undefined4 local_1d0;
  undefined4 local_1cc;
  undefined4 local_1c8;
  undefined4 local_1c4;
  undefined4 local_1c0;
  undefined4 local_1bc;
  char local_1b8 [32];
  undefined4 local_198;
  undefined4 local_194;
  undefined4 local_190;
  undefined4 local_18c;
  undefined4 local_188;
  undefined4 local_184;
  undefined4 local_180;
  undefined4 local_17c;
  undefined4 local_178;
  undefined4 local_174;
  undefined4 local_170;
  undefined4 local_16c;
  undefined4 local_168;
  undefined4 local_164;
  undefined4 local_160;
  undefined4 local_15c;
  undefined4 local_158;
  undefined4 local_154;
  undefined4 local_150;
  undefined4 local_14c;
  undefined4 local_148;
  undefined4 local_144;
  undefined4 local_140;
  undefined4 local_13c;
  char local_138 [32];
  undefined4 local_118;
  undefined4 local_114;
  undefined4 local_110;
  undefined4 local_10c;
  undefined4 local_108;
  undefined4 local_104;
  undefined4 local_100;
  undefined4 local_fc;
  char local_f8 [32];
  undefined4 local_d8;
  undefined4 local_d4;
  undefined4 local_d0;
  undefined4 local_cc;
  undefined4 local_c8;
  undefined4 local_c4;
  undefined4 local_c0;
  undefined4 local_bc;
  char local_b8 [32];
  undefined4 local_98;
  undefined4 local_94;
  undefined4 local_90;
  undefined4 local_8c;
  undefined4 local_88;
  undefined4 local_84;
  undefined4 local_80;
  undefined4 local_7c;
  char local_78 [44];
  char local_4c [8];
  in_addr local_44;
  undefined4 local_40;
  undefined4 local_3c;
  undefined4 local_38;
  undefined4 local_34;
  char local_30 [16];
  in_addr local_20;
  in_addr local_1c;
  int local_18;
  int local_14;
  int local_10 [2];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetWanSettings");
    return 0;
  }
  local_370 = 0;
  local_36c = 0;
  local_374 = 0;
  local_384 = 0;
  local_388 = 0;
  local_368[0] = '\0';
  local_368[1] = '\0';
  local_368[2] = '\0';
  local_368[3] = '\0';
  local_368[4] = '\0';
  local_368[5] = '\0';
  local_368[6] = '\0';
  local_368[7] = '\0';
  local_368[8] = '\0';
  local_368[9] = '\0';
  local_368[10] = '\0';
  local_368[11] = '\0';
  local_368[12] = '\0';
  local_368[13] = '\0';
  local_368[14] = '\0';
  local_368[15] = '\0';
  local_368[16] = '\0';
  local_368[17] = '\0';
  local_368[18] = '\0';
  local_368[19] = '\0';
  local_368[20] = '\0';
  local_368[21] = '\0';
  local_368[22] = '\0';
  local_368[23] = '\0';
  local_368[24] = '\0';
  local_368[25] = '\0';
  local_368[26] = '\0';
  local_368[27] = '\0';
  local_368[28] = '\0';
  local_368[29] = '\0';
  local_368[30] = '\0';
  local_368[31] = '\0';
  local_368[32] = '\0';
  local_368[33] = '\0';
  local_368[34] = '\0';
  local_368[35] = '\0';
  local_368[36] = '\0';
  local_368[37] = '\0';
  local_368[38] = '\0';
  local_368[39] = '\0';
  local_368[40] = '\0';
  local_368[41] = '\0';
  local_368[42] = '\0';
  local_368[43] = '\0';
  local_368[44] = '\0';
  local_368[45] = '\0';
  local_368[46] = '\0';
  local_368[47] = '\0';
  local_338 = 0;
  local_334 = 0;
  local_330 = 0;
  local_32c = 0;
  local_328 = 0;
  local_324 = 0;
  local_320 = 0;
  local_31c = 0;
  local_318 = 0;
  local_314 = 0;
  local_310 = 0;
  local_30c = 0;
  local_308 = 0;
  local_304 = 0;
  local_300 = 0;
  local_2fc = 0;
  memset(acStack_2f8,0,128);
  local_278[0] = '\0';
  local_278[1] = '\0';
  local_278[2] = '\0';
  local_278[3] = '\0';
  local_278[4] = '\0';
  local_278[5] = '\0';
  local_278[6] = '\0';
  local_278[7] = '\0';
  local_278[8] = '\0';
  local_278[9] = '\0';
  local_278[10] = '\0';
  local_278[11] = '\0';
  local_278[12] = '\0';
  local_278[13] = '\0';
  local_278[14] = '\0';
  local_278[15] = '\0';
  local_278[16] = '\0';
  local_278[17] = '\0';
  local_278[18] = '\0';
  local_278[19] = '\0';
  local_278[20] = '\0';
  local_278[21] = '\0';
  local_278[22] = '\0';
  local_278[23] = '\0';
  local_278[24] = '\0';
  local_278[25] = '\0';
  local_278[26] = '\0';
  local_278[27] = '\0';
  local_278[28] = '\0';
  local_278[29] = '\0';
  local_278[30] = '\0';
  local_278[31] = '\0';
  local_258 = 0;
  local_254 = 0;
  local_250 = 0;
  local_24c = 0;
  local_248 = 0;
  local_244 = 0;
  local_240 = 0;
  local_23c = 0;
  local_238[0] = '\0';
  local_238[1] = '\0';
  local_238[2] = '\0';
  local_238[3] = '\0';
  local_238[4] = '\0';
  local_238[5] = '\0';
  local_238[6] = '\0';
  local_238[7] = '\0';
  local_238[8] = '\0';
  local_238[9] = '\0';
  local_238[10] = '\0';
  local_238[11] = '\0';
  local_238[12] = '\0';
  local_238[13] = '\0';
  local_238[14] = '\0';
  local_238[15] = '\0';
  local_238[16] = '\0';
  local_238[17] = '\0';
  local_238[18] = '\0';
  local_238[19] = '\0';
  local_238[20] = '\0';
  local_238[21] = '\0';
  local_238[22] = '\0';
  local_238[23] = '\0';
  local_238[24] = '\0';
  local_238[25] = '\0';
  local_238[26] = '\0';
  local_238[27] = '\0';
  local_238[28] = '\0';
  local_238[29] = '\0';
  local_238[30] = '\0';
  local_238[31] = '\0';
  local_218 = 0;
  local_214 = 0;
  local_210 = 0;
  local_20c = 0;
  local_208 = 0;
  local_204 = 0;
  local_200 = 0;
  local_1fc = 0;
  local_1f8[0] = '\0';
  local_1f8[1] = '\0';
  local_1f8[2] = '\0';
  local_1f8[3] = '\0';
  local_1f8[4] = '\0';
  local_1f8[5] = '\0';
  local_1f8[6] = '\0';
  local_1f8[7] = '\0';
  local_1f8[8] = '\0';
  local_1f8[9] = '\0';
  local_1f8[10] = '\0';
  local_1f8[11] = '\0';
  local_1f8[12] = '\0';
  local_1f8[13] = '\0';
  local_1f8[14] = '\0';
  local_1f8[15] = '\0';
  local_1f8[16] = '\0';
  local_1f8[17] = '\0';
  local_1f8[18] = '\0';
  local_1f8[19] = '\0';
  local_1f8[20] = '\0';
  local_1f8[21] = '\0';
  local_1f8[22] = '\0';
  local_1f8[23] = '\0';
  local_1f8[24] = '\0';
  local_1f8[25] = '\0';
  local_1f8[26] = '\0';
  local_1f8[27] = '\0';
  local_1f8[28] = '\0';
  local_1f8[29] = '\0';
  local_1f8[30] = '\0';
  local_1f8[31] = '\0';
  local_1d8 = 0;
  local_1d4 = 0;
  local_1d0 = 0;
  local_1cc = 0;
  local_1c8 = 0;
  local_1c4 = 0;
  local_1c0 = 0;
  local_1bc = 0;
  local_1b8[0] = '\0';
  local_1b8[1] = '\0';
  local_1b8[2] = '\0';
  local_1b8[3] = '\0';
  local_1b8[4] = '\0';
  local_1b8[5] = '\0';
  local_1b8[6] = '\0';
  local_1b8[7] = '\0';
  local_1b8[8] = '\0';
  local_1b8[9] = '\0';
  local_1b8[10] = '\0';
  local_1b8[11] = '\0';
  local_1b8[12] = '\0';
  local_1b8[13] = '\0';
  local_1b8[14] = '\0';
  local_1b8[15] = '\0';
  local_1b8[16] = '\0';
  local_1b8[17] = '\0';
  local_1b8[18] = '\0';
  local_1b8[19] = '\0';
  local_1b8[20] = '\0';
  local_1b8[21] = '\0';
  local_1b8[22] = '\0';
  local_1b8[23] = '\0';
  local_1b8[24] = '\0';
  local_1b8[25] = '\0';
  local_1b8[26] = '\0';
  local_1b8[27] = '\0';
  local_1b8[28] = '\0';
  local_1b8[29] = '\0';
  local_1b8[30] = '\0';
  local_1b8[31] = '\0';
  local_198 = 0;
  local_194 = 0;
  local_190 = 0;
  local_18c = 0;
  local_188 = 0;
  local_184 = 0;
  local_180 = 0;
  local_17c = 0;
  local_178 = 0;
  local_174 = 0;
  local_170 = 0;
  local_16c = 0;
  local_168 = 0;
  local_164 = 0;
  local_160 = 0;
  local_15c = 0;
  local_158 = 0;
  local_154 = 0;
  local_150 = 0;
  local_14c = 0;
  local_148 = 0;
  local_144 = 0;
  local_140 = 0;
  local_13c = 0;
  local_138[0] = '\0';
  local_138[1] = '\0';
  local_138[2] = '\0';
  local_138[3] = '\0';
  local_138[4] = '\0';
  local_138[5] = '\0';
  local_138[6] = '\0';
  local_138[7] = '\0';
  local_138[8] = '\0';
  local_138[9] = '\0';
  local_138[10] = '\0';
  local_138[11] = '\0';
  local_138[12] = '\0';
  local_138[13] = '\0';
  local_138[14] = '\0';
  local_138[15] = '\0';
  local_138[16] = '\0';
  local_138[17] = '\0';
  local_138[18] = '\0';
  local_138[19] = '\0';
  local_138[20] = '\0';
  local_138[21] = '\0';
  local_138[22] = '\0';
  local_138[23] = '\0';
  local_138[24] = '\0';
  local_138[25] = '\0';
  local_138[26] = '\0';
  local_138[27] = '\0';
  local_138[28] = '\0';
  local_138[29] = '\0';
  local_138[30] = '\0';
  local_138[31] = '\0';
  local_118 = 0;
  local_114 = 0;
  local_110 = 0;
  local_10c = 0;
  local_108 = 0;
  local_104 = 0;
  local_100 = 0;
  local_fc = 0;
  local_f8[0] = '\0';
  local_f8[1] = '\0';
  local_f8[2] = '\0';
  local_f8[3] = '\0';
  local_f8[4] = '\0';
  local_f8[5] = '\0';
  local_f8[6] = '\0';
  local_f8[7] = '\0';
  local_f8[8] = '\0';
  local_f8[9] = '\0';
  local_f8[10] = '\0';
  local_f8[11] = '\0';
  local_f8[12] = '\0';
  local_f8[13] = '\0';
  local_f8[14] = '\0';
  local_f8[15] = '\0';
  local_f8[16] = '\0';
  local_f8[17] = '\0';
  local_f8[18] = '\0';
  local_f8[19] = '\0';
  local_f8[20] = '\0';
  local_f8[21] = '\0';
  local_f8[22] = '\0';
  local_f8[23] = '\0';
  local_f8[24] = '\0';
  local_f8[25] = '\0';
  local_f8[26] = '\0';
  local_f8[27] = '\0';
  local_f8[28] = '\0';
  local_f8[29] = '\0';
  local_f8[30] = '\0';
  local_f8[31] = '\0';
  local_d8 = 0;
  local_d4 = 0;
  local_d0 = 0;
  local_cc = 0;
  local_c8 = 0;
  local_c4 = 0;
  local_c0 = 0;
  local_bc = 0;
  local_b8[0] = '\0';
  local_b8[1] = '\0';
  local_b8[2] = '\0';
  local_b8[3] = '\0';
  local_b8[4] = '\0';
  local_b8[5] = '\0';
  local_b8[6] = '\0';
  local_b8[7] = '\0';
  local_b8[8] = '\0';
  local_b8[9] = '\0';
  local_b8[10] = '\0';
  local_b8[11] = '\0';
  local_b8[12] = '\0';
  local_b8[13] = '\0';
  local_b8[14] = '\0';
  local_b8[15] = '\0';
  local_b8[16] = '\0';
  local_b8[17] = '\0';
  local_b8[18] = '\0';
  local_b8[19] = '\0';
  local_b8[20] = '\0';
  local_b8[21] = '\0';
  local_b8[22] = '\0';
  local_b8[23] = '\0';
  local_b8[24] = '\0';
  local_b8[25] = '\0';
  local_b8[26] = '\0';
  local_b8[27] = '\0';
  local_b8[28] = '\0';
  local_b8[29] = '\0';
  local_b8[30] = '\0';
  local_b8[31] = '\0';
  local_98 = 0;
  local_94 = 0;
  local_90 = 0;
  local_8c = 0;
  local_88 = 0;
  local_84 = 0;
  local_80 = 0;
  local_7c = 0;
  local_78[0] = '\0';
  local_78[1] = '\0';
  local_78[2] = '\0';
  local_78[3] = '\0';
  local_78[4] = '\0';
  local_78[5] = '\0';
  local_78[6] = '\0';
  local_78[7] = '\0';
  local_78[8] = '\0';
  local_78[9] = '\0';
  local_78[10] = '\0';
  local_78[11] = '\0';
  local_78[12] = '\0';
  local_78[13] = '\0';
  local_78[14] = '\0';
  local_78[15] = '\0';
  local_78[16] = '\0';
  local_78[17] = '\0';
  local_78[18] = '\0';
  local_78[19] = '\0';
  local_78[20] = '\0';
  local_78[21] = '\0';
  local_78[22] = '\0';
  local_78[23] = '\0';
  local_78[24] = '\0';
  local_78[25] = '\0';
  local_78[26] = '\0';
  local_78[27] = '\0';
  local_78[28] = '\0';
  local_78[29] = '\0';
  local_78[30] = '\0';
  local_78[31] = '\0';
  local_78[32] = '\0';
  local_78[33] = '\0';
  local_78[34] = '\0';
  local_78[35] = '\0';
  local_78[36] = '\0';
  local_78[37] = '\0';
  local_78[38] = '\0';
  local_78[39] = '\0';
  local_78[40] = '\0';
  local_78[41] = '\0';
  local_4c[0] = '\0';
  local_4c[1] = '\0';
  local_4c[2] = '\0';
  local_4c[3] = '\0';
  local_4c[4] = '\0';
  local_4c[5] = '\0';
  local_4c[6] = '\0';
  local_4c[7] = '\0';
  local_44.s_addr = 0;
  local_40 = 0;
  local_3c = 0;
  local_38 = 0;
  local_34 = 0;
  local_30[0] = '\0';
  local_30[1] = '\0';
  local_30[2] = '\0';
  local_30[3] = '\0';
  local_30[4] = '\0';
  local_30[5] = '\0';
  local_30[6] = '\0';
  local_30[7] = '\0';
  local_30[8] = '\0';
  local_30[9] = '\0';
  local_30[10] = '\0';
  local_30[11] = '\0';
  local_30[12] = '\0';
  local_30[13] = '\0';
  local_30[14] = '\0';
  local_30[15] = '\0';
  iVar1 = mxmlNewXML("1.0");
  if (iVar1 == 0) {
    printf("Create new xml erro!!!");
    return 0;
  }
  iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
  if (iVar2 == 0) {
    mxmlDelete(iVar1);
    puts("soap_env=NULL");
    return 0;
  }
  mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
  mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
  mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
  iVar2 = mxmlNewElement(iVar2,"soap:Body");
  if (iVar2 == 0) {
    mxmlDelete(iVar1);
    puts("body=NULL");
    return 0;
  }
  iVar2 = mxmlNewElement(iVar2,"GetWanSettingsResponse");
  if (iVar2 == 0) {
    mxmlDelete(iVar1);
    puts("GetWanSettingsResponse=NULL");
    return 0;
  }
  mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
  iVar3 = apmib_get("h",&local_18);
  if (iVar3 == 0) {
    puts("get WanType is error!");
  }
  else {
    if (local_18 == 0) {
      snprintf(&local_338,7,"Static");
      local_374 = 157;
    }
    else if (local_18 == 1) {
      snprintf(&local_338,5,"DHCP");
      local_374 = 158;
    }
    else {
      if (local_18 != 3) {
        puts("Get connectType not is static / dhcp /...5!");
        goto LAB_0044aa34;
      }
      apmib_get(0x1b66,&local_44);
      if (local_44.s_addr == '\0') {
        snprintf(&local_338,10,"DHCPPPPoE");
        iVar3 = FUN_00449a08(&local_98," ");
        if (iVar3 == 0) {
          puts("error, get wan pppoe type!");
        }
      }
      else {
        snprintf(&local_338,31,"StaticPPPoE");
      }
    }
    iVar3 = apmib_get(197,&local_198);
    if (iVar3 != 0) {
      __stream = fopen("/tmp/PppoeStatus","r");
      if (__stream != 0) {
        fgets(local_4c,8,__stream);
        fclose(__stream);
      }
      iVar3 = apmib_get("i",&local_14);
      if (iVar3 == 0) {
        puts("Get DNSType is error!");
      }
      else {
        if (local_14 == 0) {
          snprintf(&local_d8,6,"false");
        }
        else {
          snprintf(&local_d8,5,"true");
        }
        iVar3 = apmib_get(176,&local_20);
        if (iVar3 == 0) {
          puts("get dns1 is error!");
        }
        else {
          pcVar4 = inet_ntoa(local_20);
          if (pcVar4 == 0) {
            puts("get strConfigPrimary is error!");
          }
          else {
            snprintf(&local_1d8," ","%s",pcVar4);
            iVar3 = apmib_get(177,&local_1c);
            if (iVar3 == 0) {
              puts("get dns2 is error!");
            }
            else {
              pcVar4 = inet_ntoa(local_1c);
              if (pcVar4 == 0) {
                puts("get strConfigSecondary is error!");
              }
              else {
                snprintf(local_1b8," ","%s",pcVar4);
                iVar3 = FUN_0042d91c(local_368);
                if (iVar3 < 1) {
                  local_384 = &local_1d8;
                  local_388 = local_1b8;
                }
                else if (iVar3 == 1) {
                  local_384 = local_368;
                  local_388 = "";
                }
                else if (1 < iVar3) {
                  local_384 = local_368;
                  local_388 = local_368 + 16;
                }
                iVar3 = FUN_0042d4c8(local_238,&local_218,local_1f8,&local_178);
                if (iVar3 < 0) {
                  puts("get ip/mask/gateway/mac is error!");
                }
                else {
                  iVar3 = strncmp(&local_338,"StaticPPPoE",11);
                  if (iVar3 == 0) {
                    pcVar4 = inet_ntoa(local_44);
                    strcpy(local_238,pcVar4);
                  }
                  iVar3 = apmib_get(0x1b5e,local_10);
                  if (iVar3 == 0) {
                    puts("get mac clone is error!");
                  }
                  else {
                    if (local_10[0] == 0) {
                      snprintf(local_138,6,"false");
                    }
                    else if (local_10[0] == 1) {
                      snprintf(local_138,5,"true");
                      snprintf(local_b8,5,"Auto");
                    }
                    else {
                      if (local_10[0] != 2) {
                        puts("macCloneType not is normal / auto / manual");
                        goto LAB_0044aa34;
                      }
                      snprintf(local_138,5,"true");
                      snprintf(local_b8,7,"Manual");
                    }
                    pcVar4 = strstr(&local_338,"PPPoE");
                    if (pcVar4 != 0) {
                      local_374 = 144;
                    }
                    iVar3 = apmib_get("j",&local_318);
                    if (iVar3 == 0) {
                      puts("get strUserName is error!");
                    }
                    else {
                      iVar3 = apmib_get("k",acStack_2f8);
                      if (iVar3 == 0) {
                        puts("get strPassword is error!");
                      }
                      else {
                        pcVar4 = FUN_0045ccd8(acStack_2f8);
                        strcpy(acStack_2f8,pcVar4);
                        iVar3 = apmib_get("l",&local_36c);
                        if (iVar3 == 0) {
                          puts("get strMaxIdleTime is error!");
                        }
                        else {
                          iVar3 = apmib_get(261,&local_258);
                          if (iVar3 == 0) {
                            puts("get strServiceName is error!");
                          }
                          else {
                            iVar3 = apmib_get(local_374,&local_370);
                            if (iVar3 == 0) {
                              puts("get MTU is error!");
                            }
                            else {
                              iVar3 = apmib_get(0x1b7d,local_30);
                              if (iVar3 == 0) {
                                puts("apmib get MIB_WAN_SPEED_TYPE is error!");
                              }
                              else {
                                iVar3 = strncmp(local_30,"Auto",5);
                                if (iVar3 == 0) {
                                  snprintf(local_f8,31,"Auto");
                                  snprintf(local_78,31,"Auto");
                                }
                                else {
                                  iVar3 = strncmp(local_30,"10_half",8);
                                  if (iVar3 == 0) {
                                    snprintf(local_f8,31,"10");
                                    snprintf(local_78,31,"Half");
                                  }
                                  else {
                                    iVar3 = strncmp(local_30,"10_full",8);
                                    if (iVar3 == 0) {
                                      snprintf(local_f8,31,"10");
                                      snprintf(local_78,31,"Full");
                                    }
                                    else {
                                      iVar3 = strncmp(local_30,"100_half",9);
                                      if (iVar3 == 0) {
                                        snprintf(local_f8,31,"100");
                                        snprintf(local_78,31,"Half");
                                      }
                                      else {
                                        iVar3 = strncmp(local_30,"100_full",9);
                                        if (iVar3 == 0) {
                                          snprintf(local_f8,31,"100");
                                          snprintf(local_78,31,"Full");
                                        }
                                        else {
                                          puts("get wan speed type is error!");
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
LAB_0044aa34:
  iVar3 = mxmlNewElement(iVar2,"GetWanSettingsResult");
  if (iVar3 == 0) {
    mxmlDelete(iVar1);
    puts("GetWanSettingsResult=NULL");
  }
  else {
    mxmlNewText(iVar3,0,FUN_004ad49c);
    iVar3 = mxmlNewElement(iVar2,"Type");
    if (iVar3 == 0) {
      mxmlDelete(iVar1);
      puts("Type=NULL");
    }
    else {
      mxmlNewText(iVar3,0,&local_338);
      iVar3 = mxmlNewElement(iVar2,"PppoeType");
      if (iVar3 == 0) {
        mxmlDelete(iVar1);
        puts("PppoeType=NULL");
      }
      else {
        mxmlNewText(iVar3,0,&local_98);
        iVar3 = mxmlNewElement(iVar2,"PppoeStatus");
        if (iVar3 == 0) {
          mxmlDelete(iVar1);
          puts("PppoeStatus=NULL");
        }
        else {
          mxmlNewText(iVar3,0,local_4c);
          iVar3 = mxmlNewElement(iVar2,"Username");
          if (iVar3 == 0) {
            mxmlDelete(iVar1);
            puts("Username=NULL");
          }
          else {
            mxmlNewText(iVar3,0,&local_318);
            iVar3 = mxmlNewElement(iVar2,"Password");
            if (iVar3 == 0) {
              mxmlDelete(iVar1);
              puts("Password=NULL");
            }
            else {
              mxmlNewText(iVar3,0,acStack_2f8);
              iVar3 = mxmlNewElement(iVar2,"MaxIdleTime");
              if (iVar3 == 0) {
                mxmlDelete(iVar1);
                puts("MaxIdleTime=NULL");
              }
              else {
                snprintf(local_278,16,"%d",local_36c);
                mxmlNewText(iVar3,0,local_278);
                iVar3 = mxmlNewElement(iVar2,"HostName");
                if (iVar3 == 0) {
                  mxmlDelete(iVar1);
                  puts("HostName=NULL");
                }
                else {
                  mxmlNewText(iVar3,0,&local_198);
                  iVar3 = mxmlNewElement(iVar2,"ServiceName");
                  if (iVar3 == 0) {
                    mxmlDelete(iVar1);
                    puts("ServiceName=NULL");
                  }
                  else {
                    mxmlNewText(iVar3,0,&local_258);
                    iVar3 = mxmlNewElement(iVar2,"IPAddress");
                    if (iVar3 == 0) {
                      mxmlDelete(iVar1);
                      puts("IPAddress=NULL");
                    }
                    else {
                      iVar5 = strncmp(local_238,"0.0.0.0",7);
                      if (iVar5 == 0) {
                        mxmlNewText(iVar3,0,0);
                      }
                      else {
                        mxmlNewText(iVar3,0,local_238);
                      }
                      iVar3 = mxmlNewElement(iVar2,"SubnetMask");
                      if (iVar3 == 0) {
                        mxmlDelete(iVar1);
                        puts("SubnetMask=NULL");
                      }
                      else {
                        iVar5 = strncmp(&local_218,"0.0.0.0",7);
                        if (iVar5 == 0) {
                          mxmlNewText(iVar3,0,0);
                        }
                        else {
                          mxmlNewText(iVar3,0,&local_218);
                        }
                        iVar3 = mxmlNewElement(iVar2,"Gateway");
                        if (iVar3 == 0) {
                          mxmlDelete(iVar1);
                          puts("Gateway=NULL");
                        }
                        else {
                          iVar5 = strncmp(local_1f8,"0.0.0.0",7);
                          if (iVar5 == 0) {
                            mxmlNewText(iVar3,0,0);
                          }
                          else {
                            mxmlNewText(iVar3,0,local_1f8);
                          }
                          iVar3 = mxmlNewElement(iVar2,"DnsManual");
                          if (iVar3 == 0) {
                            mxmlDelete(iVar1);
                            puts("DnsManual=NULL");
                          }
                          else {
                            mxmlNewText(iVar3,0,&local_d8);
                            iVar3 = mxmlNewElement(iVar2,"RuntimeDNS");
                            if (iVar3 == 0) {
                              mxmlDelete(iVar1);
                              puts("RuntimeDNS=NULL");
                            }
                            else {
                              iVar5 = mxmlNewElement(iVar3,"Primary");
                              if (iVar5 == 0) {
                                mxmlDelete(iVar1);
                                puts("Primary=NULL");
                              }
                              else {
                                iVar6 = strncmp(local_384,"0.0.0.0",7);
                                if (iVar6 == 0) {
                                  mxmlNewText(iVar5,0,0);
                                }
                                else {
                                  mxmlNewText(iVar5,0,local_384);
                                }
                                iVar3 = mxmlNewElement(iVar3,"Secondary");
                                if (iVar3 == 0) {
                                  mxmlDelete(iVar1);
                                  puts("Secondary=NULL");
                                }
                                else {
                                  iVar5 = strncmp(local_388,"0.0.0.0",7);
                                  if (iVar5 == 0) {
                                    mxmlNewText(iVar3,0,0);
                                  }
                                  else {
                                    mxmlNewText(iVar3,0,local_388);
                                  }
                                  iVar3 = mxmlNewElement(iVar2,"ConfigDNS");
                                  if (iVar3 == 0) {
                                    mxmlDelete(iVar1);
                                    puts("ConfigDNS=NULL");
                                  }
                                  else {
                                    iVar5 = mxmlNewElement(iVar3,"Primary");
                                    if (iVar5 == 0) {
                                      mxmlDelete(iVar1);
                                      puts("Primary_c=NULL");
                                    }
                                    else {
                                      iVar6 = strncmp(&local_1d8,"0.0.0.0",7);
                                      if (iVar6 == 0) {
                                        mxmlNewText(iVar5,0,0);
                                      }
                                      else {
                                        mxmlNewText(iVar5,0,&local_1d8);
                                      }
                                      iVar3 = mxmlNewElement(iVar3,"Secondary");
                                      if (iVar3 == 0) {
                                        mxmlDelete(iVar1);
                                        puts("Secondary_c=NULL");
                                      }
                                      else {
                                        iVar5 = strncmp(local_1b8,"0.0.0.0",7);
                                        if (iVar5 == 0) {
                                          mxmlNewText(iVar3,0,0);
                                        }
                                        else {
                                          mxmlNewText(iVar3,0,local_1b8);
                                        }
                                        iVar3 = mxmlNewElement(iVar2,"MacAddress");
                                        if (iVar3 == 0) {
                                          mxmlDelete(iVar1);
                                          puts("MacAddress=NULL");
                                        }
                                        else {
                                          mxmlNewText(iVar3,0,&local_178);
                                          iVar3 = mxmlNewElement(iVar2,"MacCloneEnable");
                                          if (iVar3 == 0) {
                                            mxmlDelete(iVar1);
                                            puts("MacCloneEnable=NULL");
                                          }
                                          else {
                                            mxmlNewText(iVar3,0,local_138);
                                            iVar3 = mxmlNewElement(iVar2,"MacCloneType");
                                            if (iVar3 == 0) {
                                              mxmlDelete(iVar1);
                                              puts("MacCloneType=NULL");
                                            }
                                            else {
                                              mxmlNewText(iVar3,0,local_b8);
                                              iVar3 = mxmlNewElement(iVar2,"VPNLocalIPAddress");
                                              if (iVar3 == 0) {
                                                mxmlDelete(iVar1);
                                                puts("VPNLocalIPAddress=NULL");
                                              }
                                              else {
                                                mxmlNewText(iVar3,0,"");
                                                iVar3 = mxmlNewElement(iVar2,"VPNLocalSubnetMask");
                                                if (iVar3 == 0) {
                                                  mxmlDelete(iVar1);
                                                  puts("VPNLocalSubnetMask=NULL");
                                                }
                                                else {
                                                  mxmlNewText(iVar3,0,"");
                                                  iVar3 = mxmlNewElement(iVar2,"VPNLocalGateway");
                                                  if (iVar3 == 0) {
                                                    mxmlDelete(iVar1);
                                                    puts("VPNLocalGateway=NULL");
                                                  }
                                                  else {
                                                    mxmlNewText(iVar3,0,"");
                                                    iVar3 = mxmlNewElement(iVar2,&PTR_0x0055544d);
                                                    if (iVar3 == 0) {
                                                      mxmlDelete(iVar1);
                                                      puts("MTU=NULL");
                                                    }
                                                    else {
                                                      snprintf(&local_118,5,"%d",local_370);
                                                      mxmlNewText(iVar3,0,&local_118);
                                                      iVar3 = mxmlNewElement(iVar2,"WanSpeed");
                                                      if (iVar3 == 0) {
                                                        mxmlDelete(iVar1);
                                                        puts("WanSpeed=NULL");
                                                      }
                                                      else {
                                                        mxmlNewText(iVar3,0,local_f8);
                                                        iVar2 = mxmlNewElement(iVar2,"WanDuplex");
                                                        if (iVar2 == 0) {
                                                          mxmlDelete(iVar1);
                                                          puts("WanDuplex=NULL");
                                                        }
                                                        else {
                                                          mxmlNewText(iVar2,0,local_78);
                                                          if (("" == 0) &&
                                                             (__ptr = mxmlSaveAllocString(
                                                  iVar1,0), __ptr != 0)) {
                                                    FUN_0041ed70("",200,__ptr,
                                                                 "");
                                                    free(__ptr);
                                                  }
                                                  mxmlDelete(iVar1);
                                                  }
                                                  }
                                                  }
                                                  }
                                                }
                                              }
                                            }
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

