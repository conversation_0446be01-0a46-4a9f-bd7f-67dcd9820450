
int FUN_0044cf00(undefined4 param_1)

{
  int iVar1;
  short sVar3;
  char *pcVar2;
  undefined4 uVar4;
  int iVar5;
  code *pcVar6;
  int local_520;
  undefined4 local_51c;
  char local_518;
  undefined auStack_517 [43];
  char local_4ec;
  undefined auStack_4eb [43];
  undefined local_4c0;
  undefined auStack_4bf [43];
  undefined local_494;
  undefined auStack_493 [43];
  undefined local_468;
  undefined auStack_467 [43];
  undefined *local_43c;
  undefined4 local_438;
  undefined *local_434;
  undefined4 local_430;
  undefined *local_42c;
  undefined4 local_428;
  undefined *local_424;
  undefined4 local_420;
  undefined *local_41c;
  undefined4 local_418;
  undefined *local_414;
  undefined4 local_410;
  undefined *local_40c;
  undefined4 local_408;
  undefined *local_404;
  undefined4 local_400;
  undefined *local_3fc;
  undefined4 local_3f8;
  undefined *local_3f4;
  undefined4 local_3f0;
  undefined *local_3ec;
  undefined4 local_3e8;
  undefined *local_3e4;
  undefined4 local_3e0;
  undefined *local_3dc;
  undefined4 local_3d8;
  undefined *local_3d4;
  undefined4 local_3d0;
  undefined *local_3cc;
  undefined4 local_3c8;
  undefined *local_3c4;
  undefined4 local_3c0;
  undefined *local_3bc;
  undefined4 local_3b8;
  undefined *local_3b4;
  undefined4 local_3b0;
  undefined4 local_3ac;
  undefined4 local_3a8;
  char acStack_39c [45];
  undefined auStack_36f [45];
  char acStack_342 [46];
  int local_314;
  undefined auStack_310 [4];
  char acStack_30c [45];
  char acStack_2df [45];
  char acStack_2b2 [65];
  char acStack_271 [65];
  int local_230;
  int local_22c;
  undefined auStack_228 [4];
  undefined auStack_224 [4];
  undefined auStack_220 [45];
  undefined auStack_1f3 [45];
  undefined auStack_1c6 [46];
  undefined auStack_198 [4];
  undefined auStack_194 [4];
  undefined auStack_190 [45];
  undefined auStack_163 [45];
  undefined auStack_136 [65];
  undefined auStack_f5 [65];
  undefined auStack_b4 [4];
  undefined auStack_b0 [4];
  undefined auStack_ac [45];
  undefined auStack_7f [45];
  undefined auStack_52 [46];
  undefined auStack_24 [4];
  undefined auStack_20 [8];
  
  local_518 = '\0';
  local_520 = 0;
  memset(auStack_517,0,"+");
  local_4ec = '\0';
  memset(auStack_4eb,0,"+");
  local_4c0 = 0;
  memset(auStack_4bf,0,"+");
  local_494 = 0;
  memset(auStack_493,0,"+");
  local_468 = 0;
  memset(auStack_467,0,"+");
  local_434 = auStack_224;
  local_42c = auStack_220;
  local_424 = auStack_1f3;
  local_41c = auStack_1c6;
  local_414 = auStack_198;
  local_40c = auStack_194;
  local_404 = auStack_190;
  local_3fc = auStack_163;
  local_3f4 = auStack_136;
  local_3ec = auStack_f5;
  local_3e4 = auStack_b4;
  local_3dc = auStack_b0;
  local_3d4 = auStack_ac;
  local_3cc = auStack_7f;
  local_3c4 = auStack_52;
  local_3bc = auStack_24;
  local_43c = auStack_228;
  local_3b4 = auStack_20;
  local_428 = "-";
  local_420 = "-";
  local_418 = "-";
  local_400 = "-";
  local_3f8 = "-";
  local_3e8 = "@";
  local_3f0 = "@";
  local_51c = 0;
  local_3ac = 0;
  local_438 = 0;
  local_430 = 0;
  local_410 = 0;
  local_408 = 0;
  local_3e0 = 0;
  local_3d8 = 0;
  local_3d0 = "-";
  local_3c0 = "-";
  local_3c8 = "-";
  local_3b8 = 0;
  local_3b0 = 0;
  local_3a8 = 0;
  memset(local_43c,0,524);
  memset(acStack_39c,0,372);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar3 = HttpDenyPage(param_1);
  }
  else {
    swGetDhcpv6Cfg(acStack_39c);
    iVar1 = httpGetEnv(param_1,"Save");
    if ((iVar1 != 0) || (iVar1 = httpGetEnv(param_1,"RenewIp"), iVar1 != 0)) {
      iVar1 = httpGetEnv(param_1,"ipv6Enable");
      if (iVar1 == 0) {
        IPV6_ECHO("ucSetIPv6Enable(FALSE);");
        ucSetIPv6Enable(0);
        uVar4 = 4;
        pcVar6 = setDhcp6cStatus;
      }
      else {
        IPV6_ECHO("ucSetIPv6Enable(TRUE);");
        pcVar6 = ucSetIPv6Enable;
        uVar4 = 1;
      }
      (*pcVar6)(uVar4);
      pcVar2 = httpGetEnv(param_1,"ipType");
      if (pcVar2 != 0) {
        local_230 = atoi(pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"g");
      if (pcVar2 != 0) {
        strcpy(acStack_342,pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"mtu");
      if (pcVar2 != 0) {
        local_314 = atoi(pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"dnsType");
      if (pcVar2 != 0) {
        local_22c = atoi(pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"dnsserver1");
      if (pcVar2 == 0) {
        memset(acStack_30c,0,"-");
      }
      else {
        strcpy(acStack_30c,pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"dnsserver2");
      if (pcVar2 == 0) {
        memset(acStack_2df,0,"-");
      }
      else {
        strcpy(acStack_2df,pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"domain");
      if (pcVar2 != 0) {
        strcpy(acStack_271,pcVar2);
      }
      pcVar2 = httpGetEnv(param_1,"hostname");
      if (pcVar2 != 0) {
        strcpy(acStack_2b2,pcVar2);
      }
      stopCurrentConnection(2);
      swSetDhcpv6Cfg(acStack_39c);
      setUserStop(0);
      swRenewDhcp6cClient();
    }
    iVar1 = httpGetEnv(param_1,"ReleaseIp");
    if (iVar1 != 0) {
      setUserStop(1);
      swReleaseDhcp6cClient();
    }
    LanIpv6RpmHtm(param_1,2);
    local_520 = 2;
    pageParaSet(&local_43c,&local_520,0);
    local_520 = swGetDhcpv6Status(2);
    pageParaSet(&local_43c,&local_520,1);
    strcpy(acStack_39c,"");
    if (local_520 == 1) {
      swGetDhcpv6Ip(&local_518,2);
      pageParaSet(&local_43c,&local_518,2);
      pageParaSet(&local_43c,auStack_36f,3);
      swGetRunningGateway(&local_468,2);
    }
    else {
      pageParaSet(&local_43c,&local_518,2);
      pageParaSet(&local_43c,&local_4ec,3);
    }
    pageParaSet(&local_43c,&local_468,4);
    pageParaSet(&local_43c,&local_314,5);
    pageParaSet(&local_43c,auStack_310,6);
    pageParaSet(&local_43c,acStack_30c,7);
    pageParaSet(&local_43c,acStack_2df,8);
    swGetDhcpv6Host(acStack_2b2);
    pageParaSet(&local_43c,acStack_2b2,9);
    swGetDhcpv6Domain(acStack_271);
    pageParaSet(&local_43c,acStack_271,10);
    pageParaSet(&local_43c,&local_230,11);
    pageParaSet(&local_43c,&local_22c,12);
    swGetDhcpv6Dns(&local_4c0,&local_494,2);
    pageParaSet(&local_43c,&local_4c0,13);
    pageParaSet(&local_43c,&local_494,14);
    swGetDelegatePrefixAndLengthByWanType(&local_518,&local_51c,2);
    if (local_518 == '\0') {
      strcpy(&local_4ec,"");
    }
    else {
      sprintf(&local_4ec,"%s/%d",&local_518,local_51c);
    }
    pageParaSet(&local_43c,&local_4ec,15);
    pageParaSet(&local_43c,&local_520,16);
    local_520 = swGetIPv6Enable();
    pageParaSet(&local_43c,&local_520,17);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "dhcpv6Info");
    iVar1 = 0;
    do {
      iVar5 = iVar1 + 1;
      pageDynParaPrintf(&local_43c,iVar1,param_1);
      iVar1 = iVar5;
    } while (iVar5 != 19);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintfWanIpv6TypeInfo(param_1);
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/WanDynamicIpV6CfgRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar3 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar3;
}

