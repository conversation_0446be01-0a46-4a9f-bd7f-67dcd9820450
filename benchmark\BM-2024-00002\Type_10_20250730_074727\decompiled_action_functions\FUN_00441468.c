
undefined4 FUN_00441468(int param_1)

{
  int iVar1;
  int iVar2;
  char *__s;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetFactoryDefault");
  }
  else {
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar2 = mxmlNewElement(iVar2,"soap:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"SetFactoryDefaultResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("SetFactoryDefaultResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar2 = mxmlNewElement(iVar2,"SetFactoryDefaultResult");
            if (iVar2 == 0) {
              mxmlDelete(iVar1);
              puts("SetFactoryDefaultResult=NULL");
            }
            else {
              mxmlNewText(iVar2,0,"O");
              if ("" == 0) {
                __s = mxmlSaveAllocString(iVar1,0);
                if (__s != 0) {
                  FUN_0042c210();
                  FUN_0042c320(1);
                  FUN_0042c320(3);
                  FUN_004263f0("flash default-sw",0,0);
                  puts(__s);
                  FUN_0041ed70("",200,__s,"");
                  free(__s);
                  FUN_004263f0("reboot",0,0);
                }
              }
              mxmlDelete(iVar1);
            }
          }
        }
      }
    }
  }
  return 0;
}

