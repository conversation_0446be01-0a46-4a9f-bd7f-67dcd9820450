
int FUN_004576ec(undefined4 param_1)

{
  int iVar1;
  short sVar10;
  char *pcVar2;
  uint uVar3;
  uint uVar4;
  uint32_t uVar5;
  uint32_t uVar6;
  uint32_t uVar7;
  uint32_t uVar8;
  uint uVar9;
  undefined4 uVar11;
  uint uVar12;
  uint local_110;
  char acStack_10c [16];
  undefined auStack_fc [4];
  undefined auStack_f8 [4];
  undefined auStack_f4 [4];
  undefined auStack_f0 [4];
  undefined auStack_ec [4];
  in_addr_t local_e8;
  in_addr_t local_e4;
  in_addr_t local_e0;
  uint local_dc;
  undefined *local_c8;
  undefined4 local_c4;
  undefined *local_c0;
  undefined4 local_bc;
  undefined *local_b8;
  undefined4 local_b4;
  undefined *local_b0;
  undefined4 local_ac;
  undefined4 local_a8;
  undefined *local_a0;
  undefined4 local_9c;
  undefined *local_98;
  undefined4 local_94;
  undefined *local_90;
  undefined4 local_8c;
  undefined *local_88;
  undefined4 local_84;
  undefined *local_80;
  undefined4 local_7c;
  undefined4 local_78;
  undefined auStack_70 [16];
  undefined auStack_60 [16];
  undefined auStack_50 [16];
  undefined auStack_40 [8];
  int local_38;
  int local_34;
  in_addr_t *local_30;
  uint *local_2c;
  
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar10 = HttpDenyPage(param_1);
LAB_00458220:
    iVar1 = sVar10;
  }
  else {
    iVar1 = httpGetEnv(param_1,"Add");
    if (iVar1 == 0) {
      iVar1 = httpGetEnv(param_1,"Modify");
      local_a0 = auStack_fc;
      if (iVar1 == 0) {
        local_98 = auStack_f8;
        local_90 = auStack_f4;
        local_88 = auStack_f0;
        local_80 = auStack_ec;
        local_c8 = auStack_70;
        local_c0 = auStack_60;
        local_b8 = auStack_50;
        local_b0 = auStack_40;
        local_b4 = 16;
        local_78 = 0;
        local_9c = 0;
        local_94 = 0;
        local_8c = 0;
        local_84 = 0;
        local_7c = 0;
        local_a8 = 0;
        local_c4 = 16;
        local_bc = 16;
        local_ac = 0;
        pcVar2 = httpGetEnv(param_1,"Page");
        uVar12 = 1;
        if (pcVar2 != 0) {
          uVar12 = atoi(pcVar2);
          uVar3 = getStaticRouteTblSize();
          if ((uVar3 >> 3 < uVar12) || (uVar12 < 1)) {
            uVar12 = 1;
          }
        }
        memset(&local_e8,0," ");
        pcVar2 = httpGetEnv(param_1,"Add");
        if (pcVar2 == 0) {
          pcVar2 = httpGetEnv(param_1,"doAll");
          if (pcVar2 != 0) {
            iVar1 = strcmp(pcVar2,"EnAll");
            if (iVar1 == 0) {
              uVar11 = 1;
            }
            else {
              iVar1 = strcmp(pcVar2,"DisAll");
              if (iVar1 == 0) {
                uVar11 = 2;
              }
              else {
                iVar1 = strcmp(pcVar2,"DelAll");
                uVar11 = 3;
                if (iVar1 != 0) goto LAB_00457914;
              }
            }
            swSetStaticRouteTblCfgAll(uVar11);
            goto LAB_00457914;
          }
          pcVar2 = httpGetEnv(param_1,"Del");
          if (pcVar2 != 0) {
            iVar1 = atoi(pcVar2);
            swDelStaticRouteTblEntryCfg(iVar1);
            goto LAB_00457914;
          }
          iVar1 = httpGetEnv(param_1,"Save");
          if (iVar1 == 0) goto LAB_00457914;
          memset(&local_e8,0," ");
          pcVar2 = httpGetEnv(param_1,"Ip");
          if (pcVar2 == 0) {
LAB_00457aac:
            pcVar2 = httpGetEnv(param_1,"Mask");
            if (pcVar2 != 0) {
              iVar1 = swChkDotIpAddr(pcVar2);
              if (iVar1 == 0) {
                iVar1 = 0xfa2;
                goto LAB_00458214;
              }
              local_e4 = inet_addr(pcVar2);
            }
            pcVar2 = httpGetEnv(param_1,"Gateway");
            if (pcVar2 != 0) {
              iVar1 = swChkDotIpAddr(pcVar2);
              if (iVar1 == 0) {
                iVar1 = 0xfa4;
                goto LAB_00458214;
              }
              local_e0 = inet_addr(pcVar2);
            }
            pcVar2 = httpGetEnv(param_1,"State");
            if (pcVar2 != 0) {
              iVar1 = atoi(pcVar2);
              local_dc = (uint)(iVar1 != 0);
            }
            iVar1 = swChkStaticRouteEntry(&local_e8);
            if (iVar1 == 0) {
              pcVar2 = httpGetEnv(param_1,"Changed");
              if ((pcVar2 == 0) || (iVar1 = strcmp(pcVar2,"1"), iVar1 != 0)) {
                iVar1 = swAddStaticRouteTblEntryCfg(&local_e8);
              }
              else {
                pcVar2 = httpGetEnv(param_1,"SelIndex");
                iVar1 = 0;
                if (pcVar2 != 0) {
                  iVar1 = atoi(pcVar2);
                }
                iVar1 = swModifyStaticRouteTblEntryCfg(iVar1,&local_e8);
              }
              if (iVar1 == 0) goto LAB_00457914;
            }
          }
          else {
            iVar1 = swChkDotIpAddr(pcVar2);
            if (iVar1 != 0) {
              local_e8 = inet_addr(pcVar2);
              goto LAB_00457aac;
            }
            iVar1 = 0xfa1;
          }
        }
        else {
          iVar1 = strcmp("Add",pcVar2);
          if (iVar1 == 0) {
            local_dc = 1;
          }
LAB_00457914:
          for (uVar3 = 0; uVar4 = getStaticRouteTblSize(), uVar3 < uVar4; uVar3 = uVar3 + 1) {
            memset(&local_e8,0," ");
            iVar1 = swGetStaticRouteTblEntryCfg(uVar3,&local_e8);
            if (iVar1 != 0) break;
          }
          httpPrintf(param_1,
                     "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n"
                     ,"routeList");
          iVar1 = swGetStaticRouteTblEntryCfg((uVar12 - 1) * 8,&local_e8);
          if ((iVar1 == 0) || (local_38 = uVar12 - 2, uVar3 = uVar12 - 1, uVar12 == 1)) {
            local_38 = uVar12 - 1;
            uVar3 = uVar12;
          }
          local_38 = local_38 << 3;
          local_34 = uVar3 << 3;
          local_30 = &local_e8;
          local_2c = &local_110;
          uVar12 = 0;
          uVar4 = 0;
          while (uVar9 = getStaticRouteTblSize(), uVar12 < uVar9) {
            memset(local_30,0," ");
            iVar1 = swGetStaticRouteTblEntryCfg(uVar12,local_30);
            if (iVar1 != 0) break;
            uVar12 = uVar12 + 1;
            if (local_38 < uVar12) {
              if (local_34 < uVar12) break;
              uVar4 = uVar4 + 1;
              uVar5 = ntohl(local_e8);
              uVar6 = ntohl(local_e8);
              uVar7 = ntohl(local_e8);
              uVar8 = ntohl(local_e8);
              sprintf(acStack_10c,"%d.%d.%d.%d",uVar5 >> 24,(uVar6 & 0xff0000) >> 16,
                      (int)(uVar7 & -256) >> 8,uVar8 & 255);
              pageParaSet(&local_c8,acStack_10c,0);
              uVar5 = ntohl(local_e4);
              uVar6 = ntohl(local_e4);
              uVar7 = ntohl(local_e4);
              uVar8 = ntohl(local_e4);
              sprintf(acStack_10c,"%d.%d.%d.%d",uVar5 >> 24,(uVar6 & 0xff0000) >> 16,
                      (int)(uVar7 & -256) >> 8,uVar8 & 255);
              pageParaSet(&local_c8,acStack_10c,1);
              uVar5 = ntohl(local_e0);
              uVar6 = ntohl(local_e0);
              uVar7 = ntohl(local_e0);
              uVar8 = ntohl(local_e0);
              sprintf(acStack_10c,"%d.%d.%d.%d",uVar5 >> 24,(uVar6 & 0xff0000) >> 16,
                      (int)(uVar7 & -256) >> 8,uVar8 & 255);
              pageParaSet(&local_c8,acStack_10c,2);
              local_110 = local_dc;
              pageParaSet(&local_c8,local_2c,3);
              pageDynParaListPrintf(&local_c8,param_1);
            }
          }
          httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
          local_110 = uVar3;
          pageParaSet(&local_a0,&local_110,0);
          local_110 = (uint)(local_34 < uVar12);
          pageParaSet(&local_a0,&local_110,1);
          local_110 = uVar4;
          pageParaSet(&local_a0,&local_110,2);
          local_110 = 4;
          pageParaSet(&local_a0,&local_110,3);
          local_110 = 8;
          pageParaSet(&local_a0,&local_110,4);
          httpPrintf(param_1,
                     "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n"
                     ,"SRTPara");
          pageDynParaPrintf(&local_a0,0,param_1);
          pageDynParaPrintf(&local_a0,1,param_1);
          pageDynParaPrintf(&local_a0,2,param_1);
          pageDynParaPrintf(&local_a0,3,param_1);
          pageDynParaPrintf(&local_a0,4,param_1);
          httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
          HttpWebV4Head(param_1,0,1);
          iVar1 = httpRpmFsA(param_1,"/userRpm/StaticRouteTableRpm.htm");
          if (iVar1 == 2) {
            return 2;
          }
          iVar1 = 10;
        }
LAB_00458214:
        sVar10 = HttpErrorPage(param_1,iVar1,0,0);
        goto LAB_00458220;
      }
    }
    iVar1 = FUN_00458260(param_1);
  }
  return iVar1;
}

