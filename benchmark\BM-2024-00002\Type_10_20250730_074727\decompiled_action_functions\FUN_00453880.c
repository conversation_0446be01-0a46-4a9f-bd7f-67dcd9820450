
undefined4 FUN_00453880(void)

{
  char *pcVar1;
  int iVar2;
  undefined4 local_168;
  char acStack_15c [100];
  undefined auStack_f8 [64];
  undefined auStack_b8 [4];
  undefined auStack_b4 [4];
  undefined auStack_b0 [4];
  undefined auStack_ac [4];
  in_addr_t local_a8;
  uint local_a4;
  uint local_a0;
  uint local_9c;
  uint local_98;
  undefined auStack_94 [64];
  undefined auStack_54 [72];
  
  memset(acStack_15c,0,100);
  memset(auStack_f8,0,"@");
  memset(auStack_94,0,"@");
  memset(auStack_54,0,"@");
  local_168 = 0;
  FUN_00452c20("br0",2,auStack_b8);
  FUN_00452c20("br0",4,auStack_b4);
  FUN_00452c20("eth1",2,auStack_b0);
  FUN_00452c20("eth1",4,auStack_ac);
  memcpy(&local_a4,auStack_b8,4);
  memcpy(&local_a0,auStack_b4,4);
  memcpy(&local_9c,auStack_b0,4);
  memcpy(&local_98,auStack_ac,4);
  if ((local_a4 & local_a0) == (local_9c & local_98)) {
    local_a8 = local_a4 & local_a0;
    pcVar1 = inet_ntoa(local_a8);
    sprintf(acStack_15c,"%s\n",pcVar1);
    iVar2 = strncmp(acStack_15c,"192.168.0.0",11);
    if (iVar2 == 0) {
      local_168 = 1;
    }
    else {
      iVar2 = strncmp(acStack_15c,"192.168.100.0",13);
      if (iVar2 == 0) {
        local_168 = 2;
      }
      else {
        local_168 = 3;
      }
    }
  }
  if ("" == 0) {
    FUN_004532d8(local_168);
  }
  return local_168;
}

