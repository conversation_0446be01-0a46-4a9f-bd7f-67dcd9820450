
int FUN_00446a5c(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  short sVar5;
  char *pcVar3;
  size_t sVar4;
  undefined1 *puVar6;
  undefined4 local_128;
  undefined auStack_124 [16];
  undefined auStack_114 [4];
  undefined auStack_110 [4];
  undefined auStack_10c [4];
  undefined *local_108;
  undefined4 local_104;
  undefined *local_100;
  undefined4 local_fc;
  undefined *local_f8;
  undefined4 local_f4;
  undefined *local_f0;
  undefined4 local_ec;
  undefined4 local_e8;
  undefined auStack_e0 [49];
  char acStack_af [16];
  char acStack_9f [35];
  char acStack_7c [15];
  undefined local_6d;
  undefined auStack_6c [33];
  char acStack_4b [15];
  undefined local_3c;
  char acStack_3b [39];
  
  local_108 = auStack_124;
  local_100 = auStack_114;
  local_f8 = auStack_110;
  local_f0 = auStack_10c;
  local_104 = 16;
  local_e8 = 0;
  local_fc = 0;
  local_f4 = 0;
  local_ec = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    sVar5 = HttpDenyPage(param_1);
    goto LAB_00446f44;
  }
  memset(acStack_7c,0,"b");
  iVar2 = httpGetEnv(param_1,"Save");
  if (iVar2 == 0) {
LAB_00446d4c:
    memset(acStack_7c,0,"b");
    swGetPasswordCfg(acStack_7c);
    pageParaSet(&local_108,acStack_4b,0);
    local_128 = 0;
    pageParaSet(&local_108,&local_128,1);
    local_128 = 1;
    pageParaSet(&local_108,&local_128,2);
    iVar2 = HttpIsAccessFromLAN(param_1);
    if ((iVar2 == 0) && (iVar2 = getForbiddenWanUpgrade(), iVar2 != 0)) {
      local_128 = 1;
    }
    else {
      local_128 = 0;
    }
    pageParaSet(&local_108,&local_128,3);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "LoginPwdInf");
    pageDynParaPrintf(&local_108,0,param_1);
    pageDynParaPrintf(&local_108,1,param_1);
    pageDynParaPrintf(&local_108,2,param_1);
    pageDynParaPrintf(&local_108,3,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar2 = httpRpmFsA(param_1,"/userRpm/ChangeLoginPwdRpm.htm");
    if (iVar2 == 2) {
      return 2;
    }
    iVar2 = 10;
    puVar6 = 0;
  }
  else {
    swGetPasswordCfg(auStack_e0);
    pcVar3 = httpGetEnv(param_1,"oldname");
    if (pcVar3 != 0) {
      local_6d = 0;
      strncpy(acStack_7c,pcVar3,15);
    }
    pcVar3 = httpGetEnv(param_1,"newname");
    if (pcVar3 != 0) {
      local_3c = 0;
      strncpy(acStack_4b,pcVar3,15);
    }
    pcVar3 = httpGetEnv(param_1,"oldpassword");
    if (pcVar3 != 0) {
      sVar4 = strlen(pcVar3);
      b64_decode(auStack_6c," ",pcVar3,sVar4);
    }
    pcVar3 = httpGetEnv(param_1,"newpassword");
    if (pcVar3 != 0) {
      sVar4 = strlen(pcVar3);
      b64_decode(acStack_3b," ",pcVar3,sVar4);
    }
    sVar5 = swChkPasswordCfg(acStack_7c);
    iVar2 = sVar5;
    if (iVar2 == 0) {
      iVar2 = strcmp(acStack_af,acStack_4b);
      bVar1 = true;
      if (iVar2 == 0) {
        iVar2 = strcmp(acStack_9f,acStack_3b);
        bVar1 = iVar2 != 0;
      }
      swSetPasswordCfg(acStack_7c);
      httpAdminPwconf();
      if (bVar1) {
        httpSessionInit();
        sVar5 = GoIndex(param_1,1);
        goto LAB_00446f44;
      }
      goto LAB_00446d4c;
    }
    puVar6 = "";
  }
  sVar5 = HttpErrorPage(param_1,iVar2,puVar6,0);
LAB_00446f44:
  return sVar5;
}

