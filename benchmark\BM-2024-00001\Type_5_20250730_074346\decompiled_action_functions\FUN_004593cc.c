
int FUN_004593cc(undefined4 param_1)

{
  int *piVar1;
  int iVar2;
  short sVar3;
  undefined4 local_30;
  undefined4 local_2c;
  undefined4 *local_28;
  undefined4 local_24;
  undefined4 *local_20;
  undefined4 local_1c;
  undefined4 local_18;
  
  local_30 = 0xffffffff;
  local_28 = &local_30;
  local_20 = &local_2c;
  local_2c = 1;
  local_18 = 0;
  local_24 = 0;
  local_1c = 0;
  piVar1 = swGetSendStatus();
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    sVar3 = HttpDenyPage(param_1);
  }
  else {
    iVar2 = httpGetEnv(param_1,"doMailLog");
    if (iVar2 == 0) {
      if (*piVar1 == 0) {
        local_30 = 0;
        local_2c = 0;
      }
      else {
        local_30 = 2;
        local_2c = swGetSendResult();
      }
    }
    else if (*piVar1 == 0) {
      local_30 = 1;
    }
    else {
      *piVar1 = 0;
      swManuSendMail();
      local_30 = 0;
    }
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "mailResult");
    pageDynParaListPrintf(&local_28,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar2 = httpRpmFsA(param_1,"/userRpm/MailResultRpm.htm");
    if (iVar2 == 2) {
      return 2;
    }
    sVar3 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar3;
}

