
undefined4 FUN_00465d28(int param_1)

{
  undefined4 uVar1;
  int iVar2;
  int iVar3;
  int iVar4;
  void *__ptr;
  int local_24;
  undefined4 local_20;
  char local_1c [20];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetGuestWLanSettings");
    uVar1 = 0;
  }
  else {
    local_24 = 0;
    local_20 = 0;
    local_1c[0] = '\0';
    local_1c[1] = '\0';
    local_1c[2] = '\0';
    local_1c[3] = '\0';
    local_1c[4] = '\0';
    local_1c[5] = '\0';
    local_1c[6] = '\0';
    local_1c[7] = '\0';
    local_1c[8] = '\0';
    local_1c[9] = '\0';
    local_1c[10] = '\0';
    local_1c[11] = '\0';
    local_1c[12] = '\0';
    local_1c[13] = '\0';
    local_1c[14] = '\0';
    local_1c[15] = '\0';
    local_1c[16] = '\0';
    local_1c[17] = '\0';
    local_1c[18] = '\0';
    local_1c[19] = '\0';
    iVar2 = mxmlNewXML("1.0");
    if (iVar2 == 0) {
      puts("xml=NULL");
      uVar1 = 0;
    }
    else {
      iVar3 = mxmlNewElement(iVar2,"soap:Envelope");
      if (iVar3 == 0) {
        mxmlDelete(iVar2);
        puts("soap_env=NULL");
        uVar1 = 0;
      }
      else {
        mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar3 = mxmlNewElement(iVar3,"soap:Body");
        if (iVar3 == 0) {
          mxmlDelete(iVar2);
          puts("body=NULL");
          uVar1 = 0;
        }
        else {
          iVar3 = mxmlNewElement(iVar3,"GetGuestWLanSettingsResponse");
          if (iVar3 == 0) {
            mxmlDelete(iVar2);
            puts("GetGuestWLanSettingsResponse_xml=NULL");
            uVar1 = 0;
          }
          else {
            mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
            iVar4 = mxmlNewElement(iVar3,"GetGuestWLanSettingsResult");
            if (iVar4 == 0) {
              mxmlDelete(iVar2);
              puts("GetGuestWLanSettingsResult_xml=NULL");
              uVar1 = 0;
            }
            else {
              mxmlNewText(iVar4,0,"O");
              iVar4 = mxmlNewElement(iVar3,"LocalAccess");
              if (iVar4 == 0) {
                mxmlDelete(iVar2);
                puts("LocalAccess_xml=NULL");
                uVar1 = 0;
              }
              else {
                apmib_get(0x1b77,&local_24);
                if (local_24 == 0) {
                  mxmlNewText(iVar4,0,"false");
                }
                else {
                  mxmlNewText(iVar4,0,"true");
                }
                iVar3 = mxmlNewElement(iVar3,"LocalAccessTimeout");
                if (iVar3 == 0) {
                  mxmlDelete(iVar2);
                  puts("LocalAccessTimeout_xml=NULL");
                  uVar1 = 0;
                }
                else {
                  apmib_get(0x1b78,&local_20);
                  snprintf(local_1c,19,"%d",local_20);
                  mxmlNewText(iVar3,0,local_1c);
                  __ptr = mxmlSaveAllocString(iVar2,0);
                  if (__ptr == 0) {
                    puts("retstring=NULL");
                  }
                  else {
                    FUN_0041ed70("",200,__ptr,"");
                    free(__ptr);
                  }
                  uVar1 = mxmlDelete(iVar2);
                }
              }
            }
          }
        }
      }
    }
  }
  return uVar1;
}

