
int AccessTargetsRpmHtm(undefined4 param_1)

{
  in_addr_t iVar1;
  in_addr_t iVar2;
  char cVar3;
  ushort uVar4;
  ushort uVar5;
  int iVar6;
  short sVar13;
  uint uVar7;
  undefined4 uVar8;
  char *pcVar9;
  int iVar10;
  size_t sVar11;
  uint uVar12;
  char *pcVar14;
  char *__s;
  uint local_288;
  uint local_284;
  char acStack_280 [8];
  char local_278 [15];
  undefined local_269;
  undefined auStack_268 [4];
  undefined auStack_264 [4];
  undefined auStack_260 [4];
  undefined auStack_25c [4];
  undefined auStack_258 [4];
  char acStack_254 [32];
  int *local_234;
  undefined4 local_230;
  undefined *local_22c;
  undefined4 local_228;
  char *local_224;
  undefined4 local_220;
  char *local_21c;
  undefined4 local_218;
  undefined4 local_214;
  undefined *local_20c;
  undefined4 local_208;
  undefined *local_204;
  undefined4 local_200;
  undefined *local_1fc;
  undefined4 local_1f8;
  undefined *local_1f4;
  undefined4 local_1f0;
  undefined *local_1ec;
  undefined4 local_1e8;
  undefined4 local_1e4;
  undefined4 local_1dc;
  int local_1d8;
  char acStack_1d4 [24];
  undefined local_1bc;
  in_addr_t local_1b8;
  in_addr_t local_1b4;
  char local_1b0;
  ushort local_1ae;
  ushort local_1ac;
  char local_1aa [124];
  char acStack_12e [2];
  int local_12c;
  undefined auStack_128 [25];
  char acStack_10f [60];
  char local_d3 [147];
  int local_40;
  uint local_3c;
  uint local_38;
  uint local_34;
  uint local_30;
  int local_2c;
  
  local_288 = 0;
  swGetAccessTargetsTableSize();
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar6 = HttpAccessPermit(param_1);
  if (iVar6 == 0) {
    sVar13 = HttpDenyPage(param_1);
    goto LAB_004237d4;
  }
  iVar6 = httpGetEnv(param_1,"Add");
  if ((iVar6 != 0) || (iVar6 = httpGetEnv(param_1,"Modify"), iVar6 != 0)) {
    iVar6 = FUN_0042254c(param_1);
    return iVar6;
  }
  memset(&local_1dc,0,176);
  local_20c = auStack_268;
  local_204 = auStack_264;
  local_1fc = auStack_260;
  local_1f4 = auStack_25c;
  local_1ec = auStack_258;
  local_234 = &local_12c;
  local_22c = auStack_128;
  local_224 = acStack_10f;
  local_21c = local_d3;
  local_228 = 25;
  local_220 = "<";
  local_218 = 140;
  local_1e4 = 0;
  local_208 = 0;
  local_200 = 0;
  local_1f8 = 0;
  local_1f0 = 0;
  local_1e8 = 0;
  local_214 = 0;
  local_230 = 0;
  uVar7 = getEnvToInt(param_1,"Page",1,0x7fffffff);
  uVar12 = 1;
  if (uVar7 != 0xffffff80) {
    uVar12 = uVar7;
  }
  uVar8 = swGetAccessTargetsTableSize();
  getEnvToInt(param_1,"EntryIndex",0,uVar8);
  pcVar9 = httpGetEnv(param_1,"doAll");
  if (pcVar9 == 0) {
    pcVar9 = httpGetEnv(param_1,"Del");
    if (pcVar9 != 0) {
      iVar6 = atoi(pcVar9);
      uVar8 = swDelFilterEntry(1,(uVar12 - 1) * 8 + iVar6,1);
      goto LAB_004231d0;
    }
    iVar6 = httpGetEnv(param_1,"Save");
    if (iVar6 == 0) goto LAB_004231cc;
    memset(&local_1dc,0,176);
    local_1dc = 1;
    iVar6 = getEnvToInt(param_1,"target_type",0,1);
    local_1d8 = iVar6;
    pcVar9 = httpGetEnv(param_1,"targets_lists_name");
    if (pcVar9 != 0) {
      local_1bc = 0;
      strncpy(acStack_1d4,pcVar9,24);
    }
    if (iVar6 != 1) {
      if (iVar6 == 0) {
        pcVar9 = local_1aa;
        iVar6 = 0;
        do {
          sprintf(acStack_280,"url_%d",iVar6);
          pcVar14 = httpGetEnv(param_1,acStack_280);
          if (pcVar14 != 0) {
            do {
              __s = pcVar14;
              pcVar14 = __s + 1;
            } while (*__s == ' ');
            if (__s != 0) {
              sVar11 = strlen(__s);
              if (30 < sVar11) {
                iVar6 = 9000;
                goto LAB_004237c8;
              }
              iVar10 = swChkLegalDomain(__s);
              if (iVar10 == 0) {
                iVar6 = 0x2329;
                goto LAB_004237c8;
              }
              pcVar9[30] = '\0';
              strncpy(pcVar9,__s,30);
            }
          }
          iVar6 = iVar6 + 1;
          pcVar9 = pcVar9 + 31;
        } while (iVar6 != 4);
      }
LAB_00423128:
      pcVar9 = httpGetEnv(param_1,"Changed");
      iVar6 = strcmp(pcVar9,"1");
      if (iVar6 == 0) {
        pcVar9 = httpGetEnv(param_1,"SelIndex");
        iVar6 = 4;
        if (pcVar9 != 0) {
          iVar6 = atoi(pcVar9);
        }
        uVar8 = 1;
      }
      else {
        iVar6 = 0;
        uVar8 = 0;
      }
      uVar8 = swSetAccessTargetsEntry(&local_1dc,iVar6,uVar8,1);
      goto LAB_004231d0;
    }
    pcVar9 = httpGetEnv(param_1,"dst_ip_start");
    if (pcVar9 == 0) {
LAB_00422e20:
      pcVar9 = httpGetEnv(param_1,"dst_ip_end");
      if (pcVar9 != 0) {
        iVar6 = swChkDotIpAddr(pcVar9);
        if (iVar6 == 0) goto LAB_00422e5c;
        local_278[0] = '\0';
        local_278[1] = '\0';
        local_278[2] = '\0';
        local_278[3] = '\0';
        local_269 = 0;
        strncpy(local_278,pcVar9,15);
        local_1b4 = inet_addr(local_278);
      }
      if ((local_1b8 == 0) || (local_1b4 == 0)) {
        local_1b8 = local_1b8 + local_1b4;
        local_1b4 = local_1b8;
      }
      pcVar9 = httpGetEnv(param_1,"proto");
      if (pcVar9 != 0) {
        iVar6 = atoi(pcVar9);
        local_1b0 = iVar6;
      }
      if (local_1b0 == '\x03') goto LAB_00423128;
      pcVar9 = httpGetEnv(param_1,"dst_port_start");
      if (pcVar9 == 0) {
LAB_00422f74:
        pcVar9 = httpGetEnv(param_1,"dst_port_end");
        if (pcVar9 != 0) {
          iVar6 = atoi(pcVar9);
          if (-2 < iVar6 - 1U) goto LAB_00422fc0;
          local_1ac = iVar6;
        }
        if ((local_1ae == 0) || (local_1ac == 0)) {
          local_1ae = local_1ac + local_1ae;
          local_1ac = local_1ae;
        }
        uVar4 = local_1ae;
        if (local_1ac < local_1ae) {
          local_1ae = local_1ac;
          local_1ac = uVar4;
        }
        goto LAB_00423128;
      }
      iVar6 = atoi(pcVar9);
      if (iVar6 - 1U < -1) {
        local_1ae = iVar6;
        goto LAB_00422f74;
      }
LAB_00422fc0:
      iVar6 = 0x1f44;
    }
    else {
      iVar6 = swChkDotIpAddr(pcVar9);
      if (iVar6 != 0) {
        local_278[0] = '\0';
        local_278[1] = '\0';
        local_278[2] = '\0';
        local_278[3] = '\0';
        local_269 = 0;
        strncpy(local_278,pcVar9,15);
        local_1b8 = inet_addr(local_278);
        goto LAB_00422e20;
      }
LAB_00422e5c:
      iVar6 = 0x1f43;
    }
LAB_004237c8:
    pcVar9 = 0;
  }
  else {
    iVar6 = strcmp(pcVar9,"DelAll");
    if (iVar6 == 0) {
      uVar8 = swDelAllFilterEntry(1);
    }
    else {
LAB_004231cc:
      uVar8 = 0;
    }
LAB_004231d0:
    iVar6 = swFilterFindErrorNum(uVar8);
    if (iVar6 == 0) {
      memset(&local_1dc,0,176);
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "access_targets_data_param");
      local_3c = uVar12 - 1;
      iVar6 = swGetAccessTargetsEntry(local_3c * 8,&local_1dc);
      if ((iVar6 != 0) || (uVar12 < 2)) {
        local_3c = uVar12;
      }
      local_30 = 0;
      local_34 = 0;
      local_40 = (local_3c - 1) * 8;
      local_2c = local_3c << 3;
      while( true ) {
        uVar12 = swGetAccessTargetsTableSize();
        if (uVar12 <= local_30) break;
        memset(&local_1dc,0,176);
        iVar6 = swGetAccessTargetsEntry(local_30,&local_1dc);
        if (iVar6 == 0) break;
        local_30 = local_30 + 1;
        if (local_40 < local_30) {
          if (local_2c < local_30) break;
          local_12c = local_1d8;
          pageParaSet(&local_234,acStack_1d4,1);
          if (local_12c == 1) {
            pcVar9 = acStack_10f;
            memset(pcVar9,0,"<");
            uVar5 = local_1ac;
            uVar4 = local_1ae;
            cVar3 = local_1b0;
            iVar2 = local_1b4;
            iVar1 = local_1b8;
            local_38 = (uint)(local_1b4 == 0 && local_1b8 == 0);
            uVar7 = local_1ae;
            uVar12 = local_1ac;
            if (local_38 == 0) {
              local_284 = local_1b8;
              sprintf(acStack_254,"%d.%d.%d.%d",local_1b8 >> 24,local_1b8 >> 16 & 255,
                      local_1b8 >> 8 & 255,local_1b8 & 255);
              iVar6 = sprintf(pcVar9,"%s",acStack_254);
              pcVar9 = pcVar9 + iVar6;
              if (iVar1 != iVar2) {
                local_284 = iVar2;
                sprintf(acStack_254,"%d.%d.%d.%d",iVar2 >> 24,iVar2 >> 16 & 255,
                        iVar2 >> 8 & 255,iVar2 & 255);
                iVar6 = sprintf(pcVar9," - %s",acStack_254);
                pcVar9 = pcVar9 + iVar6;
              }
            }
            if (uVar5 != 0 || uVar4 != 0) {
              if (local_38 == 0) {
                strcpy(pcVar9,"/");
                pcVar9 = pcVar9 + 1;
              }
              iVar6 = sprintf(pcVar9,"%d",uVar7);
              pcVar9 = pcVar9 + iVar6;
              if (uVar7 != uVar12) {
                iVar6 = sprintf(pcVar9," - %d",uVar12);
                pcVar9 = pcVar9 + iVar6;
              }
            }
            if (cVar3 != '\0') {
              strcpy(pcVar9,"/");
              if (cVar3 == '\x01') {
                pcVar14 = "TCP";
              }
              else if (cVar3 == '\x02') {
                pcVar14 = "UDP";
              }
              else {
                if (cVar3 != '\x03') goto LAB_0042352c;
                pcVar14 = "ICMP";
              }
              strcpy(pcVar9 + 1,pcVar14);
            }
LAB_0042352c:
            pageParaSet(&local_234,"",3);
          }
          else {
            pageParaSet(&local_234,"",2);
            pcVar9 = local_d3;
            memset(pcVar9,0,140);
            local_d3[0] = '\0';
            for (pcVar14 = local_1aa; pcVar14 != acStack_12e; pcVar14 = pcVar14 + 31) {
              if (*pcVar14 != '\0') {
                iVar6 = sprintf(pcVar9,"%s, ",pcVar14);
                pcVar9 = pcVar9 + iVar6;
              }
            }
            sVar11 = strlen(pcVar9);
            (pcVar9 + (sVar11 - 2))[1] = '\0';
            pcVar9[sVar11 - 2] = '\0';
          }
          local_34 = local_34 + 1;
          pageDynParaListPrintf(&local_234,param_1);
        }
      }
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      local_288 = local_3c;
      pageParaSet(&local_20c,&local_288,0);
      local_288 = (uint)(local_2c < local_30);
      pageParaSet(&local_20c,&local_288,1);
      local_288 = local_34;
      pageParaSet(&local_20c,&local_288,2);
      local_288 = 4;
      pageParaSet(&local_20c,&local_288,3);
      local_288 = swGetFilterEntryNumCfg(1);
      pageParaSet(&local_20c,&local_288,4);
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "access_targets_page_param");
      pageDynParaListPrintf(&local_20c,param_1);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      HttpWebV4Head(param_1,0,1);
      iVar6 = httpRpmFsA(param_1,"/userRpm/AccessCtrlAccessTargetsRpm.htm");
      if (iVar6 == 2) {
        return 2;
      }
      iVar6 = 10;
      goto LAB_004237c8;
    }
    if (iVar6 != 0x714f) goto LAB_004237c8;
    pcVar9 = "../userRpm/AccessCtrlAccessTargetsRpm.htm";
    iVar6 = 0x714f;
  }
  sVar13 = HttpErrorPage(param_1,iVar6,pcVar9,0);
LAB_004237d4:
  return sVar13;
}

