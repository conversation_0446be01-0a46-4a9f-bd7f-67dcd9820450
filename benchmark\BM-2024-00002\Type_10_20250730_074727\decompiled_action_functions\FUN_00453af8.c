
/* WARNING: Type propagation algorithm not settling */

void FUN_00453af8(void)

{
  int iVar1;
  int iVar2;
  char local_b4 [32];
  undefined4 local_94;
  undefined4 local_90;
  undefined4 local_8c;
  undefined4 local_88;
  undefined4 local_84;
  undefined4 local_80;
  undefined4 local_7c;
  undefined4 local_78;
  undefined4 local_74;
  undefined4 local_70;
  undefined4 local_6c;
  undefined4 local_68;
  undefined4 local_64;
  undefined4 local_60;
  undefined4 local_5c;
  undefined4 local_58;
  undefined4 local_54;
  undefined4 local_50;
  undefined4 local_4c;
  undefined4 local_48;
  undefined4 local_44;
  undefined4 local_40;
  undefined4 local_3c;
  undefined4 local_38;
  in_addr iStack_34;
  in_addr aiStack_30 [3];
  in_addr local_24;
  in_addr local_20;
  uint local_1c;
  uint local_18 [4];
  
  local_b4[0] = '\0';
  local_b4[1] = '\0';
  local_b4[2] = '\0';
  local_b4[3] = '\0';
  local_b4[4] = '\0';
  local_b4[5] = '\0';
  local_b4[6] = '\0';
  local_b4[7] = '\0';
  local_b4[8] = '\0';
  local_b4[9] = '\0';
  local_b4[10] = '\0';
  local_b4[11] = '\0';
  local_b4[12] = '\0';
  local_b4[13] = '\0';
  local_b4[14] = '\0';
  local_b4[15] = '\0';
  local_b4[16] = '\0';
  local_b4[17] = '\0';
  local_b4[18] = '\0';
  local_b4[19] = '\0';
  local_b4[20] = '\0';
  local_b4[21] = '\0';
  local_b4[22] = '\0';
  local_b4[23] = '\0';
  local_b4[24] = '\0';
  local_b4[25] = '\0';
  local_b4[26] = '\0';
  local_b4[27] = '\0';
  local_b4[28] = '\0';
  local_b4[29] = '\0';
  local_b4[30] = '\0';
  local_b4[31] = '\0';
  local_94 = 0;
  local_90 = 0;
  local_8c = 0;
  local_88 = 0;
  local_84 = 0;
  local_80 = 0;
  local_7c = 0;
  local_78 = 0;
  local_74 = 0;
  local_70 = 0;
  local_6c = 0;
  local_68 = 0;
  local_64 = 0;
  local_60 = 0;
  local_5c = 0;
  local_58 = 0;
  local_54 = 0;
  local_50 = 0;
  local_4c = 0;
  local_48 = 0;
  local_44 = 0;
  local_40 = 0;
  local_3c = 0;
  local_38 = 0;
  local_18[1] = 0;
  local_18[2] = 0;
  "" = 1;
  iVar1 = FUN_00453880(0);
  "" = 0;
  if (iVar1 == 1) {
    memcpy(local_b4,"192.168.100.1",13);
  }
  else if (iVar1 == 2) {
    memcpy(local_b4,"192.168.0.1",11);
  }
  else {
    memcpy(local_b4,"192.168.0.1",11);
  }
  inet_aton(local_b4,&iStack_34);
  iVar2 = apmib_set(170,&iStack_34);
  if ((((iVar2 != 0) && (iVar2 = apmib_set(172,&iStack_34), iVar2 != 0)) &&
      (iVar2 = inet_aton("255.255.255.0",aiStack_30), iVar2 != 0)) &&
     ((iVar2 = apmib_set(171,aiStack_30), iVar2 != 0 &&
      (iVar2 = apmib_get(174,&local_94), iVar2 != 0)))) {
    local_18[2] = 0;
    memcpy(local_18 + 2,&local_94,4);
    memcpy(&local_1c,&iStack_34,4);
    memcpy(local_18,aiStack_30,4);
    local_24.s_addr = local_18[2] & 0xff000000 | local_1c & local_18[0];
    iVar2 = apmib_set(174,&local_24);
    if ((iVar2 != 0) && (iVar2 = apmib_get(175,&local_74), iVar2 != 0)) {
      local_18[2] = 0;
      memcpy(local_18 + 2,&local_74,4);
      local_20.s_addr = local_18[2] & 0xff000000 | local_1c & local_18[0];
      iVar2 = apmib_set(175,&local_20);
      if ((iVar2 != 0) && (iVar2 = apmib_set(176,&iStack_34), iVar2 != 0)) {
        local_18[1] = 0x5a0;
        iVar2 = apmib_set(945,local_18 + 1);
        if (iVar2 != 0) {
          memcpy(&local_54,"dlinkrouter",11);
          iVar2 = apmib_set(198,&local_54);
          if (iVar2 != 0) {
            if ((iVar1 == 1) || (iVar1 == 2)) {
              memset(&iStack_34,0,4);
              inet_aton("192.168.107.1",&iStack_34);
              apmib_set(0x1b67,&iStack_34);
              apmib_set(0x1b69,&iStack_34);
              memset(&local_24,0,4);
              memset(&local_20,0,4);
              inet_aton("192.168.107.100",&local_24);
              inet_aton("192.168.107.200",&local_20);
              apmib_set(0x1b6b,&local_24);
              apmib_set(0x1b6c,&local_20);
            }
            else {
              memset(&iStack_34,0,4);
              inet_aton("192.168.100.1",&iStack_34);
              apmib_set(0x1b67,&iStack_34);
              apmib_set(0x1b69,&iStack_34);
              memset(&local_24,0,4);
              memset(&local_20,0,4);
              inet_aton("192.168.100.100",&local_24);
              inet_aton("192.168.100.200",&local_20);
              apmib_set(0x1b6b,&local_24);
              apmib_set(0x1b6c,&local_20);
            }
          }
        }
      }
    }
  }
  FUN_0045360c();
  apmib_update(4);
  sleep(2);
  system("reboot");
  return;
}

