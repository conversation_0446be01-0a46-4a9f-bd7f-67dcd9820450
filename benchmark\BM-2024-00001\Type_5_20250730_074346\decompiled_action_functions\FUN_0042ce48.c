
int FUN_0042ce48(undefined4 param_1)

{
  char *__nptr;
  int iVar1;
  uint uVar2;
  short sVar3;
  uint *puVar4;
  char local_30;
  undefined auStack_2f [35];
  
  local_30 = '\0';
  memset(auStack_2f,0,31);
  __nptr = httpGetEnv(param_1,"wan");
  if ((__nptr == 0) || (iVar1 = atoi(__nptr), -1 < iVar1)) {
    getMaxWanPortNumber();
  }
  uVar2 = swGetWanIpv6Type();
  puVar4 = "";
  if (uVar2 < 6) {
    for (; puVar4 != 0; puVar4 = puVar4[10]) {
      if (*puVar4 == uVar2) {
        sprintf(&local_30,"../userRpm/%s",puVar4 + 1);
        sVar3 = GoUrl(param_1,&local_30);
        return sVar3;
      }
    }
  }
  printf("%s %d WAN type not found\r\n","WanIpv6CfgRpmHtm",540);
  return 2;
}

