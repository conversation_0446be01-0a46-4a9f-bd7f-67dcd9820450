
undefined4 FUN_0043f95c(int param_1)

{
  int iVar1;
  __pid_t _Var2;
  int iVar3;
  void *__ptr;
  undefined auStack_108 [256];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","StartFirmwareDownload");
  }
  else {
    memset(auStack_108,0,256);
    iVar1 = mxmlLoadString(0,param_1,0);
    if (iVar1 == 0) {
      puts("ERROR!  tree is NULL");
    }
    else {
      iVar1 = FUN_0042ebb0(auStack_108);
      if (iVar1 == 0) {
        puts("ERROR!, get FW download url");
      }
      else {
        _Var2 = fork();
        if (_Var2 < 0) {
          puts("ERROR! StartFirmwareDownload fork");
        }
        else if (0 < _Var2) {
          iVar1 = FUN_0042ef94(auStack_108);
          if (iVar1 == 0) {
            puts("upload firmware error");
          }
                    /* WARNING: Subroutine does not return */
          exit(0);
        }
      }
    }
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar3 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar3 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar3 = mxmlNewElement(iVar3,"soap:Body");
        if (iVar3 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar3 = mxmlNewElement(iVar3,"StartFirmwareDownloadResponse");
          if (iVar3 == 0) {
            mxmlDelete(iVar1);
            puts("StartFirmwareDownloadResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
            iVar3 = mxmlNewElement(iVar3,"StartFirmwareDownloadResult");
            if (iVar3 == 0) {
              mxmlDelete(iVar1);
              puts("StartFirmwareDownloadResult=NULL");
            }
            else {
              mxmlNewText(iVar3,0,"O");
              if ("" == 0) {
                __ptr = mxmlSaveAllocString(iVar1,0);
                if (__ptr != 0) {
                  FUN_0041ed70("",200,__ptr,"");
                  free(__ptr);
                }
              }
              mxmlDelete(iVar1);
            }
          }
        }
      }
    }
  }
  return 0;
}

