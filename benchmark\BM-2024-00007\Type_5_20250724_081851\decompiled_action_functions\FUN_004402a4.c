
/* WARNING: Type propagation algorithm not settling */

int FUN_004402a4(undefined4 param_1)

{
  char cVar1;
  ushort uVar2;
  uint uVar3;
  int iVar4;
  short sVar7;
  char *pcVar5;
  int iVar6;
  ushort *puVar8;
  char *pcVar9;
  undefined1 *puVar10;
  int iVar11;
  uint local_180;
  int local_17c;
  int local_178;
  int local_174;
  int local_170;
  int local_16c;
  int local_168;
  undefined auStack_164 [44];
  uint local_138 [2];
  char local_130 [32];
  char local_110 [36];
  undefined4 local_ec;
  int local_e8;
  int local_e4;
  int local_e0;
  int local_dc [4];
  int local_cc;
  int local_c8;
  int local_c4;
  undefined4 local_c0;
  int *local_bc;
  undefined4 local_b8;
  int *local_b4;
  undefined4 local_b0;
  int *local_ac;
  undefined4 local_a8;
  int *local_a4;
  undefined4 local_a0;
  int *local_9c;
  undefined4 local_98;
  int *local_94;
  undefined4 local_90;
  uint *local_8c;
  undefined4 local_88;
  char *local_84;
  undefined4 local_80;
  char *local_7c;
  undefined4 local_78;
  int *local_74;
  undefined4 local_70;
  int *local_6c;
  undefined4 local_68;
  int *local_64;
  undefined4 local_60;
  int *local_5c;
  undefined4 local_58;
  int *local_54;
  undefined4 local_50;
  int *local_4c;
  undefined4 local_48;
  int *local_44;
  undefined4 local_40;
  int *local_3c;
  undefined4 local_38;
  int *local_34;
  undefined4 local_30;
  undefined4 *local_2c;
  undefined4 local_28;
  undefined4 local_24;
  
  local_bc = &local_178;
  local_b4 = &local_174;
  local_ac = &local_17c;
  local_a4 = &local_170;
  local_9c = &local_16c;
  local_94 = &local_168;
  local_8c = &local_180;
  local_84 = local_130;
  local_7c = local_110;
  local_74 = &local_e8;
  local_6c = &local_e4;
  local_64 = &local_e0;
  local_5c = local_dc;
  local_54 = local_dc + 2;
  local_4c = local_dc + 3;
  local_44 = &local_cc;
  local_3c = &local_c8;
  local_34 = &local_c4;
  local_2c = &local_c0;
  local_78 = " ";
  local_80 = " ";
  local_24 = 0;
  local_b8 = 0;
  local_b0 = 0;
  local_a8 = 0;
  local_a0 = 0;
  local_98 = 0;
  local_90 = 0;
  local_88 = 0;
  local_70 = 0;
  local_68 = 0;
  local_60 = 0;
  local_58 = 0;
  local_50 = 0;
  local_48 = 0;
  local_40 = 0;
  local_38 = 0;
  local_30 = 0;
  local_28 = 0;
  iVar4 = HttpAccessPermit();
  if (iVar4 == 0) {
    sVar7 = HttpDenyPage(param_1);
    goto LAB_00440f2c;
  }
  local_180 = 0;
  local_17c = 0;
  local_178 = 0;
  local_174 = 0;
  local_170 = 0;
  local_16c = 0;
  local_168 = 0;
  memset(auStack_164,0,",");
  memset(local_138,0,"|");
  iVar4 = httpGetEnv(param_1,"Save");
  if ((iVar4 == 0) &&
     ((iVar4 = httpGetEnv(param_1,"GetGmtTime"), iVar4 == 0 || ("" != 0)))) {
LAB_00440d80:
    "" = 0;
    local_180 = 0;
    local_17c = 0;
    local_178 = 0;
    local_174 = 0;
    local_170 = 0;
    local_16c = 0;
    local_168 = 0;
    memset(auStack_164,0,",");
    swGetSntpCfg(local_138);
    swGetSntpSystemTime(auStack_164);
    swSntpTime2HttpTime(auStack_164,&local_180);
    puVar8 = "";
    local_180 = local_138[0];
    do {
      uVar2 = *puVar8;
      puVar8 = puVar8 + 1;
      uVar3 = uVar2;
      if (local_138[0] == uVar2 - 720) break;
      uVar3 = local_180;
    } while (puVar8 != "");
    local_180 = uVar3;
    httpStatusSet(param_1,0);
    httpHeaderGenerate(param_1);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "timeInf");
    iVar4 = 0;
    do {
      iVar11 = iVar4 + 1;
      pageDynParaPrintf(&local_bc,iVar4,param_1);
      iVar4 = iVar11;
    } while (iVar11 != 19);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar4 = httpRpmFsA(param_1,"/userRpm/DateTimeCfgRpm.htm");
    if (iVar4 == 2) {
      return 2;
    }
    iVar4 = 10;
    puVar10 = 0;
  }
  else {
    swGetSntpCfg(local_138);
    pcVar5 = httpGetEnv(param_1,"timezone");
    if (pcVar5 != 0) {
      iVar4 = atoi(pcVar5);
      local_180 = iVar4 - 720;
    }
    pcVar5 = httpGetEnv(param_1,"year");
    if (pcVar5 != 0) {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 != 0) {
        local_17c = atoi(pcVar9);
      }
    }
    pcVar5 = httpGetEnv(param_1,"month");
    if (pcVar5 != 0) {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 != 0) {
        local_178 = atoi(pcVar9);
      }
    }
    pcVar5 = httpGetEnv(param_1,"day");
    if (pcVar5 != 0) {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 != 0) {
        local_174 = atoi(pcVar9);
      }
    }
    pcVar5 = httpGetEnv(param_1,"hour");
    if (pcVar5 != 0) {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 != 0) {
        local_170 = atoi(pcVar9);
      }
    }
    pcVar5 = httpGetEnv(param_1,"minute");
    if (pcVar5 != 0) {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 != 0) {
        local_16c = atoi(pcVar9);
      }
    }
    pcVar5 = httpGetEnv(param_1,"second");
    if (pcVar5 != 0) {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 != 0) {
        local_168 = atoi(pcVar9);
      }
    }
    pcVar5 = httpGetEnv(param_1,"ntpA");
    if (pcVar5 == 0) {
      strcpy(local_130,"0.0.0.0");
    }
    else {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      iVar4 = 0;
      do {
        cVar1 = pcVar9[iVar4];
        if ((cVar1 == '\0') || (iVar11 = iVar4 + 1, cVar1 == ' ')) {
          local_130[iVar4] = '\0';
          break;
        }
        local_130[iVar4] = cVar1;
        iVar4 = iVar11;
      } while (iVar11 != 31);
    }
    pcVar5 = httpGetEnv(param_1,"ntpB");
    if (pcVar5 == 0) {
      strcpy(local_110,"0.0.0.0");
    }
    else {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      iVar4 = 0;
      do {
        cVar1 = pcVar9[iVar4];
        if ((cVar1 == '\0') || (iVar11 = iVar4 + 1, cVar1 == ' ')) {
          local_110[iVar4] = '\0';
          break;
        }
        local_110[iVar4] = cVar1;
        iVar4 = iVar11;
      } while (iVar11 != 31);
    }
    pcVar5 = httpGetEnv(param_1,"isTimeChanged");
    if (pcVar5 == 0) {
      iVar4 = 0;
    }
    else {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 == 0) {
        iVar4 = 0;
      }
      else {
        iVar4 = atoi(pcVar9);
      }
    }
    pcVar5 = httpGetEnv(param_1,"isDaylightSavingChanged");
    if (pcVar5 == 0) {
      iVar11 = 0;
    }
    else {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 == 0) {
        iVar11 = 0;
      }
      else {
        iVar11 = atoi(pcVar9);
      }
    }
    pcVar5 = httpGetEnv(param_1,"DaylightSaving");
    if (pcVar5 == 0) {
      local_c4 = 0;
    }
    else {
      do {
        pcVar9 = pcVar5;
        pcVar5 = pcVar9 + 1;
      } while (*pcVar9 == ' ');
      if (pcVar9 == 0) {
        local_c4 = 0;
      }
      else {
        iVar6 = strcmp(pcVar9,"on");
        local_c4 = 1;
        if (iVar6 != 0) {
          local_c4 = atoi(pcVar9);
        }
      }
    }
    if (local_c4 == 1) {
      pcVar5 = httpGetEnv(param_1,"start_month");
      if (pcVar5 != 0) {
        local_e8 = atoi(pcVar5);
      }
      pcVar5 = httpGetEnv(param_1,"start_count");
      if (pcVar5 != 0) {
        local_e4 = atoi(pcVar5);
      }
      pcVar5 = httpGetEnv(param_1,"start_week");
      if (pcVar5 != 0) {
        local_e0 = atoi(pcVar5);
      }
      pcVar5 = httpGetEnv(param_1,"start_hour");
      if (pcVar5 != 0) {
        local_dc[0] = atoi(pcVar5);
      }
      pcVar5 = httpGetEnv(param_1,"end_month");
      if (pcVar5 != 0) {
        local_dc[2] = atoi(pcVar5);
      }
      pcVar5 = httpGetEnv(param_1,"end_count");
      if (pcVar5 != 0) {
        local_dc[3] = atoi(pcVar5);
      }
      pcVar5 = httpGetEnv(param_1,"end_week");
      if (pcVar5 != 0) {
        local_cc = atoi(pcVar5);
      }
      pcVar5 = httpGetEnv(param_1,"end_hour");
      if (pcVar5 != 0) {
        local_c8 = atoi(pcVar5);
      }
    }
    iVar6 = local_c4;
    if (((iVar11 == 0) || (local_c4 != 1)) ||
       (sVar7 = swCheckDaylightSaving(&local_ec,local_dc + 1), sVar7 == 0)) {
      local_138[0] = local_180;
      iVar6 = httpGetEnv(param_1,"GetGmtTime");
      if (iVar6 != 0) {
        local_138[1] = 0;
        sVar7 = swSetSntpCfg(local_138);
        iVar4 = sVar7;
        if (iVar4 == 0) {
          iVar4 = FUN_0043ffac(param_1);
          return iVar4;
        }
        goto LAB_00440d78;
      }
      if (iVar4 == 0) {
        sVar7 = swSetSntpCfg(local_138);
        iVar4 = sVar7;
        if (iVar4 != 0) goto LAB_00440d78;
        if (iVar11 == 0) goto LAB_00440d80;
        sVar7 = daylightSavingAction();
      }
      else {
        swHttpTime2SntpTime(&local_180,auStack_164);
        sVar7 = swSetSntpTime(auStack_164);
        iVar4 = sVar7;
        if (iVar4 != 0) goto LAB_00440d78;
        local_138[1] = 1;
        if (local_c4 == 0) {
LAB_00440ce8:
          local_c0 = 0;
        }
        else {
          iVar4 = swIsDaylightSavingPeriod(&local_ec,local_dc + 1,auStack_164);
          if (iVar4 != 1) {
            if (iVar4 != 0) goto LAB_00440cfc;
            goto LAB_00440ce8;
          }
          local_c0 = 1;
        }
        sVar7 = swSetSntpCfg(local_138);
      }
      iVar4 = sVar7;
      if (iVar4 != 0) goto LAB_00440d78;
      goto LAB_00440d80;
    }
    local_dc[2] = 10;
    local_c8 = 3;
    local_dc[3] = iVar6;
    local_e8 = 2;
    local_e4 = 2;
    local_e0 = 0;
    local_dc[0] = 2;
    local_ec = 0;
    local_cc = 0;
    local_dc[1] = 0;
    local_c0 = 0;
    local_c4 = 0;
    if (local_138[0] == 180) {
      local_c4 = 2;
    }
    sVar7 = swSetSntpCfg(local_138);
    iVar4 = sVar7;
    if (iVar4 == 0) {
LAB_00440cfc:
      puVar10 = "";
      iVar4 = 0x4656;
    }
    else {
LAB_00440d78:
      puVar10 = "";
    }
  }
  sVar7 = HttpErrorPage(param_1,iVar4,puVar10,0);
LAB_00440f2c:
  return sVar7;
}

