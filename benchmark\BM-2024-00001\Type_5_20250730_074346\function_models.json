[{"function_name": "httpGetEnv", "key_position": 2, "default_value_position": null, "arguments": [{"position": 1, "type": "VARIABLE", "value": "param_1"}, {"position": 2, "type": "STRING", "value": "upnpenb"}]}, {"function_name": "getEnvToInt", "key_position": 2, "default_value_position": null, "arguments": [{"position": 1, "type": "VARIABLE", "value": "param_1"}, {"position": 2, "type": "STRING", "value": "port"}, {"position": 3, "type": "INTEGER", "value": "0x80000000"}, {"position": 4, "type": "INTEGER", "value": "0x7fffffff"}]}]