
/* WARNING: Type propagation algorithm not settling */

int FUN_00429638(undefined4 param_1)

{
  int iVar1;
  short sVar8;
  undefined **ppuVar2;
  uint uVar3;
  int iVar4;
  int iVar5;
  undefined4 uVar6;
  char *pcVar7;
  undefined1 *puVar9;
  uint uVar10;
  uint local_120;
  uint local_11c [5];
  char local_108 [20];
  undefined4 local_f4;
  uint local_f0;
  in_addr_t local_ec;
  uint local_e8;
  uint local_e4;
  undefined auStack_e0 [4];
  undefined auStack_dc [4];
  undefined auStack_d8 [4];
  undefined auStack_d4 [4];
  undefined auStack_d0 [4];
  undefined auStack_cc [4];
  undefined auStack_c8 [4];
  undefined *local_c4;
  undefined4 local_c0;
  undefined *local_bc;
  undefined4 local_b8;
  undefined *local_b4;
  undefined4 local_b0;
  undefined4 local_ac;
  undefined auStack_a4 [18];
  undefined auStack_92 [18];
  undefined auStack_80 [4];
  undefined *local_7c;
  undefined *local_78 [16];
  int local_38;
  int local_34;
  uint *local_30;
  
  local_11c[0] = 0;
  swGetArpFixMapSize();
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar8 = HttpDenyPage(param_1);
    goto LAB_0042a110;
  }
  iVar1 = httpGetEnv(param_1,"Add");
  if ((iVar1 != 0) || (iVar1 = httpGetEnv(param_1,"Modify"), iVar1 != 0)) {
    iVar1 = FUN_0042a150(param_1);
    return iVar1;
  }
  local_7c = auStack_e0;
  local_e4 = 0;
  local_78[1] = auStack_dc;
  local_78[3] = auStack_d8;
  local_78[5] = auStack_d4;
  local_78[7] = auStack_d0;
  local_78[9] = auStack_cc;
  local_78[11] = auStack_c8;
  local_f4 = 0;
  local_f0 = 0;
  local_ec = 0;
  local_e8 = 0;
  ppuVar2 = local_78;
  local_78[13] = 0;
  do {
    *ppuVar2 = 0;
    ppuVar2 = ppuVar2 + 2;
  } while (ppuVar2 != local_78 + 14);
  local_c4 = auStack_a4;
  local_bc = auStack_92;
  local_b4 = auStack_80;
  local_c0 = 18;
  local_b8 = 16;
  local_ac = 0;
  local_b0 = 0;
  uVar3 = getEnvToInt(param_1,"Page",1,0x7fffffff);
  uVar10 = 1;
  if (uVar3 != 0xffffff80) {
    uVar10 = uVar3;
  }
  uVar3 = getEnvToInt(param_1,"arpServer",0,1);
  if (uVar3 != 0xffffff80) {
    local_120 = uVar3;
    swSetArpCfg(&local_120);
  }
  iVar1 = getEnvToInt(param_1,"enableId",0,7);
  if (iVar1 != -128) {
    iVar4 = getEnvToInt(param_1,"enable",0,1);
    iVar5 = swGetArpFixMapSize();
    iVar5 = getEnvToInt(param_1,"Page",1,(int)(((uint)(iVar5 + 7 >> 31) >> 29) + iVar5 + 7) >> 3
                       );
    if (iVar5 != -128) {
      iVar1 = iVar1 + (iVar5 + -1) * 8;
      swEnableArpFixMapEntryCfg(iVar1,iVar4 != 0);
    }
  }
  uVar6 = swGetArpFixMapSize();
  local_34 = getEnvToInt(param_1,"EntryIndex",0,uVar6);
  if (local_34 == -128) {
    local_34 = -1;
  }
  iVar4 = httpGetEnv(param_1,"Find");
  if ((iVar4 != 0) || (iVar4 = httpGetEnv(param_1,"findmethod"), iVar4 != 0)) {
    iVar1 = FUN_0042909c(param_1);
    return iVar1;
  }
  pcVar7 = httpGetEnv(param_1,"doAll");
  if (pcVar7 == 0) {
    pcVar7 = httpGetEnv(param_1,"Del");
    if (pcVar7 != 0) {
      iVar1 = atoi(pcVar7);
      swDelArpBindingEntryCfg((uVar10 - 1) * 8 + iVar1);
      goto LAB_00429cf8;
    }
    iVar4 = httpGetEnv(param_1,"Save");
    if (iVar4 == 0) goto LAB_00429cf8;
    local_e4 = 0;
    local_f4 = 0;
    local_f0 = 0;
    local_ec = 0;
    local_e8 = 0;
    iVar4 = httpGetEnv(param_1,"Mac");
    if (iVar4 != 0) {
      swMacStr2Eth(iVar4,&local_e8);
    }
    pcVar7 = httpGetEnv(param_1,"Ip");
    if (pcVar7 != 0) {
      local_ec = inet_addr(pcVar7);
    }
    iVar4 = swChkSameLanSubnet(local_ec);
    if (iVar4 != 0) {
      iVar4 = swChkIsLanIpMac(local_ec,&local_e8);
      if (iVar4 == 0) {
        puVar9 = "";
        iVar1 = 0x4e27;
        goto LAB_0042a108;
      }
      iVar4 = swChkDhcpsStaticEntryCfg(&local_e8,local_ec);
      if (iVar4 == 1) {
        puVar9 = "";
        iVar1 = 0x4e2a;
        goto LAB_0042a108;
      }
      pcVar7 = httpGetEnv(param_1,"State");
      if (pcVar7 != 0) {
        iVar4 = atoi(pcVar7);
        local_f0 = (uint)(iVar4 != 0);
      }
      local_f4 = 1;
      pcVar7 = httpGetEnv(param_1,"Changed");
      iVar5 = strcmp(pcVar7,"1");
      iVar4 = 0;
      if (iVar5 == 0) {
        pcVar7 = httpGetEnv(param_1,"SelIndex");
        iVar4 = iVar1;
        if (pcVar7 != 0) {
          iVar4 = atoi(pcVar7);
        }
        uVar6 = 1;
      }
      else {
        uVar6 = 0;
      }
      uVar6 = swSetArpFixmapEntryCfg(iVar4,&local_f4,uVar6,1);
      iVar1 = swArpFindErrorNum(uVar6);
      if (iVar1 == 0) goto LAB_00429cf8;
      goto LAB_0042a104;
    }
    puVar9 = "";
    iVar1 = 0x65fa;
  }
  else {
    iVar1 = strcmp(pcVar7,"EnAll");
    if (iVar1 == 0) {
      uVar6 = 9;
    }
    else {
      iVar1 = strcmp(pcVar7,"DisAll");
      if (iVar1 == 0) {
        uVar6 = 8;
      }
      else {
        iVar1 = strcmp(pcVar7,"DelAll");
        if (iVar1 != 0) goto LAB_00429cf8;
        uVar6 = 10;
      }
    }
    swSetArpBindingCfgAll(uVar6);
LAB_00429cf8:
    local_f4 = 0;
    local_f0 = 0;
    local_ec = 0;
    local_e8 = 0;
    local_e4 = 0;
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "arpFixmapList");
    iVar1 = swGetArpFixmapEntryCfg((uVar10 - 1) * 8,&local_f4);
    if ((iVar1 != 0) || (local_38 = uVar10 - 2, uVar3 = uVar10 - 1, uVar10 < 2)) {
      local_38 = uVar10 - 1;
      uVar3 = uVar10;
    }
    local_38 = local_38 << 3;
    local_30 = &local_f0;
    iVar1 = 0;
    uVar10 = 0;
    while (iVar4 = swGetArpFixMapSize(), iVar1 < iVar4) {
      local_f4 = 0;
      local_f0 = 0;
      local_ec = 0;
      local_e8 = 0;
      local_e4 = 0;
      iVar4 = swGetArpFixmapEntryCfg(iVar1,&local_f4);
      if (iVar4 == 0) break;
      iVar1 = iVar1 + 1;
      if (local_38 < iVar1) {
        if ((int)(uVar3 << 3) < iVar1) break;
        sprintf(local_108,"%02X-%02X-%02X-%02X-%02X-%02X",local_e8 >> 24,local_e8 >> 16 & 255,
                local_e8 >> 8 & 255,local_e8 & 255,local_e4 >> 24,local_e4 >> 16 & 255);
        iVar4 = strcmp(local_108,"00-00-00-00-00-00");
        if (iVar4 == 0) {
          local_108[0] = '\0';
          local_108[1] = '\0';
          local_108[2] = '\0';
          local_108[3] = '\0';
          local_108[4] = '\0';
          local_108[5] = '\0';
          local_108[6] = '\0';
          local_108[7] = '\0';
          local_108[8] = '\0';
          local_108[9] = '\0';
          local_108[10] = '\0';
          local_108[11] = '\0';
          local_108[12] = '\0';
          local_108[13] = '\0';
          local_108[14] = '\0';
          local_108[15] = '\0';
          local_108[16] = '\0';
          local_108[17] = '\0';
        }
        pageParaSet(&local_c4,local_108,0);
        local_11c[1] = 0;
        local_11c[2] = 0;
        local_11c[3] = 0;
        local_11c[4] = 0;
        if (local_ec == 0) {
          local_11c[1] = 0;
        }
        else {
          inet_ntoa_b(local_ec,local_11c + 1,1);
        }
        uVar10 = uVar10 + 1;
        pageParaSet(&local_c4,local_11c + 1,1);
        pageParaSet(&local_c4,local_30,2);
        pageDynParaListPrintf(&local_c4,param_1);
      }
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    swGetArpCfg(&local_120);
    local_11c[0] = local_120;
    pageParaSet(&local_7c,local_11c,0);
    local_11c[0] = uVar3;
    pageParaSet(&local_7c,local_11c,1);
    local_11c[0] = (uint)((int)(uVar3 << 3) < iVar1);
    pageParaSet(&local_7c,local_11c,2);
    local_11c[0] = uVar10;
    pageParaSet(&local_7c,local_11c,3);
    local_11c[0] = 3;
    pageParaSet(&local_7c,local_11c,4);
    local_11c[0] = local_34 + 1;
    pageParaSet(&local_7c,local_11c,5);
    local_11c[0] = swGetArpFixmapEntryNumsCfg();
    pageParaSet(&local_7c,local_11c,6);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "arpFixmapPara");
    pageDynParaListPrintf(&local_7c,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/LanArpBindingRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    iVar1 = 10;
LAB_0042a104:
    puVar9 = 0;
  }
LAB_0042a108:
  sVar8 = HttpErrorPage(param_1,iVar1,puVar9,0);
LAB_0042a110:
  return sVar8;
}

