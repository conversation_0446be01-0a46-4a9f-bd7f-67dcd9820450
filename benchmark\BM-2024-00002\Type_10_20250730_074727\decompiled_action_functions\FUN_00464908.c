
undefined4 FUN_00464908(int param_1)

{
  undefined4 uVar1;
  int iVar2;
  int iVar3;
  char *__s1;
  int iVar4;
  void *__ptr;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetSmartconnectSettings");
    uVar1 = 0;
  }
  else {
    iVar2 = mxmlLoadString(0,param_1,0);
    if (iVar2 == 0) {
      puts("tree=NULL");
      uVar1 = 0;
    }
    else {
      iVar3 = mxmlFindElement(iVar2,iVar2,"soap:Envelope",0,0,1);
      if ((iVar3 != 0) &&
         (iVar3 = mxmlFindElement(iVar2,iVar2,"SetSmartconnectSettings",0,0,1), iVar3 != 0)) {
        iVar3 = mxmlFindElement(iVar3,iVar2,"Enabled",0,0,1);
        if (iVar3 == 0) {
          puts("state=NULL");
          mxmlDelete(iVar2);
          return 0;
        }
        __s1 = mxmlGetText(iVar3,0);
        if (__s1 == 0) {
          puts("value==NULL");
        }
        else {
          strncmp(__s1,"true",4);
        }
      }
      iVar3 = mxmlNewXML("1.0");
      if (iVar3 == 0) {
        puts("xml=NULL");
        mxmlDelete(iVar2);
        uVar1 = 0;
      }
      else {
        iVar4 = mxmlNewElement(iVar3,"soap:Envelope");
        if (iVar4 == 0) {
          mxmlDelete(iVar3);
          mxmlDelete(iVar2);
          puts("soap_env=NULL");
          uVar1 = 0;
        }
        else {
          mxmlElementSetAttr(iVar4,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
          mxmlElementSetAttr(iVar4,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
          mxmlElementSetAttr(iVar4,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
          iVar4 = mxmlNewElement(iVar4,"soap:Body");
          if (iVar4 == 0) {
            mxmlDelete(iVar3);
            mxmlDelete(iVar2);
            puts("body=NULL");
            uVar1 = 0;
          }
          else {
            iVar4 = mxmlNewElement(iVar4,"SetSmartconnectSettingsResponse");
            if (iVar4 == 0) {
              mxmlDelete(iVar3);
              mxmlDelete(iVar2);
              puts("SetSmartconnectSettingsResponse_xml=NULL");
              uVar1 = 0;
            }
            else {
              mxmlElementSetAttr(iVar4,"xmlns","http://purenetworks.com/HNAP1/");
              iVar4 = mxmlNewElement(iVar4,"SetSmartconnectSettingsResult");
              if (iVar4 == 0) {
                mxmlDelete(iVar3);
                mxmlDelete(iVar2);
                puts("SetSmartconnectSettingsResult_xml=NULL");
                uVar1 = 0;
              }
              else {
                mxmlNewText(iVar4,0,"O");
                if ("" == 0) {
                  apmib_update(4);
                  __ptr = mxmlSaveAllocString(iVar3,0);
                  if (__ptr == 0) {
                    puts("retstring=NULL");
                  }
                  else {
                    FUN_0041ed70("",200,__ptr,"");
                    free(__ptr);
                  }
                  mxmlDelete(iVar3);
                  uVar1 = mxmlDelete(iVar2);
                }
                else {
                  mxmlDelete(iVar3);
                  uVar1 = mxmlDelete(iVar2);
                }
              }
            }
          }
        }
      }
    }
  }
  return uVar1;
}

