
undefined4 FUN_00463c90(int param_1)

{
  undefined4 uVar1;
  undefined4 uVar2;
  int iVar3;
  int iVar4;
  char *pcVar5;
  uint local_134;
  undefined auStack_130 [296];
  
  uVar2 = wlan_idx;
  uVar1 = vwlan_idx;
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetWLanRadioSecurity");
  }
  else {
    local_134 = 0;
    iVar3 = mxmlLoadString(0,param_1,0);
    if (iVar3 == 0) {
      fwrite("Create the tree of Wifi Security Failed\n",1,"(",stderr);
    }
    else {
      iVar4 = mxmlFindElement(iVar3,iVar3,"RadioID",0,0,1);
      pcVar5 = mxmlGetText(iVar4,0);
      if ((iVar4 == 0) || (pcVar5 == 0)) {
        fwrite("Get the RadioID value of Wifi Security Failed\n",1,".",stderr);
        mxmlDelete(iVar3);
      }
      else {
        iVar4 = strncmp(pcVar5,"RADIO_2.4GHz",12);
        local_134 = (uint)(iVar4 == 0);
        pcVar5 = strstr(pcVar5,"_Guest");
        if (pcVar5 == 0) {
          vwlan_idx = 0;
        }
        else {
          vwlan_idx = 2;
        }
        memset(auStack_130,0,292);
        iVar4 = FUN_0045e2b4(auStack_130,&local_134);
        if (iVar4 == 0) {
          wlan_idx = uVar2;
          iVar4 = FUN_0045d468(auStack_130);
          if (iVar4 == 0) {
            mxmlDelete(iVar3);
            vwlan_idx = uVar1;
          }
          else {
            fwrite("ReplyWiFiSecurityXmlToWeb failed\n",1,"!",stderr);
            mxmlDelete(iVar3);
          }
        }
        else {
          fwrite("ApmibGetWifiParamValue failed\n",1,30,stderr);
          mxmlDelete(iVar3);
        }
      }
    }
  }
  return 0;
}

