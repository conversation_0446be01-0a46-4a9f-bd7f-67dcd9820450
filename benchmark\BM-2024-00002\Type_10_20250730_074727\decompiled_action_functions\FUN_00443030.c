
undefined4 FUN_00443030(int param_1)

{
  undefined4 uVar1;
  undefined4 uVar2;
  int iVar3;
  int iVar4;
  int iVar5;
  int iVar6;
  char *pcVar7;
  FILE *__stream;
  void *__ptr;
  undefined4 local_4e0;
  undefined4 local_4dc;
  undefined4 local_4d8;
  undefined4 local_4d4;
  sysinfo local_4d0;
  char local_490 [12];
  char local_484 [32];
  uint local_464;
  uint local_460;
  undefined4 local_45c;
  undefined4 local_458;
  undefined4 local_454;
  undefined4 local_450;
  undefined4 local_44c;
  undefined4 local_448;
  char local_444 [32];
  in_addr local_424;
  undefined4 local_420;
  undefined4 local_41c;
  undefined4 local_418;
  undefined4 local_414;
  undefined4 local_410;
  undefined4 local_40c;
  undefined4 local_408;
  undefined4 local_404;
  undefined4 local_400;
  undefined4 local_3fc;
  undefined4 local_3f8;
  undefined4 local_3f4;
  undefined4 local_3f0;
  undefined4 local_3ec;
  undefined4 local_3e8;
  char local_3e4 [32];
  undefined4 local_3c4;
  undefined4 local_3c0;
  undefined4 local_3bc;
  undefined4 local_3b8;
  undefined4 local_3b4;
  undefined4 local_3b0;
  undefined4 local_3ac;
  undefined4 local_3a8;
  undefined4 local_3a4;
  undefined4 local_3a0;
  undefined4 local_39c;
  undefined4 local_398;
  undefined4 local_394;
  undefined4 local_390;
  undefined4 local_38c;
  undefined4 local_388;
  int local_384;
  char local_380 [32];
  undefined4 local_360;
  undefined4 local_35c;
  undefined4 local_358;
  undefined4 local_354;
  undefined4 local_350;
  undefined4 local_34c;
  undefined4 local_348;
  undefined4 local_344;
  int local_340;
  char local_33c [48];
  undefined4 local_30c;
  undefined4 local_308;
  undefined4 local_304;
  undefined4 local_300;
  char local_2fc [8];
  undefined4 local_2f4;
  undefined4 local_2f0;
  undefined4 local_2ec;
  undefined4 local_2e8;
  undefined4 local_2e4;
  undefined4 local_2e0;
  undefined4 local_2dc;
  undefined4 local_2d8;
  undefined4 local_2d4;
  undefined4 local_2d0;
  undefined4 local_2cc;
  undefined4 local_2c8;
  undefined4 local_2c4;
  undefined4 local_2c0;
  undefined4 local_2bc;
  undefined4 local_2b8;
  undefined4 local_2b4;
  undefined4 local_2b0;
  undefined4 local_2ac;
  undefined4 local_2a8;
  undefined4 local_2a4;
  undefined4 local_2a0;
  undefined4 local_29c;
  undefined4 local_298;
  undefined auStack_294 [128];
  undefined auStack_214 [128];
  undefined4 local_194;
  undefined4 local_190;
  undefined4 local_18c;
  undefined4 local_188;
  undefined4 local_184;
  undefined4 local_180;
  undefined4 local_17c;
  undefined4 local_178;
  undefined4 local_174;
  undefined4 local_170;
  undefined4 local_16c;
  undefined4 local_168;
  undefined4 local_164;
  undefined4 local_160;
  undefined4 local_15c;
  undefined4 local_158;
  undefined auStack_154 [128];
  undefined auStack_d4 [128];
  undefined4 local_54;
  undefined4 local_50;
  undefined4 local_4c;
  undefined4 local_48;
  undefined4 local_44;
  undefined4 local_40;
  undefined4 local_3c;
  undefined4 local_38;
  undefined4 local_34;
  undefined4 local_30;
  undefined4 local_2c;
  undefined4 local_28;
  undefined4 local_24;
  undefined4 local_20;
  undefined4 local_1c;
  undefined4 local_18;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetRouterInformationSettings");
  }
  else {
    iVar3 = mxmlNewXML("1.0");
    if (iVar3 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar4 = mxmlNewElement(iVar3,"soap:Envelope");
      if (iVar4 == 0) {
        mxmlDelete(iVar3);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar4,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar4,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar4,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar4 = mxmlNewElement(iVar4,"soap:Body");
        if (iVar4 == 0) {
          mxmlDelete(iVar3);
          puts("body=NULL");
        }
        else {
          iVar4 = mxmlNewElement(iVar4,"GetRouterInformationSettingsResponse");
          if (iVar4 == 0) {
            mxmlDelete(iVar3);
            puts("GetRouterInformationSettingsResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar4,"xmlns","http://purenetworks.com/HNAP1/");
            iVar5 = mxmlNewElement(iVar4,"GetRouterInformationSettingsResult");
            if (iVar5 == 0) {
              mxmlDelete(iVar3);
              puts("GetRouterInformationSettingsResult=NULL");
            }
            else {
              mxmlNewText(iVar5,0,"O");
              iVar5 = mxmlNewElement(iVar4,"ProductName");
              if (iVar5 == 0) {
                mxmlDelete(iVar3);
                puts("ProductName=NULL");
              }
              else {
                mxmlNewText(iVar5,0,"DIR-823G");
                iVar5 = mxmlNewElement(iVar4,"SerialNumber");
                if (iVar5 == 0) {
                  mxmlDelete(iVar3);
                  puts("SerialNumber=NULL");
                }
                else {
                  local_4e0 = 0;
                  local_4dc = 0;
                  local_4d8 = 0;
                  local_4d4 = 0;
                  iVar6 = apmib_get(0x1c21,&local_4e0);
                  if (iVar6 == 0) {
                    puts("get MIB_HW_SERIAL_NUMBER error");
                  }
                  mxmlNewText(iVar5,0,&local_4e0);
                  iVar5 = mxmlNewElement(iVar4,"SoftwareVersion");
                  if (iVar5 == 0) {
                    mxmlDelete(iVar3);
                    puts("SoftwareVersion=NULL");
                  }
                  else {
                    mxmlNewText(iVar5,0,"1.0.2");
                    iVar5 = mxmlNewElement(iVar4,"OperationHours");
                    if (iVar5 == 0) {
                      mxmlDelete(iVar3);
                      puts("OperationHours=NULL");
                    }
                    else {
                      local_490[0] = '\0';
                      local_490[1] = '\0';
                      local_490[2] = '\0';
                      local_490[3] = '\0';
                      local_490[4] = '\0';
                      local_490[5] = '\0';
                      local_490[6] = '\0';
                      local_490[7] = '\0';
                      local_490[8] = '\0';
                      local_490[9] = '\0';
                      sysinfo(&local_4d0);
                      snprintf(local_490,9,"%d",local_4d0.uptime);
                      mxmlNewText(iVar5,0,local_490);
                      iVar5 = mxmlNewElement(iVar4,"OperatingMode");
                      if (iVar5 == 0) {
                        mxmlDelete(iVar3);
                        puts("OperatingMode=NULL");
                      }
                      else {
                        mxmlNewText(iVar5,0,"router");
                        iVar5 = mxmlNewElement(iVar4,"LanMacAddress");
                        if (iVar5 == 0) {
                          mxmlDelete(iVar3);
                          puts("LanMacAddress=NULL");
                        }
                        else {
                          local_484[0] = '\0';
                          local_484[1] = '\0';
                          local_484[2] = '\0';
                          local_484[3] = '\0';
                          local_484[4] = '\0';
                          local_484[5] = '\0';
                          local_484[6] = '\0';
                          local_484[7] = '\0';
                          local_484[8] = '\0';
                          local_484[9] = '\0';
                          local_484[10] = '\0';
                          local_484[11] = '\0';
                          local_484[12] = '\0';
                          local_484[13] = '\0';
                          local_484[14] = '\0';
                          local_484[15] = '\0';
                          local_484[16] = '\0';
                          local_484[17] = '\0';
                          local_484[18] = '\0';
                          local_484[19] = '\0';
                          local_484[20] = '\0';
                          local_484[21] = '\0';
                          local_484[22] = '\0';
                          local_484[23] = '\0';
                          local_484[24] = '\0';
                          local_484[25] = '\0';
                          local_484[26] = '\0';
                          local_484[27] = '\0';
                          local_484[28] = '\0';
                          local_484[29] = '\0';
                          local_484[30] = '\0';
                          local_484[31] = '\0';
                          local_464 = 0;
                          local_460 = 0;
                          local_45c = 0;
                          local_458 = 0;
                          local_454 = 0;
                          local_450 = 0;
                          local_44c = 0;
                          local_448 = 0;
                          iVar6 = apmib_get(201,&local_464);
                          if (iVar6 == 0) {
                            puts("get HW_NIC0_ADDR error");
                          }
                          snprintf(local_484," ","%02x:%02x:%02x:%02x:%02x:%02x",local_464 & 255,
                                   local_464 >> 8 & 255,local_464 >> 16 & 255,local_464 >> 24,
                                   local_460 & 255,local_460 >> 8 & 255);
                          mxmlNewText(iVar5,0,local_484);
                          iVar5 = mxmlNewElement(iVar4,"LanIPAddress");
                          if (iVar5 == 0) {
                            mxmlDelete(iVar3);
                            puts("LanIPAddress=NULL");
                          }
                          else {
                            local_444[0] = '\0';
                            local_444[1] = '\0';
                            local_444[2] = '\0';
                            local_444[3] = '\0';
                            local_444[4] = '\0';
                            local_444[5] = '\0';
                            local_444[6] = '\0';
                            local_444[7] = '\0';
                            local_444[8] = '\0';
                            local_444[9] = '\0';
                            local_444[10] = '\0';
                            local_444[11] = '\0';
                            local_444[12] = '\0';
                            local_444[13] = '\0';
                            local_444[14] = '\0';
                            local_444[15] = '\0';
                            local_444[16] = '\0';
                            local_444[17] = '\0';
                            local_444[18] = '\0';
                            local_444[19] = '\0';
                            local_444[20] = '\0';
                            local_444[21] = '\0';
                            local_444[22] = '\0';
                            local_444[23] = '\0';
                            local_444[24] = '\0';
                            local_444[25] = '\0';
                            local_444[26] = '\0';
                            local_444[27] = '\0';
                            local_444[28] = '\0';
                            local_444[29] = '\0';
                            local_444[30] = '\0';
                            local_444[31] = '\0';
                            local_424.s_addr = 0;
                            local_420 = 0;
                            local_41c = 0;
                            local_418 = 0;
                            local_414 = 0;
                            local_410 = 0;
                            local_40c = 0;
                            local_408 = 0;
                            iVar6 = apmib_get(170,&local_424);
                            if (iVar6 == 0) {
                              puts("get IP_ADDR error");
                            }
                            pcVar7 = inet_ntoa(local_424);
                            snprintf(local_444," ","%s",pcVar7);
                            iVar6 = strncmp(local_444,"0.0.0.0",7);
                            if (iVar6 == 0) {
                              mxmlNewText(iVar5,0,0);
                            }
                            else {
                              mxmlNewText(iVar5,0,local_444);
                            }
                            iVar5 = mxmlNewElement(iVar4,"WanMacAddress");
                            if (iVar5 == 0) {
                              mxmlDelete(iVar3);
                              puts("WanMacAddress=NULL");
                            }
                            else {
                              local_404 = 0;
                              local_400 = 0;
                              local_3fc = 0;
                              local_3f8 = 0;
                              local_3f4 = 0;
                              local_3f0 = 0;
                              local_3ec = 0;
                              local_3e8 = 0;
                              local_3e4[0] = '\0';
                              local_3e4[1] = '\0';
                              local_3e4[2] = '\0';
                              local_3e4[3] = '\0';
                              local_3e4[4] = '\0';
                              local_3e4[5] = '\0';
                              local_3e4[6] = '\0';
                              local_3e4[7] = '\0';
                              local_3e4[8] = '\0';
                              local_3e4[9] = '\0';
                              local_3e4[10] = '\0';
                              local_3e4[11] = '\0';
                              local_3e4[12] = '\0';
                              local_3e4[13] = '\0';
                              local_3e4[14] = '\0';
                              local_3e4[15] = '\0';
                              local_3e4[16] = '\0';
                              local_3e4[17] = '\0';
                              local_3e4[18] = '\0';
                              local_3e4[19] = '\0';
                              local_3e4[20] = '\0';
                              local_3e4[21] = '\0';
                              local_3e4[22] = '\0';
                              local_3e4[23] = '\0';
                              local_3e4[24] = '\0';
                              local_3e4[25] = '\0';
                              local_3e4[26] = '\0';
                              local_3e4[27] = '\0';
                              local_3e4[28] = '\0';
                              local_3e4[29] = '\0';
                              local_3e4[30] = '\0';
                              local_3e4[31] = '\0';
                              local_3c4 = 0;
                              local_3c0 = 0;
                              local_3bc = 0;
                              local_3b8 = 0;
                              local_3b4 = 0;
                              local_3b0 = 0;
                              local_3ac = 0;
                              local_3a8 = 0;
                              local_3a4 = 0;
                              local_3a0 = 0;
                              local_39c = 0;
                              local_398 = 0;
                              local_394 = 0;
                              local_390 = 0;
                              local_38c = 0;
                              local_388 = 0;
                              iVar6 = FUN_0042d4c8(local_3e4,&local_3a4,&local_3c4,&local_404);
                              if (iVar6 < 0) {
                                puts("get ip/mask/gateway/mac is error!");
                              }
                              mxmlNewText(iVar5,0,&local_404);
                              iVar5 = mxmlNewElement(iVar4,"WANInternetAccess");
                              if (iVar5 == 0) {
                                mxmlDelete(iVar3);
                                puts("WANInternetAccess=NULL");
                              }
                              else {
                                local_380[0] = '\0';
                                local_380[1] = '\0';
                                local_380[2] = '\0';
                                local_380[3] = '\0';
                                local_380[4] = '\0';
                                local_380[5] = '\0';
                                local_380[6] = '\0';
                                local_380[7] = '\0';
                                local_380[8] = '\0';
                                local_380[9] = '\0';
                                local_380[10] = '\0';
                                local_380[11] = '\0';
                                local_380[12] = '\0';
                                local_380[13] = '\0';
                                local_380[14] = '\0';
                                local_380[15] = '\0';
                                local_380[16] = '\0';
                                local_380[17] = '\0';
                                local_380[18] = '\0';
                                local_380[19] = '\0';
                                local_380[20] = '\0';
                                local_380[21] = '\0';
                                local_380[22] = '\0';
                                local_380[23] = '\0';
                                local_380[24] = '\0';
                                local_380[25] = '\0';
                                local_380[26] = '\0';
                                local_380[27] = '\0';
                                local_380[28] = '\0';
                                local_380[29] = '\0';
                                local_380[30] = '\0';
                                local_380[31] = '\0';
                                iVar6 = apmib_get("h",&local_384);
                                if (iVar6 == 0) {
                                  puts("get WanType is error!");
                                }
                                else if (local_384 == 0) {
                                  snprintf(local_380,7,"Static");
                                }
                                else if (local_384 == 1) {
                                  snprintf(local_380,5,"DHCP");
                                }
                                else if (local_384 == 3) {
                                  snprintf(local_380,10,"DHCPPPPoE");
                                }
                                else {
                                  snprintf(local_380,1,"");
                                }
                                mxmlNewText(iVar5,0,local_380);
                                iVar5 = mxmlNewElement(iVar4,"PppoeType");
                                if (iVar5 == 0) {
                                  mxmlDelete(iVar3);
                                  puts("PppoeType=NULL");
                                }
                                else {
                                  local_360 = 0;
                                  local_35c = 0;
                                  local_358 = 0;
                                  local_354 = 0;
                                  local_350 = 0;
                                  local_34c = 0;
                                  local_348 = 0;
                                  local_344 = 0;
                                  local_340 = 0;
                                  iVar6 = strncmp(local_380,"DHCPPPPoE",10);
                                  if (iVar6 == 0) {
                                    iVar6 = apmib_get(0x1b75,&local_340);
                                    if (iVar6 == 0) {
                                      puts("get PPPoEType is error!");
                                    }
                                    if (local_340 == 0) {
                                      snprintf(&local_360,7,"normal");
                                    }
                                    else if (local_340 == 1) {
                                      snprintf(&local_360,9,"pppoe_hn");
                                    }
                                    else if (local_340 == 2) {
                                      snprintf(&local_360,9,"pppoe_nc");
                                    }
                                    else if (local_340 == 3) {
                                      snprintf(&local_360,10,"pppoe_hb1");
                                    }
                                    else if (local_340 == 4) {
                                      snprintf(&local_360,10,"pppoe_hb2");
                                    }
                                  }
                                  mxmlNewText(iVar5,0,&local_360);
                                  iVar5 = mxmlNewElement(iVar4,"WanIPAddress");
                                  if (iVar5 == 0) {
                                    mxmlDelete(iVar3);
                                    puts("WanIPAddress=NULL");
                                  }
                                  else {
                                    iVar6 = strncmp(local_3e4,"0.0.0.0",7);
                                    if (iVar6 == 0) {
                                      mxmlNewText(iVar5,0,0);
                                    }
                                    else {
                                      mxmlNewText(iVar5,0,local_3e4);
                                    }
                                    iVar5 = mxmlNewElement(iVar4,"Gateway");
                                    if (iVar5 == 0) {
                                      mxmlDelete(iVar3);
                                      puts("Gateway=NULL");
                                    }
                                    else {
                                      iVar6 = strncmp(&local_3c4,"0.0.0.0",7);
                                      if (iVar6 == 0) {
                                        mxmlNewText(iVar5,0,0);
                                      }
                                      else {
                                        mxmlNewText(iVar5,0,&local_3c4);
                                      }
                                      iVar5 = mxmlNewElement(iVar4,"DNSServer");
                                      if (iVar5 == 0) {
                                        mxmlDelete(iVar3);
                                        puts("DNSServer=NULL");
                                      }
                                      else {
                                        local_33c[0] = '\0';
                                        local_33c[1] = '\0';
                                        local_33c[2] = '\0';
                                        local_33c[3] = '\0';
                                        local_33c[4] = '\0';
                                        local_33c[5] = '\0';
                                        local_33c[6] = '\0';
                                        local_33c[7] = '\0';
                                        local_33c[8] = '\0';
                                        local_33c[9] = '\0';
                                        local_33c[10] = '\0';
                                        local_33c[11] = '\0';
                                        local_33c[12] = '\0';
                                        local_33c[13] = '\0';
                                        local_33c[14] = '\0';
                                        local_33c[15] = '\0';
                                        local_33c[16] = '\0';
                                        local_33c[17] = '\0';
                                        local_33c[18] = '\0';
                                        local_33c[19] = '\0';
                                        local_33c[20] = '\0';
                                        local_33c[21] = '\0';
                                        local_33c[22] = '\0';
                                        local_33c[23] = '\0';
                                        local_33c[24] = '\0';
                                        local_33c[25] = '\0';
                                        local_33c[26] = '\0';
                                        local_33c[27] = '\0';
                                        local_33c[28] = '\0';
                                        local_33c[29] = '\0';
                                        local_33c[30] = '\0';
                                        local_33c[31] = '\0';
                                        local_33c[32] = '\0';
                                        local_33c[33] = '\0';
                                        local_33c[34] = '\0';
                                        local_33c[35] = '\0';
                                        local_33c[36] = '\0';
                                        local_33c[37] = '\0';
                                        local_33c[38] = '\0';
                                        local_33c[39] = '\0';
                                        local_33c[40] = '\0';
                                        local_33c[41] = '\0';
                                        local_33c[42] = '\0';
                                        local_33c[43] = '\0';
                                        local_33c[44] = '\0';
                                        local_33c[45] = '\0';
                                        local_33c[46] = '\0';
                                        local_33c[47] = '\0';
                                        local_30c = 0;
                                        local_308 = 0;
                                        local_304 = 0;
                                        local_300 = 0;
                                        iVar6 = FUN_0042d91c(local_33c);
                                        if (0 < iVar6) {
                                          snprintf(&local_30c,16,local_33c);
                                        }
                                        mxmlNewText(iVar5,0,&local_30c);
                                        iVar5 = mxmlNewElement(iVar4,"PPPoEdialingStatus");
                                        if (iVar5 == 0) {
                                          mxmlDelete(iVar3);
                                          puts("PPPoEdialingStatus=NULL");
                                        }
                                        else {
                                          local_2fc[0] = '\0';
                                          local_2fc[1] = '\0';
                                          local_2fc[2] = '\0';
                                          local_2fc[3] = '\0';
                                          local_2fc[4] = '\0';
                                          local_2fc[5] = '\0';
                                          local_2fc[6] = '\0';
                                          local_2fc[7] = '\0';
                                          __stream = fopen("/tmp/PppoeStatus","r");
                                          if (__stream != 0) {
                                            fgets(local_2fc,8,__stream);
                                            fclose(__stream);
                                          }
                                          mxmlNewText(iVar5,0,local_2fc);
                                          uVar2 = wlan_idx;
                                          uVar1 = vwlan_idx;
                                          iVar5 = mxmlNewElement(iVar4,"ChannelTwo");
                                          if (iVar5 == 0) {
                                            mxmlDelete(iVar3);
                                            puts("ChannelTwo=NULL");
                                          }
                                          else {
                                            local_2f4 = 0;
                                            local_2f0 = 0;
                                            local_2ec = 0;
                                            local_2e8 = 0;
                                            FUN_004429d0(1,0,&local_2f4);
                                            mxmlNewText(iVar5,0,&local_2f4);
                                            iVar5 = mxmlNewElement(iVar4,"ChannelFive");
                                            if (iVar5 == 0) {
                                              mxmlDelete(iVar3);
                                              puts("ChannelFive=NULL");
                                            }
                                            else {
                                              local_2e4 = 0;
                                              local_2e0 = 0;
                                              local_2dc = 0;
                                              local_2d8 = 0;
                                              FUN_004429d0(0,0,&local_2e4);
                                              mxmlNewText(iVar5,0,&local_2e4);
                                              iVar5 = mxmlNewElement(iVar4,"BandwidthTwo");
                                              if (iVar5 == 0) {
                                                mxmlDelete(iVar3);
                                                puts("BandwidthTwo=NULL");
                                              }
                                              else {
                                                local_2d4 = 0;
                                                local_2d0 = 0;
                                                local_2cc = 0;
                                                local_2c8 = 0;
                                                FUN_00442af4(1,0,&local_2d4);
                                                mxmlNewText(iVar5,0,&local_2d4);
                                                iVar5 = mxmlNewElement(iVar4,"BandwidthFive");
                                                if (iVar5 == 0) {
                                                  mxmlDelete(iVar3);
                                                  puts("BandwidthFive=NULL");
                                                }
                                                else {
                                                  local_2c4 = 0;
                                                  local_2c0 = 0;
                                                  local_2bc = 0;
                                                  local_2b8 = 0;
                                                  FUN_00442af4(0,0,&local_2c4);
                                                  mxmlNewText(iVar5,0,&local_2c4);
                                                  iVar5 = mxmlNewElement(iVar4,"PowerTwo");
                                                  if (iVar5 == 0) {
                                                    mxmlDelete(iVar3);
                                                    puts("PowerTwo=NULL");
                                                  }
                                                  else {
                                                    local_2b4 = 0;
                                                    local_2b0 = 0;
                                                    local_2ac = 0;
                                                    local_2a8 = 0;
                                                    FUN_00442c78(1,0,&local_2b4);
                                                    mxmlNewText(iVar5,0,&local_2b4);
                                                    iVar5 = mxmlNewElement(iVar4,"PowerFive");
                                                    if (iVar5 == 0) {
                                                      mxmlDelete(iVar3);
                                                      puts("PowerFive=NULL");
                                                    }
                                                    else {
                                                      local_2a4 = 0;
                                                      local_2a0 = 0;
                                                      local_29c = 0;
                                                      local_298 = 0;
                                                      FUN_00442c78(0,0,&local_2a4);
                                                      mxmlNewText(iVar5,0,&local_2a4);
                                                      iVar5 = mxmlNewElement(iVar4,"MasterSsidTwo");
                                                      if (iVar5 == 0) {
                                                        mxmlDelete(iVar3);
                                                        puts("MasterSsidTwo=NULL");
                                                      }
                                                      else {
                                                        memset(auStack_294,0,128);
                                                        FUN_00442d90(1,0,auStack_294);
                                                        mxmlNewText(iVar5,0,auStack_294);
                                                        iVar5 = mxmlNewElement(iVar4,
                                                  "MasterSsidFive");
                                                  if (iVar5 == 0) {
                                                    mxmlDelete(iVar3);
                                                    puts("MasterSsidFive=NULL");
                                                  }
                                                  else {
                                                    memset(auStack_214,0,128);
                                                    FUN_00442d90(0,0,auStack_214);
                                                    mxmlNewText(iVar5,0,auStack_214);
                                                    iVar5 = mxmlNewElement(iVar4,
                                                  "EncryptionMethodTwo");
                                                  if (iVar5 == 0) {
                                                    mxmlDelete(iVar3);
                                                    puts("EncryptionMethodTwo=NULL");
                                                  }
                                                  else {
                                                    local_194 = 0;
                                                    local_190 = 0;
                                                    local_18c = 0;
                                                    local_188 = 0;
                                                    FUN_00442e08(1,0,&local_194);
                                                    mxmlNewText(iVar5,0,&local_194);
                                                    iVar5 = mxmlNewElement(iVar4,
                                                  "EncryptionMethodFive");
                                                  if (iVar5 == 0) {
                                                    mxmlDelete(iVar3);
                                                    puts("EncryptionMethodFive=NULL");
                                                  }
                                                  else {
                                                    local_184 = 0;
                                                    local_180 = 0;
                                                    local_17c = 0;
                                                    local_178 = 0;
                                                    FUN_00442e08(0,0,&local_184);
                                                    mxmlNewText(iVar5,0,&local_184);
                                                    iVar5 = mxmlNewElement(iVar4,"MasterStatusTwo");
                                                    if (iVar5 == 0) {
                                                      mxmlDelete(iVar3);
                                                      puts("MasterStatusTwo=NULL");
                                                    }
                                                    else {
                                                      local_174 = 0;
                                                      local_170 = 0;
                                                      local_16c = 0;
                                                      local_168 = 0;
                                                      FUN_00442f88(1,0,&local_174);
                                                      mxmlNewText(iVar5,0,&local_174);
                                                      iVar5 = mxmlNewElement(iVar4,
                                                  "MasterStatusFive");
                                                  if (iVar5 == 0) {
                                                    mxmlDelete(iVar3);
                                                    puts("MasterStatusFive=NULL");
                                                  }
                                                  else {
                                                    local_164 = 0;
                                                    local_160 = 0;
                                                    local_15c = 0;
                                                    local_158 = 0;
                                                    FUN_00442f88(0,0,&local_164);
                                                    mxmlNewText(iVar5,0,&local_164);
                                                    iVar5 = mxmlNewElement(iVar4,"GuestSsidTwo");
                                                    if (iVar5 == 0) {
                                                      mxmlDelete(iVar3);
                                                      puts("GuestSsidTwo=NULL");
                                                    }
                                                    else {
                                                      memset(auStack_154,0,128);
                                                      FUN_00442d90(1,2,auStack_154);
                                                      mxmlNewText(iVar5,0,auStack_154);
                                                      iVar5 = mxmlNewElement(iVar4,"GuestSsidFive");
                                                      if (iVar5 == 0) {
                                                        mxmlDelete(iVar3);
                                                        puts("GuestSsidFive=NULL");
                                                      }
                                                      else {
                                                        memset(auStack_d4,0,128);
                                                        FUN_00442d90(0,2,auStack_d4);
                                                        mxmlNewText(iVar5,0,auStack_d4);
                                                        iVar5 = mxmlNewElement(iVar4,
                                                  "GuestEncryptionTwo");
                                                  if (iVar5 == 0) {
                                                    mxmlDelete(iVar3);
                                                    puts("GuestEncryptionTwo=NULL");
                                                  }
                                                  else {
                                                    local_54 = 0;
                                                    local_50 = 0;
                                                    local_4c = 0;
                                                    local_48 = 0;
                                                    FUN_00442e08(1,2,&local_54);
                                                    mxmlNewText(iVar5,0,&local_54);
                                                    iVar5 = mxmlNewElement(iVar4,
                                                  "GuestEncryptionFive");
                                                  if (iVar5 == 0) {
                                                    mxmlDelete(iVar3);
                                                    puts("GuestEncryptionFive=NULL");
                                                  }
                                                  else {
                                                    local_44 = 0;
                                                    local_40 = 0;
                                                    local_3c = 0;
                                                    local_38 = 0;
                                                    FUN_00442e08(0,2,&local_44);
                                                    mxmlNewText(iVar5,0,&local_44);
                                                    iVar5 = mxmlNewElement(iVar4,"GuestStatusTwo");
                                                    if (iVar5 == 0) {
                                                      mxmlDelete(iVar3);
                                                      puts("GuestStatusTwo=NULL");
                                                    }
                                                    else {
                                                      local_34 = 0;
                                                      local_30 = 0;
                                                      local_2c = 0;
                                                      local_28 = 0;
                                                      FUN_00442f88(1,2,&local_34);
                                                      mxmlNewText(iVar5,0,&local_34);
                                                      iVar4 = mxmlNewElement(iVar4,"GuestStatusFive"
                                                                            );
                                                      if (iVar4 == 0) {
                                                        mxmlDelete(iVar3);
                                                        puts("GuestStatusFive=NULL");
                                                      }
                                                      else {
                                                        local_24 = 0;
                                                        local_20 = 0;
                                                        local_1c = 0;
                                                        local_18 = 0;
                                                        FUN_00442f88(0,2,&local_24);
                                                        mxmlNewText(iVar4,0,&local_24);
                                                        if ("" == 0) {
                                                          __ptr = mxmlSaveAllocString(iVar3,
                                                  0);
                                                  if (__ptr != 0) {
                                                    FUN_0041ed70("",200,__ptr,
                                                                 "");
                                                    free(__ptr);
                                                  }
                                                  }
                                                  mxmlDelete(0);
                                                  mxmlDelete(iVar3);
                                                  vwlan_idx = uVar1;
                                                  wlan_idx = uVar2;
                                                  }
                                                  }
                                                  }
                                                  }
                                                  }
                                                  }
                                                  }
                                                  }
                                                  }
                                                  }
                                                  }
                                                  }
                                                  }
                                                  }
                                                }
                                              }
                                            }
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

