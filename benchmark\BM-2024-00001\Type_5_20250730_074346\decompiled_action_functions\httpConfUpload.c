
int httpConfUpload(undefined4 param_1)

{
  int iVar1;
  uint uVar2;
  uint uVar3;
  short sVar4;
  undefined4 uVar5;
  char *pcVar6;
  undefined4 local_48;
  undefined4 local_44;
  char acStack_40 [36];
  
  local_44 = 0;
  memcpy(acStack_40,"../userRpm/ConfUpdateTemp.htm",30);
  "" = 0;
  "" = 0;
  "" = 0;
  "" = &local_48;
  "" = &local_44;
  "" = acStack_40;
  "" = strlen(acStack_40);
  iVar1 = httpRpmUpload(param_1,0);
  if (iVar1 == -1) {
    uVar2 = httpMimeContentLengthGet(param_1,0);
    uVar3 = uploadBufLenGet(0);
    if (uVar2 < uVar3) {
      return -1;
    }
    pcVar6 = "../userRpm/BakNRestoreRpm.htm";
    uVar5 = 23000;
  }
  else if (iVar1 == -2) {
    pcVar6 = "../userRpm/BakNRestoreRpm.htm";
    uVar5 = 0x59da;
  }
  else {
    httpStatusSet(param_1,0);
    httpHeaderGenerate(param_1);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "tempPageInf");
    local_48 = sysGetUploadConfigTime();
    pageParaSet("",&local_48,0);
    local_44 = 2;
    pageParaSet("",&local_44,1);
    pageDynParaPrintf("",0,param_1);
    pageDynParaPrintf("",1,param_1);
    pageDynParaPrintf("",2,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/UpdateTemp.htm");
    if (iVar1 == 2) {
      return 2;
    }
    uVar5 = 10;
    pcVar6 = 0;
  }
  sVar4 = HttpErrorPage(param_1,uVar5,pcVar6,0);
  return sVar4;
}

