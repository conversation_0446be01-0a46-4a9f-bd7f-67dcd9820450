
/* WARNING: Type propagation algorithm not settling */

int FUN_0045a8a0(undefined4 param_1)

{
  int iVar1;
  undefined4 uVar2;
  undefined4 uVar3;
  char *pcVar4;
  int iVar5;
  void *__ptr;
  uint local_10 [2];
  
  local_10[0] = 0;
  local_10[1] = 0;
  iVar1 = mxmlLoadString(0,param_1,0);
  if (iVar1 != 0) {
    uVar2 = mxmlFindElement(iVar1,iVar1,"soap:Envelope",0,0,1);
    uVar2 = mxmlFindElement(uVar2,iVar1,"SetDDNSSettings",0,0,1);
    uVar3 = mxmlFindElement(uVar2,iVar1,"Enable",0,0,1);
    pcVar4 = mxmlGetText(uVar3,0);
    iVar5 = strncmp(pcVar4,"true",4);
    local_10[0] = (uint)(iVar5 == 0);
    iVar5 = apmib_set(187,local_10);
    if (iVar5 == 0) {
      printf("Set enabled flag error!");
    }
    else {
      uVar3 = mxmlFindElement(uVar2,iVar1,"Provider",0,0,1);
      pcVar4 = mxmlGetText(uVar3,0);
      iVar5 = strncmp(pcVar4,"oray.com",8);
      if (iVar5 == 0) {
        local_10[1] = 2;
      }
      else {
        local_10[1] = 0;
      }
      iVar5 = apmib_set(188,local_10 + 1);
      if (iVar5 == 0) {
        printf("Set DDNS Type error!");
      }
      else {
        uVar3 = mxmlFindElement(uVar2,iVar1,"Hostname",0,0,1);
        iVar5 = mxmlGetText(uVar3,0);
        if ((iVar5 == 0) || (iVar5 = apmib_set(189,iVar5), iVar5 != 0)) {
          uVar3 = mxmlFindElement(uVar2,iVar1,"Username",0,0,1);
          iVar5 = mxmlGetText(uVar3,0);
          if ((iVar5 == 0) || (iVar5 = apmib_set(190,iVar5), iVar5 != 0)) {
            uVar2 = mxmlFindElement(uVar2,iVar1,"Password",0,0,1);
            iVar1 = mxmlGetText(uVar2,0);
            if ((iVar1 == 0) || (iVar1 = apmib_set(191,iVar1), iVar1 != 0)) {
              iVar1 = mxmlNewXML("1.0");
              if (iVar1 == 0) {
                puts("new xml error");
                return 0;
              }
              iVar5 = mxmlNewElement(iVar1,"soap:Envelope");
              if (iVar5 == 0) {
                puts("new xml error");
                return 0;
              }
              mxmlElementSetAttr(iVar5,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
              mxmlElementSetAttr(iVar5,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
              mxmlElementSetAttr(iVar5,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
              iVar5 = mxmlNewElement(iVar5,"soap:Body");
              if (iVar5 == 0) {
                puts("body xml error");
                return 0;
              }
              iVar5 = mxmlNewElement(iVar5,"SetDDNSSettingsResponse");
              if (iVar5 == 0) {
                puts("SetDDNSSettingsResponse xml error");
                return 0;
              }
              mxmlElementSetAttr(iVar5,"xmlns","http://purenetworks.com/HNAP1/");
              iVar5 = mxmlNewElement(iVar5,"SetDDNSSettingsResult");
              if (iVar5 == 0) {
                puts("SetDDNSSettingsResult xml error");
                return 0;
              }
              mxmlNewText(iVar5,0,"O");
              __ptr = mxmlSaveAllocString(iVar1,0);
              FUN_0041ed70("",200,__ptr,"");
              free(__ptr);
              mxmlDelete(iVar1);
            }
            else {
              printf("Set DDNS Password String error!");
            }
          }
          else {
            printf("Set DDNS Password String error!");
          }
        }
        else {
          printf("Set DDNS Password String error!");
        }
      }
    }
  }
  apmib_update(4);
  iVar1 = system("ddns.sh");
  return iVar1;
}

