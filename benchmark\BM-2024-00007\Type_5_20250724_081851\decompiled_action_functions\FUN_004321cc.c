
int FUN_004321cc(undefined4 param_1)

{
  bool bVar1;
  undefined4 uVar2;
  int iVar3;
  uint32_t uVar4;
  uint32_t uVar5;
  uint32_t uVar6;
  uint32_t uVar7;
  undefined4 *puVar8;
  code *pcVar9;
  int local_b8;
  undefined4 local_b4;
  undefined4 local_b0;
  undefined4 local_ac;
  undefined4 local_a8;
  char acStack_a4 [16];
  char acStack_94 [16];
  undefined auStack_84 [100];
  
  local_b8 = 0;
  local_b4 = 0;
  local_b0 = 0;
  local_ac = 0;
  local_a8 = 1;
  swGetLanCfg(&httpLanCfgQuickSetup);
  swGetDhcpsCfg(httpLanDhcpServerQuickSetup);
  swGetPasswordCfg(auStack_84);
  memcpy(userQuickSetup,auStack_84,"b");
  iVar3 = getProductId();
  if (iVar3 == 0x8020001) {
    local_ac = 1;
    "" = 0xffffff00;
    httpLanCfgQuickSetup = 0xc0a800fe;
  }
  show_rootap = 1;
  uVar4 = ntohl(httpLanCfgQuickSetup);
  uVar5 = ntohl(httpLanCfgQuickSetup);
  uVar6 = ntohl(httpLanCfgQuickSetup);
  uVar7 = ntohl(httpLanCfgQuickSetup);
  sprintf(acStack_a4,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
          (int)(uVar6 & -256) >> 8,uVar7 & 255);
  uVar4 = ntohl("");
  uVar5 = ntohl("");
  uVar6 = ntohl("");
  uVar7 = ntohl("");
  sprintf(acStack_94,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
          (int)(uVar6 & -256) >> 8,uVar7 & 255);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar3 = HttpAccessPermit(param_1);
  if (iVar3 == 0) {
    iVar3 = HttpDenyPage(param_1);
    iVar3 = iVar3 << 16;
LAB_0043277c:
    iVar3 = iVar3 >> 16;
  }
  else {
    iVar3 = httpGetEnv(param_1,"Next");
    bVar1 = false;
    if (iVar3 == 0) {
LAB_0043248c:
      iVar3 = httpGetEnv(param_1,"Return");
      if (iVar3 == 0) {
        if (!bVar1) {
          local_b0 = swIsMultiSystemMode();
          httpPrintf(param_1,
                     "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n"
                     ,"wzdLanPara");
          iVar3 = getProductId();
          if (iVar3 == 0x8020001) {
            puVar8 = &local_a8;
          }
          else {
            puVar8 = &local_ac;
          }
          writePageParamSet(param_1,"%d,",puVar8,0);
          writePageParamSet(param_1,""%s",",acStack_a4,1);
          iVar3 = strcmp(acStack_94,"*********");
          if (iVar3 == 0) {
            local_b4 = 0;
            uVar2 = local_b4;
          }
          else {
            iVar3 = strcmp(acStack_94,"***********");
            uVar2 = 1;
            if (iVar3 != 0) {
              iVar3 = strcmp(acStack_94,"*************");
              uVar2 = 2;
              if (iVar3 != 0) {
                uVar2 = 3;
              }
            }
          }
          local_b4 = uVar2;
          writePageParamSet(param_1,"%d,",&local_b4,2);
          writePageParamSet(param_1,""%s",",acStack_94,3);
          iVar3 = getProductId();
          if (iVar3 == 0x8020001) {
            writePageParamSet(param_1,"%d,",&local_ac,0);
          }
          httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
          httpPrintf(param_1,
                     "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n"
                     ,"sysModePara");
          writePageParamSet(param_1,"%d,",&local_b0,0);
          httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
          httpWizardPrintStepInfo(param_1);
          HttpWebV4Head(param_1,0,0);
          iVar3 = httpRpmFsA(param_1,"/userRpm/WzdNetworkRpm.htm");
          if (iVar3 != 2) {
            iVar3 = HttpErrorPage(param_1,10,0,0);
            iVar3 = iVar3 << 16;
            goto LAB_0043277c;
          }
          goto LAB_00432788;
        }
        pcVar9 = wzdStepFindNext;
      }
      else {
        pcVar9 = wzdStepFindPrev;
      }
      iVar3 = (*pcVar9)(&local_b8);
      if (iVar3 != 0) {
        iVar3 = GoUrl(param_1,local_b8 + 8);
        iVar3 = iVar3 << 16;
        goto LAB_0043277c;
      }
    }
    else {
      iVar3 = FUN_00431c28(param_1,&httpLanCfgQuickSetup,httpLanDhcpServerQuickSetup,auStack_84,
                           userQuickSetup);
      bVar1 = true;
      if (iVar3 != 2) goto LAB_0043248c;
    }
LAB_00432788:
    iVar3 = 2;
  }
  return iVar3;
}

