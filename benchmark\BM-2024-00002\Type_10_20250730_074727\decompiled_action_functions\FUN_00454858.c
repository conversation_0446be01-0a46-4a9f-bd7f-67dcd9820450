
void FUN_00454858(undefined4 param_1)

{
  int iVar1;
  undefined4 uVar2;
  undefined4 uVar3;
  char *pcVar4;
  int iVar5;
  int iVar6;
  void *__ptr;
  in_addr iStack_b4;
  in_addr aiStack_b0 [3];
  in_addr iStack_a4;
  in_addr iStack_a0;
  char acStack_9c [100];
  int local_38;
  int local_34;
  int local_30 [8];
  undefined2 local_10;
  
  memset(acStack_9c,0,100);
  local_38 = 0;
  local_34 = 0;
  local_30[0] = 0;
  local_30[1] = 0;
  local_30[2] = 0;
  local_30[3] = 0;
  local_30[4] = 0;
  local_30[5] = 0;
  local_30[6] = 0;
  local_30[7] = 0;
  local_10 = 0;
  iVar1 = mxmlLoadString(0,param_1,0);
  if (iVar1 != 0) {
    uVar2 = mxmlFindElement(iVar1,iVar1,"soap:Envelope",0,0,1);
    uVar2 = mxmlFindElement(uVar2,iVar1,"SetNetworkSettings",0,0,1);
    uVar3 = mxmlFindElement(uVar2,iVar1,"IPAddress",0,0,1);
    pcVar4 = mxmlGetText(uVar3,0);
    inet_aton(pcVar4,&iStack_b4);
    iVar5 = apmib_set(170,&iStack_b4);
    if (iVar5 != 0) {
      uVar3 = mxmlFindElement(uVar2,iVar1,"DHCPenable",0,0,1);
      pcVar4 = mxmlGetText(uVar3,0);
      iVar5 = strncmp(pcVar4,"true",4);
      if (iVar5 == 0) {
        local_34 = 2;
      }
      else {
        local_34 = 0;
      }
      iVar5 = apmib_get(173,local_30);
      if (iVar5 != 0) {
        if (local_34 != local_30[0]) {
          snprintf(acStack_9c,100,"/var/run/udhcpd_br0.pid");
          uVar3 = FUN_0042dad4(acStack_9c);
          memset(acStack_9c,0,100);
          snprintf(acStack_9c,100,"kill  %d\n",uVar3);
          iVar5 = apmib_set(173,&local_34);
          if (iVar5 == 0) goto LAB_00454dd0;
          if (local_34 == 0) {
            system(acStack_9c);
          }
          else if (local_34 == 2) {
            system("udhcpd /var/udhcpd.conf &");
          }
          else {
            puts("the mode is not found");
          }
        }
        uVar3 = mxmlFindElement(uVar2,iVar1,"SubnetMask",0,0,1);
        pcVar4 = mxmlGetText(uVar3,0);
        iVar5 = inet_aton(pcVar4,aiStack_b0);
        if ((iVar5 != 0) && (iVar5 = apmib_set(171,aiStack_b0), iVar5 != 0)) {
          uVar3 = mxmlFindElement(uVar2,iVar1,"IPRangeStart",0,0,1);
          pcVar4 = mxmlGetText(uVar3,0);
          iVar5 = inet_aton(pcVar4,&iStack_a4);
          if ((iVar5 != 0) && (iVar5 = apmib_set(174,&iStack_a4), iVar5 != 0)) {
            uVar3 = mxmlFindElement(uVar2,iVar1,"IPRangeEnd",0,0,1);
            pcVar4 = mxmlGetText(uVar3,0);
            iVar5 = inet_aton(pcVar4,&iStack_a0);
            if ((iVar5 != 0) && (iVar5 = apmib_set(175,&iStack_a0), iVar5 != 0)) {
              uVar2 = mxmlFindElement(uVar2,iVar1,"LeaseTime",0,0,1);
              pcVar4 = mxmlGetText(uVar2,0);
              if (pcVar4 != 0) {
                local_38 = atoi(pcVar4);
                apmib_set(945,&local_38);
              }
            }
          }
        }
      }
    }
  }
LAB_00454dd0:
  iVar5 = mxmlNewXML("1.0");
  if (iVar5 == 0) {
    puts("xml=NULL");
  }
  else {
    iVar6 = mxmlNewElement(iVar5,"soap:Envelope");
    if (iVar6 == 0) {
      puts("soap_env=NULL");
    }
    else {
      mxmlElementSetAttr(iVar6,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
      mxmlElementSetAttr(iVar6,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
      mxmlElementSetAttr(iVar6,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
      iVar6 = mxmlNewElement(iVar6,"soap:Body");
      if (iVar6 == 0) {
        puts("body=NULL");
      }
      else {
        iVar6 = mxmlNewElement(iVar6,"SetNetworkSettingsResponse");
        if (iVar6 == 0) {
          puts("SetNetworkSettingsResponse=NULL");
        }
        else {
          mxmlElementSetAttr(iVar6,"xmlns","http://purenetworks.com/HNAP1/");
          iVar6 = mxmlNewElement(iVar6,"SetNetworkSettingsResult");
          if (iVar6 == 0) {
            puts("SetNetworkSettingsResult=NULL");
          }
          else {
            mxmlNewText(iVar6,0,"O");
            if ("" == 0) {
              apmib_update(4);
              __ptr = mxmlSaveAllocString(iVar5,0);
              if (__ptr == 0) {
                puts("retstring=NULL");
              }
              else {
                FUN_0041ed70("",200,__ptr,"");
                free(__ptr);
              }
              mxmlDelete(iVar5);
              mxmlDelete(iVar1);
              system("sysconf init gw all");
            }
            else {
              mxmlDelete(iVar5);
              mxmlDelete(iVar1);
            }
          }
        }
      }
    }
  }
  return;
}

