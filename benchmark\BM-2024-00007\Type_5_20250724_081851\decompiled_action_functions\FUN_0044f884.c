
int FUN_0044f884(undefined4 param_1)

{
  short sVar2;
  int iVar1;
  undefined4 local_18;
  undefined4 local_14;
  
  local_18 = 0;
  local_14 = 0;
  semTake(tddp_sem,0xffffffff);
  sVar2 = tddp_DownloadFlashData(&local_18,&local_14);
  if (sVar2 == 0) {
    httpStatusSet(param_1,0);
    httpMimeContentTypeSet(param_1,1,"x-bin/octet-stream");
    httpHeaderGenerate(param_1);
    httpBlockPut(param_1,local_18,local_14);
    semGive(tddp_sem);
    iVar1 = 2;
  }
  else {
    semGive(tddp_sem);
    sVar2 = HttpErrorPage(param_1,sVar2,"../userRpm/BakNRestoreRpm.htm",0);
    iVar1 = sVar2;
  }
  return iVar1;
}

