
int FUN_004591a8(undefined4 param_1)

{
  int iVar1;
  short sVar2;
  int iVar3;
  undefined4 uVar4;
  
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpIsAccessFromLAN(param_1);
  uVar4 = 0;
  if (iVar1 == 0) {
    uVar4 = 1;
    iVar1 = getForbiddenWanUpgrade();
    if (iVar1 == 0) {
      uVar4 = 0;
    }
  }
  httpPrintf(param_1,"<Script type=\"text/javascript\">\n");
  httpPrintf(param_1,"var bakNRestroreInf = new Array (\n%d,\n0,0);\n",uVar4);
  httpPrintf(param_1,"</script>\n");
  HttpWebV4Head(param_1,0,1);
  iVar1 = httpRpmFsA(param_1,"/userRpm/BakNRestoreRpm.htm");
  iVar3 = 2;
  if (iVar1 != 2) {
    sVar2 = HttpErrorPage(param_1,10,0,0);
    iVar3 = sVar2;
  }
  return iVar3;
}

