
int FUN_0044b4ac(undefined4 param_1)

{
  int iVar1;
  short sVar2;
  undefined4 local_98;
  undefined4 local_94;
  undefined4 local_90;
  undefined4 local_8c;
  undefined4 local_88;
  undefined4 local_84;
  undefined auStack_80 [20];
  undefined auStack_6c [4];
  undefined auStack_68 [20];
  undefined auStack_54 [4];
  undefined auStack_50 [4];
  undefined auStack_4c [4];
  undefined *local_48;
  undefined4 local_44;
  undefined *local_40;
  undefined4 local_3c;
  undefined *local_38;
  undefined4 local_34;
  undefined *local_30;
  undefined4 local_2c;
  undefined *local_28;
  undefined4 local_24;
  undefined4 local_20;
  
  local_40 = auStack_68;
  local_38 = auStack_54;
  local_30 = auStack_50;
  local_28 = auStack_4c;
  local_48 = auStack_6c;
  local_3c = 20;
  local_20 = 0;
  local_44 = 0;
  local_34 = 0;
  local_2c = 0;
  local_24 = 0;
  memset(local_48,0,"$");
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar2 = HttpDenyPage(param_1);
  }
  else {
    iVar1 = httpGetEnv(param_1,"Save");
    if (iVar1 != 0) {
      FUN_0044b208(param_1);
    }
    local_94 = 0;
    local_90 = 0;
    local_8c = 0;
    local_88 = 0;
    local_84 = 0;
    swGetPwdConf(&local_94);
    iVar1 = FUN_0044b17c(local_90);
    if (iVar1 == 1) {
      local_88 = 300;
      local_84 = 3;
      local_94 = 0;
      local_90 = 0;
      local_8c = 300;
      swSetPwdConf(&local_94);
    }
    local_98 = local_94;
    pageParaSet(&local_48,&local_98,0);
    inet_ntoa_b(local_90,auStack_80);
    pageParaSet(&local_48,auStack_80,1);
    local_98 = local_8c;
    pageParaSet(&local_48,&local_98,2);
    local_98 = local_88;
    pageParaSet(&local_48,&local_98,3);
    local_98 = local_84;
    pageParaSet(&local_48,&local_98,4);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "wlanPwdPara");
    pageDynParaListPrintf(&local_48,param_1);
    httpPrintf(param_1,"%d,",iVar1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/PingWatchDogRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar2 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar2;
}

