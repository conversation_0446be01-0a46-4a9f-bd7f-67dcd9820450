
undefined4 FUN_0043d7c8(int param_1)

{
  tm *ptVar1;
  FILE *__stream;
  uint uVar2;
  int iVar3;
  char *pcVar4;
  int iVar5;
  int iVar6;
  void *__ptr;
  char local_64 [36];
  undefined4 local_40;
  undefined4 local_3c;
  undefined4 local_38;
  undefined4 local_34;
  undefined4 local_30;
  undefined4 local_2c;
  undefined4 local_28;
  undefined4 local_24;
  undefined4 local_20;
  char local_1c [8];
  undefined4 local_14;
  time_t atStack_10 [2];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetNTPServerSettings");
  }
  else {
    local_64[0] = '\0';
    local_64[1] = '\0';
    local_64[2] = '\0';
    local_64[3] = '\0';
    local_64[4] = '\0';
    local_64[5] = '\0';
    local_64[6] = '\0';
    local_64[7] = '\0';
    local_64[8] = '\0';
    local_64[9] = '\0';
    local_64[10] = '\0';
    local_64[11] = '\0';
    local_64[12] = '\0';
    local_64[13] = '\0';
    local_64[14] = '\0';
    local_64[15] = '\0';
    local_64[16] = '\0';
    local_64[17] = '\0';
    local_64[18] = '\0';
    local_64[19] = '\0';
    local_64[20] = '\0';
    local_64[21] = '\0';
    local_64[22] = '\0';
    local_64[23] = '\0';
    local_64[24] = '\0';
    local_64[25] = '\0';
    local_64[26] = '\0';
    local_64[27] = '\0';
    local_64[28] = '\0';
    local_64[29] = '\0';
    local_64[30] = '\0';
    local_64[31] = '\0';
    local_64[32] = '\0';
    local_64[33] = '\0';
    local_64[34] = '\0';
    local_64[35] = '\0';
    local_40 = 0;
    local_3c = 0;
    local_38 = 0;
    local_34 = 0;
    local_30 = 0;
    local_2c = 0;
    local_28 = 0;
    local_24 = 0;
    local_20 = 0;
    local_1c[0] = '\0';
    local_1c[1] = '\0';
    local_1c[2] = '\0';
    local_1c[3] = '\0';
    local_1c[4] = '\0';
    local_1c[5] = '\0';
    local_1c[6] = '\0';
    local_1c[7] = '\0';
    local_14 = 0;
    time(atStack_10);
    ptVar1 = localtime(atStack_10);
    sprintf(local_64,"%d/%d/%d %d:%d:%d %d",ptVar1->tm_year + 0x76c,ptVar1->tm_mon + 1,
            ptVar1->tm_mday,ptVar1->tm_hour,ptVar1->tm_min,ptVar1->tm_sec,ptVar1->tm_wday);
    __stream = fopen("/tmp/timeStatus","r");
    if (__stream == 0) {
      local_14 = CONCAT31(local_14._1_3_,"2");
    }
    else {
      if (__stream->_lock == 0) {
        uVar2 = fgetc(__stream);
      }
      else if (__stream->_IO_write_base < __stream->_IO_write_end) {
        uVar2 = (uint)(byte)*__stream->_IO_write_base;
        __stream->_IO_write_base = __stream->_IO_write_base + 1;
      }
      else {
        uVar2 = __fgetc_unlocked(__stream);
      }
      if (uVar2 == "2") {
        local_14 = CONCAT31(local_14._1_3_,"1");
      }
      else {
        local_14 = CONCAT31(local_14._1_3_,"2");
      }
      fclose(__stream);
    }
    iVar3 = apmib_get(153,local_1c);
    if (iVar3 == 0) {
      puts("get MIB_NTP_TIMEZONE is error");
    }
    else {
      pcVar4 = strchr(local_1c," ");
      if (pcVar4 != 0) {
        *pcVar4 = '\0';
      }
      sprintf(&local_40,"CST%s",local_1c);
    }
    iVar3 = mxmlNewXML("1.0");
    if (iVar3 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar5 = mxmlNewElement(iVar3,"soap:Envelope");
      if (iVar5 == 0) {
        mxmlDelete(iVar3);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar5,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar5,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar5,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar5 = mxmlNewElement(iVar5,"soap:Body");
        if (iVar5 == 0) {
          mxmlDelete(iVar3);
          puts("body=NULL");
        }
        else {
          iVar5 = mxmlNewElement(iVar5,"GetNTPServerSettingsResponse");
          if (iVar5 == 0) {
            mxmlDelete(iVar3);
            puts("GetNTPServerSettingsResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar5,"xmlns","http://purenetworks.com/HNAP1/");
            iVar6 = mxmlNewElement(iVar5,"GetNTPServerSettingsResult");
            if (iVar6 == 0) {
              mxmlDelete(iVar3);
              puts("GetNTPServerSettingsResult=NULL");
            }
            else {
              mxmlNewText(iVar6,0,"O");
              iVar6 = mxmlNewElement(iVar5,"Time");
              if (iVar6 == 0) {
                mxmlDelete(iVar3);
                puts("Time=NULL");
              }
              else {
                mxmlNewText(iVar6,0,local_64);
                iVar6 = mxmlNewElement(iVar5,"Status");
                if (iVar6 == 0) {
                  mxmlDelete(iVar3);
                  puts("Status=NULL");
                }
                else {
                  mxmlNewText(iVar6,0,&local_14);
                  iVar5 = mxmlNewElement(iVar5,"TimeZone");
                  if (iVar5 == 0) {
                    mxmlDelete(iVar3);
                    puts("TimeZone=NULL");
                  }
                  else {
                    mxmlNewText(iVar5,0,&local_40);
                    if (("" == 0) &&
                       (__ptr = mxmlSaveAllocString(iVar3,0), __ptr != 0)) {
                      FUN_0041ed70("",200,__ptr,"");
                      free(__ptr);
                    }
                    mxmlDelete(iVar3);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

