
undefined4 FUN_00441cf0(int param_1)

{
  int iVar1;
  int iVar2;
  undefined4 uVar3;
  char *pcVar4;
  size_t sVar5;
  undefined4 local_10;
  undefined4 local_c;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetNetworkTomographySettings");
  }
  else {
    local_10 = 0;
    local_c = 0;
    iVar1 = mxmlLoadString(0,param_1,0);
    if (iVar1 != 0) {
      iVar2 = mxmlFindElement(iVar1,iVar1,"SetNetworkTomographySettings",0,0,1);
      if (iVar2 != 0) {
        uVar3 = mxmlFindElement(iVar1,iVar1,"Address",0,0,1);
        pcVar4 = mxmlGetText(uVar3,0);
        if (pcVar4 == 0) {
          puts("error, ping address is NULL");
        }
        else {
          sVar5 = strlen(pcVar4);
          if (sVar5 < 128) {
            iVar2 = apmib_set(0x1b72,pcVar4);
            if (iVar2 == 0) {
              puts("error, apmib set MIB_PING_ADDRESS");
            }
            else {
              uVar3 = mxmlFindElement(iVar1,iVar1,"Number",0,0,1);
              iVar2 = mxmlGetText(uVar3,0);
              if (iVar2 == 0) {
                puts("error, ping number is NULL");
              }
              else {
                local_10 = FUN_0042c9cc(iVar2);
                iVar2 = apmib_set(0x1b73,&local_10);
                if (iVar2 == 0) {
                  puts("error, apmib set MIB_PING_NUMBER");
                }
                else {
                  uVar3 = mxmlFindElement(iVar1,iVar1,"Size",0,0,1);
                  iVar1 = mxmlGetText(uVar3,0);
                  if (iVar1 == 0) {
                    puts("error, ping size is NULL");
                  }
                  else {
                    local_c = FUN_0042c9cc(iVar1);
                    iVar1 = apmib_set(0x1b74,&local_c);
                    if (iVar1 == 0) {
                      puts("error, apmib set MIB_PING_SIZE");
                    }
                  }
                }
              }
            }
          }
          else {
            puts("address length is error!");
          }
        }
      }
    }
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar2 = mxmlNewElement(iVar2,"soap:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"SetNetworkTomographySettingsResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("SetNetworkTomographySettingsResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar2 = mxmlNewElement(iVar2,"SetNetworkTomographySettingsResult");
            if (iVar2 == 0) {
              mxmlDelete(iVar1);
              puts("SetNetworkTomographySettingsResult=NULL");
            }
            else {
              mxmlNewText(iVar2,0,"O");
              if ("" == 0) {
                pcVar4 = mxmlSaveAllocString(iVar1,0);
                if (pcVar4 != 0) {
                  puts(pcVar4);
                  FUN_0041ed70("",200,pcVar4,"");
                  free(pcVar4);
                }
              }
              mxmlDelete(iVar1);
            }
          }
        }
      }
    }
  }
  return 0;
}

