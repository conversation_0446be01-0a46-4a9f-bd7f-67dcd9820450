
int FUN_0043b6b4(undefined4 param_1)

{
  byte bVar1;
  undefined4 uVar2;
  char *__s1;
  int iVar3;
  short sVar7;
  int iVar4;
  size_t sVar5;
  char *__s1_00;
  int iVar6;
  char *pcVar8;
  
  pcVar8 = "";
  uVar2 = httpReqMemPartIdGet();
  __s1 = httpAuthorizationGet(param_1,0);
  httpStatusSet(param_1,0);
  iVar3 = HttpAccessPermit(param_1);
  if (iVar3 == 0) {
    sVar7 = HttpDenyPage(param_1);
    goto LAB_0043bc04;
  }
  httpHeaderGenerate(param_1);
  iVar3 = swGetFirstState();
  bVar1 = 0;
  if (iVar3 != 0) {
    bVar1 = 1;
    iVar3 = swSystemModeIsNotAP();
    if (iVar3 != 0) {
      bVar1 = 0;
    }
  }
  iVar3 = httpGetEnv(param_1,"WzdStepOnly");
  iVar4 = strncmp(__s1,"Basic ",6);
  if (iVar4 == 0) {
    __s1 = __s1 + 6;
  }
  sVar5 = strlen(__s1);
  __s1_00 = memPoolAlloc(uVar2,(sVar5 & -1) + 1);
  if (__s1_00 != 0) {
    httpPwdDecode(__s1,__s1_00,(int)sVar5);
    memPoolFree(uVar2,__s1_00);
  }
  iVar4 = HttpIsAccessFromLAN(param_1);
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "visibleMenuList");
  iVar6 = getProductId();
  if (iVar6 == 0x30200002) {
    httpPrintf(param_1,"\"WorkingModeRpm\",\n");
  }
  iVar6 = isOEMWanLogin();
  if ((iVar6 == 0) || (iVar4 == 0)) {
    if ((bool)(bVar1 & iVar3 == 0)) {
      pcVar8 = "\"WzdStartRpm\",\n";
      goto LAB_0043ba04;
    }
    iVar3 = isSupportAccountEnable();
    if ((iVar3 == 1) && (iVar3 = strcmp(__s1_00,"Support"), iVar3 == 0)) {
      httpPrintf(param_1,"\"%s\",\n","StatusRpm");
      httpPrintf(param_1,"\"%s\",\n","WlanNetworkRpm");
      httpPrintf(param_1,"\"%s\",\n","WlanAdvRpm");
      httpPrintf(param_1,"\"%s\",\n","WlanStationRpm");
      httpPrintf(param_1,"\"%s\",\n","WlanMacFilterRpm");
      httpPrintf(param_1,"\"%s\",\n","WlanSecurityRpm");
      httpPrintf(param_1,"\"%s\",\n","LogoutRpm");
    }
    else {
      for (; pcVar8 != 0; pcVar8 = *(char **)(pcVar8 + "@")) {
        iVar3 = strncmp(pcVar8,"LtvRpm",6);
        if (iVar3 != 0) {
          httpPrintf(param_1,"\"%s\",\n",pcVar8);
        }
      }
    }
  }
  else {
    iVar3 = swGetFirstState();
    pcVar8 = "\"WzdStartRpm\",\n";
    if (iVar3 == 0) {
      httpPrintf(param_1,"\"StatusRpm\",\n");
      httpPrintf(param_1,"\"WzdStartRpm\",\n");
      httpPrintf(param_1,"\"LtvRpm\",\n");
      httpPrintf(param_1,"\"WlanNetworkRpm\",\n");
      httpPrintf(param_1,"\"WlanSecurityRpm\",\n");
      httpPrintf(param_1,"\"DiagnosticRpm\",\n");
      httpPrintf(param_1,"\"SystemStatisticRpm\",\n");
      pcVar8 = "\"SoftwareUpgradeRpm\",\n";
    }
LAB_0043ba04:
    httpPrintf(param_1,pcVar8);
  }
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  HttpWebV4Head(param_1,4,1);
  iVar3 = httpRpmFsA(param_1,"/userRpm/MenuRpm.htm");
  if (iVar3 == 2) {
    return 2;
  }
  sVar7 = HttpErrorPage(param_1,10,0,0);
LAB_0043bc04:
  return sVar7;
}

