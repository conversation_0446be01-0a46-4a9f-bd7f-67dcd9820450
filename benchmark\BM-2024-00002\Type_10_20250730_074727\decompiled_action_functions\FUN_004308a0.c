
/* WARNING: Type propagation algorithm not settling */

undefined4 FUN_004308a0(void)

{
  int iVar1;
  int iVar2;
  int iVar3;
  void *__ptr;
  int local_20 [6];
  
  local_20[1] = 0;
  local_20[2] = 0;
  local_20[3] = 0;
  local_20[4] = 0;
  apmib_get(0x1b5b,local_20);
  if (local_20[0] == 1) {
    memcpy(local_20 + 1,"ByDevice",9);
  }
  else if (local_20[0] == 2) {
    memcpy(local_20 + 1,"ByApplication",14);
  }
  else {
    memcpy(local_20 + 1,"Off",4);
  }
  iVar1 = mxmlNewXML("1.0");
  if (iVar1 == 0) {
    puts("Create new xml erro!!!");
  }
  else {
    iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
    if (iVar2 == 0) {
      puts("Create new element error!!!");
      mxmlDelete(iVar1);
    }
    else {
      mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
      mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
      mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
      iVar2 = mxmlNewElement(iVar2,"soap:Body");
      if (iVar2 == 0) {
        puts("Create new element error!!!");
        mxmlDelete(iVar1);
      }
      else {
        iVar2 = mxmlNewElement(iVar2,"GetQoSManagementTypeResponse");
        if (iVar2 == 0) {
          puts("Create new element error!!!");
          mxmlDelete(iVar1);
        }
        else {
          mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
          iVar3 = mxmlNewElement(iVar2,"GetQoSManagementTypeResult");
          if (iVar3 == 0) {
            puts("Create new element error!!!");
            mxmlDelete(iVar1);
          }
          else {
            iVar3 = mxmlNewText(iVar3,0,"O");
            if (iVar3 == 0) {
              puts("Create new text error!!!");
              mxmlDelete(iVar1);
            }
            else {
              iVar2 = mxmlNewElement(iVar2,"QoSManagementType");
              if (iVar2 == 0) {
                puts("Create new element error!!!");
                mxmlDelete(iVar1);
              }
              else {
                iVar2 = mxmlNewText(iVar2,0,local_20 + 1);
                if (iVar2 == 0) {
                  puts("Create new text error!!!");
                  mxmlDelete(iVar1);
                }
                else {
                  __ptr = mxmlSaveAllocString(iVar1,0);
                  if (__ptr != 0) {
                    FUN_0041ed70("",200,__ptr,"");
                    free(__ptr);
                  }
                  mxmlDelete(iVar1);
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

