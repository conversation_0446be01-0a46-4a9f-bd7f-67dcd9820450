
undefined4 FUN_00467554(int param_1)

{
  bool bVar1;
  int iVar2;
  int iVar3;
  char *__s1;
  void *__ptr;
  int local_5c;
  int local_30;
  undefined auStack_2c [36];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetWifiDownSettings");
  }
  else {
    local_30 = 0;
    bVar1 = false;
    iVar2 = mxmlLoadString(0,param_1,0);
    if (iVar2 == 0) {
      puts("ERROR!  tree is NULL");
    }
    else {
      iVar3 = apmib_set(0x41ce2,auStack_2c);
      if (iVar3 == 0) {
        puts("ERROR! Del all failed! exit!");
        mxmlDelete(iVar2);
      }
      else {
        apmib_update(4);
        local_5c = mxmlFindElement(iVar2,iVar2,"ControlMode",0,0,1);
        if ((local_5c != 0) && (__s1 = mxmlGetText(local_5c,0), __s1 != 0)) {
          iVar3 = strncmp(__s1,"true",4);
          if (iVar3 == 0) {
            local_30 = 1;
          }
          else {
            iVar3 = strncmp(__s1,"false",5);
            if (iVar3 == 0) {
              local_30 = 0;
            }
            else {
              puts("ERROR! AUTO WIFI ENABLED Unrecognized");
            }
          }
          iVar3 = apmib_set(0x1cdd,&local_30);
          if (iVar3 == 0) {
            puts("apmib set MIB_AUTO_WIFI_ENABLED error!");
          }
        }
        while ((local_5c != 0 &&
               (local_5c = mxmlFindElement(local_5c,iVar2,"ControlRule",0,0,1), local_5c != 0))) {
          iVar3 = FUN_00467210(local_5c,iVar2);
          if (iVar3 == 1) {
            bVar1 = true;
          }
        }
        mxmlDelete(iVar2);
        iVar2 = mxmlNewXML("1.0");
        if (iVar2 == 0) {
          printf("Create new xml erro!!!");
        }
        else {
          iVar3 = mxmlNewElement(iVar2,"soap:Envelope");
          if (iVar3 == 0) {
            mxmlDelete(iVar2);
            puts("soap_env=NULL");
          }
          else {
            mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
            mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
            mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
            iVar3 = mxmlNewElement(iVar3,"soap:Body");
            if (iVar3 == 0) {
              mxmlDelete(iVar2);
              puts("body=NULL");
            }
            else {
              iVar3 = mxmlNewElement(iVar3,"SetWifiDownSettingsResponse");
              if (iVar3 == 0) {
                mxmlDelete(iVar2);
                puts("SetWifiDownSettingsResponse=NULL");
              }
              else {
                mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
                iVar3 = mxmlNewElement(iVar3,"SetWifiDownSettingsResult");
                if (iVar3 == 0) {
                  mxmlDelete(iVar2);
                  puts("SetWifiDownSettingsResult=NULL");
                }
                else {
                  mxmlNewText(iVar3,0,"O");
                  apmib_update(4);
                  if (("" == 0) &&
                     (__ptr = mxmlSaveAllocString(iVar2,0), __ptr != 0)) {
                    FUN_0041ed70("",200,__ptr,"");
                    free(__ptr);
                    unlink("/tmp/wifiLock");
                    unlink("/tmp/manually_open_WiFi");
                    if ((local_30 == 1) && (bVar1)) {
                      system("echo 1 > /tmp/wifiLock");
                    }
                    system("init.sh gw all&");
                  }
                  mxmlDelete(iVar2);
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

