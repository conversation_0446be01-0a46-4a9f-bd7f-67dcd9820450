
/* WARNING: Removing unreachable block (ram,FUN_004444bc) */

int FUN_004444bc(undefined4 param_1)

{
  int iVar1;
  short sVar5;
  char *pcVar2;
  in_addr_t iVar3;
  char *pcVar4;
  undefined1 *puVar6;
  int iVar7;
  uint local_150;
  undefined auStack_14c [4];
  uint local_148 [2];
  undefined auStack_140 [16];
  uint local_130;
  in_addr_t local_12c;
  in_addr_t local_128;
  uint local_124;
  char local_120 [30];
  undefined local_102;
  in_addr_t local_100;
  in_addr_t local_fc;
  in_addr_t local_f8;
  undefined *local_f4;
  undefined4 local_f0;
  undefined *local_ec;
  undefined4 local_e8;
  undefined *local_e4;
  undefined4 local_e0;
  undefined *local_dc;
  undefined4 local_d8;
  undefined *local_d4;
  undefined4 local_d0;
  undefined *local_cc;
  undefined4 local_c8;
  undefined *local_c4;
  undefined4 local_c0;
  undefined *local_bc;
  undefined4 local_b8;
  undefined *local_b4;
  undefined4 local_b0;
  undefined *local_ac;
  undefined4 local_a8;
  undefined4 local_a4;
  undefined auStack_9c [4];
  undefined auStack_98 [16];
  undefined auStack_88 [16];
  undefined auStack_78 [4];
  undefined auStack_74 [16];
  undefined auStack_64 [31];
  undefined auStack_45 [16];
  undefined auStack_35 [17];
  undefined auStack_24 [4];
  
  local_f4 = auStack_9c;
  local_ec = auStack_98;
  local_e4 = auStack_88;
  local_dc = auStack_78;
  local_d4 = auStack_74;
  local_cc = auStack_64;
  local_c4 = auStack_45;
  local_bc = auStack_35;
  local_b4 = auStack_24;
  local_ac = auStack_14c;
  local_b8 = 16;
  local_e8 = 16;
  local_e0 = 16;
  local_d0 = 16;
  local_c0 = 16;
  local_c8 = 31;
  local_a4 = 0;
  local_f0 = 0;
  local_d8 = 0;
  local_b0 = 0;
  local_a8 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar5 = HttpDenyPage(param_1);
    goto LAB_00444d7c;
  }
  memset(&local_130,0,"<");
  iVar1 = httpGetEnv(param_1,"Save");
  if (iVar1 == 0) {
LAB_00444aa8:
    memset(&local_130,0,"<");
    swGetDhcpsCfg(&local_130);
    local_150 = local_130;
    pageParaSet(&local_f4,&local_150,0);
    swIpAddr2Str(local_12c,auStack_140);
    pageParaSet(&local_f4,auStack_140,1);
    swIpAddr2Str(local_128,auStack_140);
    pageParaSet(&local_f4,auStack_140,2);
    local_150 = local_124 / "<";
    pageParaSet(&local_f4,&local_150,3);
    swIpAddr2Str(local_f8,auStack_140);
    pageParaSet(&local_f4,auStack_140,4);
    pageParaSet(&local_f4,local_120,5);
    swIpAddr2Str(local_100,auStack_140);
    pageParaSet(&local_f4,auStack_140,6);
    swIpAddr2Str(local_fc,auStack_140);
    pageParaSet(&local_f4,auStack_140,7);
    local_150 = swDhcpsCfgIsChanged();
    pageParaSet(&local_f4,&local_150,8);
    swGetSystemMode(local_148);
    local_150 = local_148[0];
    pageParaSet(&local_f4,&local_150,9);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "DHCPPara");
    iVar1 = 0;
    do {
      iVar7 = iVar1 + 1;
      pageDynParaPrintf(&local_f4,iVar1,param_1);
      iVar1 = iVar7;
    } while (iVar7 != 10);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/LanDhcpServerRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    iVar1 = 10;
    puVar6 = 0;
  }
  else {
    local_130 = getEnvToInt(param_1,"dhcpserver",0x80000000,0x7fffffff);
    if (local_130 == 0xffffff80) {
      local_130 = 0;
    }
    pcVar2 = httpGetEnv(param_1,"ip1");
    if (pcVar2 != 0) {
      do {
        pcVar4 = pcVar2;
        pcVar2 = pcVar4 + 1;
      } while (*pcVar4 == ' ');
      if ((pcVar4 == 0) || (*pcVar4 == '\0')) {
        local_12c = 0;
        goto LAB_004446e8;
      }
      iVar1 = swChkDotIpAddr(pcVar4);
      if (iVar1 != 0) {
        local_12c = inet_addr(pcVar4);
        goto LAB_004446e8;
      }
      puVar6 = "";
      iVar1 = 0x7d5;
      goto LAB_00444d74;
    }
    local_12c = 0;
LAB_004446e8:
    pcVar2 = httpGetEnv(param_1,"ip2");
    if (pcVar2 == 0) {
      local_128 = 0;
    }
    else {
      do {
        pcVar4 = pcVar2;
        pcVar2 = pcVar4 + 1;
      } while (*pcVar4 == ' ');
      if ((pcVar4 == 0) || (*pcVar4 == '\0')) {
        local_128 = 0;
      }
      else {
        iVar1 = swChkDotIpAddr(pcVar4);
        if (iVar1 == 0) {
          puVar6 = "";
          iVar1 = 0x7d6;
          goto LAB_00444d74;
        }
        local_128 = inet_addr(pcVar4);
      }
    }
    iVar1 = getEnvToInt(param_1,"Lease",0x80000000,0x7fffffff);
    if (iVar1 != -128) {
      local_124 = iVar1 * "<";
    }
    pcVar2 = httpGetEnv(param_1,"gateway");
    if (pcVar2 == 0) {
      local_f8 = 0;
      iVar3 = local_f8;
LAB_004448a0:
      local_f8 = iVar3;
      pcVar2 = httpGetEnv(param_1,"domain");
      if (pcVar2 == 0) {
        local_120[0] = '\0';
      }
      else {
        do {
          pcVar4 = pcVar2;
          pcVar2 = pcVar4 + 1;
        } while (*pcVar4 == ' ');
        if ((pcVar4 == 0) || (*pcVar4 == '\0')) {
          local_120[0] = '\0';
        }
        else {
          local_102 = 0;
          strncpy(local_120,pcVar4,30);
        }
      }
      pcVar2 = httpGetEnv(param_1,"dnsserver");
      if (pcVar2 == 0) {
        local_100 = 0;
      }
      else {
        do {
          pcVar4 = pcVar2;
          pcVar2 = pcVar4 + 1;
        } while (*pcVar4 == ' ');
        if ((pcVar4 == 0) || (*pcVar4 == '\0')) {
          local_100 = 0;
        }
        else {
          iVar1 = swChkDotIpAddr(pcVar4);
          if (iVar1 == 0) {
            puVar6 = "";
            iVar1 = 0x7d2;
            goto LAB_00444d74;
          }
          local_100 = inet_addr(pcVar4);
        }
      }
      pcVar2 = httpGetEnv(param_1,"dnsserver2");
      if (pcVar2 == 0) {
        local_fc = 0;
      }
      else {
        do {
          pcVar4 = pcVar2;
          pcVar2 = pcVar4 + 1;
        } while (*pcVar4 == ' ');
        if ((pcVar4 == 0) || (*pcVar4 == '\0')) {
          local_fc = 0;
        }
        else {
          iVar1 = swChkDotIpAddr(pcVar4);
          if (iVar1 == 0) {
            puVar6 = "";
            iVar1 = 0x7d3;
            goto LAB_00444d74;
          }
          local_fc = inet_addr(pcVar4);
        }
      }
      sVar5 = swChkDhcpsCfg(&local_130);
      iVar1 = sVar5;
      if (iVar1 == 0) {
        iVar1 = swSetDhcpsCfg(&local_130);
        if (iVar1 == 0) {
          "" = 1;
        }
        goto LAB_00444aa8;
      }
      puVar6 = "";
    }
    else {
      do {
        pcVar4 = pcVar2;
        pcVar2 = pcVar4 + 1;
      } while (*pcVar4 == ' ');
      if ((pcVar4 == 0) || (*pcVar4 == '\0')) {
        local_f8 = 0;
        iVar3 = local_f8;
        goto LAB_004448a0;
      }
      iVar1 = swChkDotIpAddr(pcVar4);
      if (iVar1 == 0) {
        puVar6 = "";
        iVar1 = 0x7d1;
      }
      else {
        iVar3 = inet_addr(pcVar4);
        if ((iVar3 == 0) || (iVar1 = swChkSameLanSubnet(iVar3), iVar1 != 0)) goto LAB_004448a0;
        puVar6 = "";
        iVar1 = 0x65fa;
      }
    }
  }
LAB_00444d74:
  sVar5 = HttpErrorPage(param_1,iVar1,puVar6,0);
LAB_00444d7c:
  return sVar5;
}

