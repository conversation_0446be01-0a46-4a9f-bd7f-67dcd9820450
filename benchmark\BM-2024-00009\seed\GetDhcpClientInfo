POST /HNAP1/ HTTP/1.1
Host: **********
User-Agent: Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/110.0
Accept: application/json
Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2
Accept-Encoding: gzip, deflate
Content-Type: application/json
SOAPACTION: "http://purenetworks.com/HNAP1/GetDhcpClientInfo"
HNAP_AUTH: E5C14446F7156A0DE9E56D8ED83DAA45 1677385974303
Content-Length: 25
Origin: http://**********
Connection: keep-alive
Referer: http://**********/Network.html?t=1677385907815
Cookie: PHPSESSID=03a12b4b0d9ace632e47b3bb41124651; timeout=0

{"GetDhcpClientInfo": {}}