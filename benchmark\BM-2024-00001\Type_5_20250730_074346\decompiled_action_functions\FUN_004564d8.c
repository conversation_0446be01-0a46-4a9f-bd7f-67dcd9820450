
int FUN_004564d8(undefined4 param_1)

{
  int iVar1;
  int iVar2;
  short sVar4;
  char *pcVar3;
  void *__src;
  undefined4 local_270;
  undefined4 local_26c;
  undefined4 local_268;
  undefined auStack_264 [8];
  undefined auStack_25c [20];
  char *local_248;
  undefined4 local_244;
  undefined *local_240;
  undefined4 local_23c;
  undefined4 *local_238;
  undefined4 local_234;
  undefined4 *local_230;
  undefined4 local_22c;
  undefined4 *local_228;
  undefined4 local_224;
  undefined4 local_220;
  char acStack_218 [256];
  undefined auStack_118 [256];
  
  iVar1 = swGetUpgradeMode();
  local_270 = 0;
  local_26c = 0;
  memset(acStack_218,0,256);
  memset(auStack_118,0,256);
  local_268 = sysGetUpgradeTime();
  local_238 = &local_270;
  local_230 = &local_26c;
  local_228 = &local_268;
  local_23c = 256;
  local_220 = 0;
  local_244 = 256;
  local_234 = 0;
  local_22c = 0;
  local_224 = 0;
  local_248 = acStack_218;
  local_240 = auStack_118;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    sVar4 = HttpDenyPage(param_1);
  }
  else {
    if (iVar1 == 1) {
      HttpClientMacGet(param_1,auStack_25c);
      swMacStr2Eth(auStack_25c,auStack_264);
      local_270 = swWlanStaIsAssociated(auStack_264);
    }
    pcVar3 = getSysSoftwareRevisionPrefix();
    strcpy(acStack_218,pcVar3);
    pcVar3 = getSysSoftwareRevision();
    strcat(acStack_218,pcVar3);
    __src = getSysHardwareRevision();
    memcpy(auStack_118,__src,256);
    iVar1 = HttpIsAccessFromLAN(param_1);
    if ((iVar1 == 0) && (iVar1 = getForbiddenWanUpgrade(), iVar1 != 0)) {
      local_26c = 1;
    }
    else {
      local_26c = 0;
    }
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "softUpInf");
    pageDynParaPrintf(&local_248,0,param_1);
    pageDynParaPrintf(&local_248,1,param_1);
    pageDynParaPrintf(&local_248,2,param_1);
    pageDynParaPrintf(&local_248,3,param_1);
    pageDynParaPrintf(&local_248,4,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/SoftwareUpgradeRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    sVar4 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar4;
}

