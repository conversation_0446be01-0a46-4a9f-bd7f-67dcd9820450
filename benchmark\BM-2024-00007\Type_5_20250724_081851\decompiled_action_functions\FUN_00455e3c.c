
int FUN_00455e3c(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  undefined4 uVar3;
  char *pcVar4;
  int iVar5;
  uint uVar6;
  uint *puVar7;
  undefined *puVar8;
  int iVar9;
  ushort *puVar10;
  ushort *puVar11;
  code *pcVar12;
  undefined auStack_9f8 [4];
  uint uStack_9f4;
  int iStack_9f0;
  undefined4 uStack_9ec;
  undefined4 uStack_9e8;
  undefined auStack_9e4 [36];
  undefined auStack_9c0 [132];
  undefined *puStack_93c;
  undefined4 uStack_938;
  undefined *puStack_934;
  undefined4 uStack_930;
  undefined *puStack_92c;
  undefined4 uStack_928;
  undefined *puStack_924;
  undefined4 uStack_920;
  undefined *puStack_91c;
  undefined4 uStack_918;
  undefined *puStack_914;
  undefined4 uStack_910;
  undefined *puStack_90c;
  undefined4 uStack_908;
  undefined *puStack_904;
  undefined4 uStack_900;
  int *piStack_8fc;
  undefined4 uStack_8f8;
  int *piStack_8f4;
  undefined4 uStack_8f0;
  int *piStack_8ec;
  undefined4 uStack_8e8;
  int *piStack_8e4;
  undefined4 uStack_8e0;
  int *piStack_8dc;
  undefined4 uStack_8d8;
  int *piStack_8d4;
  undefined4 uStack_8d0;
  int *piStack_8cc;
  undefined4 uStack_8c8;
  int *piStack_8c4;
  undefined4 uStack_8c0;
  int *piStack_8bc;
  undefined4 uStack_8b8;
  undefined *puStack_8b4;
  undefined4 uStack_8b0;
  undefined *puStack_8ac;
  undefined4 uStack_8a8;
  undefined *puStack_8a4;
  undefined4 uStack_8a0;
  undefined *puStack_89c;
  undefined4 uStack_898;
  int *piStack_894;
  undefined4 uStack_890;
  int *piStack_88c;
  undefined4 uStack_888;
  int *piStack_884;
  undefined4 uStack_880;
  int *piStack_87c;
  undefined4 uStack_878;
  int *piStack_874;
  undefined4 uStack_870;
  int *piStack_86c;
  undefined4 uStack_868;
  undefined *puStack_864;
  undefined4 uStack_860;
  int *piStack_85c;
  undefined4 uStack_858;
  int *piStack_854;
  undefined4 uStack_850;
  undefined *puStack_84c;
  undefined4 uStack_848;
  undefined *puStack_844;
  undefined4 uStack_840;
  int *piStack_83c;
  undefined4 uStack_838;
  uint *puStack_834;
  undefined4 uStack_830;
  uint *puStack_82c;
  undefined4 uStack_828;
  undefined4 uStack_824;
  undefined auStack_81c [8];
  undefined auStack_814 [4];
  undefined auStack_810 [33];
  undefined auStack_7ef [18];
  undefined auStack_7dd [18];
  undefined auStack_7cb [18];
  undefined auStack_7b9 [33];
  undefined auStack_798 [40];
  undefined auStack_770 [18];
  undefined auStack_75e [18];
  undefined auStack_74c [18];
  undefined auStack_73a [70];
  undefined auStack_6f4 [4];
  undefined auStack_6f0 [4];
  int iStack_6ec;
  undefined uStack_6e8;
  undefined uStack_6e6;
  undefined auStack_654 [80];
  uint uStack_604;
  uint uStack_600;
  char cStack_5fc;
  undefined uStack_5fb;
  ushort uStack_5fa;
  undefined auStack_5f8 [84];
  undefined auStack_5a4 [4];
  undefined auStack_5a0 [8];
  undefined auStack_598 [4];
  undefined auStack_594 [4];
  undefined auStack_590 [8];
  undefined auStack_588 [36];
  undefined auStack_564 [4];
  undefined auStack_560 [4];
  undefined auStack_55c [36];
  undefined auStack_538 [4];
  undefined auStack_534 [4];
  undefined auStack_530 [36];
  undefined auStack_50c [4];
  undefined auStack_508 [4];
  undefined auStack_504 [36];
  undefined auStack_4e0 [4];
  int iStack_4dc;
  undefined auStack_4d8 [4];
  int iStack_4d4;
  int aiStack_4d0 [3];
  undefined auStack_4c4 [4];
  undefined auStack_4c0 [8];
  undefined auStack_4b8 [33];
  char acStack_497 [18];
  char cStack_485;
  undefined auStack_484 [4];
  undefined auStack_480 [4];
  undefined auStack_47c [4];
  undefined auStack_478 [68];
  undefined auStack_434 [8];
  undefined auStack_42c [376];
  undefined auStack_2b4 [33];
  undefined auStack_293 [19];
  undefined auStack_280 [33];
  undefined auStack_25f [19];
  undefined auStack_24c [19];
  undefined auStack_239 [19];
  undefined auStack_226 [19];
  undefined auStack_213 [19];
  int iStack_200;
  int iStack_1fc;
  int iStack_1f8;
  int iStack_1f4;
  int iStack_1f0;
  int iStack_1ec;
  int iStack_1e8;
  int iStack_1e4;
  int iStack_1e0;
  undefined auStack_1dc [66];
  undefined auStack_19a [66];
  undefined auStack_158 [66];
  undefined auStack_116 [66];
  int iStack_d4;
  int iStack_d0;
  int iStack_cc;
  int iStack_c8;
  int iStack_c4;
  int iStack_c0;
  undefined auStack_bc [68];
  int iStack_78;
  int iStack_74;
  undefined auStack_70 [33];
  undefined auStack_4f [19];
  int iStack_3c;
  uint uStack_38;
  uint uStack_34;
  undefined *puStack_30;
  undefined *puStack_2c;
  
  iStack_9f0 = 0;
  uStack_9e8 = 0;
  uStack_9e8 = swIsMultiSystemMode();
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  puStack_93c = auStack_2b4;
  if (iVar2 == 0) {
    iVar2 = HttpDenyPage(param_1);
    iVar2 = iVar2 << 16;
LAB_00454970:
    iVar2 = iVar2 >> 16;
  }
  else {
    memset(puStack_93c,0,644);
    puStack_934 = auStack_293;
    puStack_92c = auStack_280;
    puStack_924 = auStack_25f;
    puStack_91c = auStack_24c;
    puStack_914 = auStack_239;
    puStack_90c = auStack_226;
    puStack_904 = auStack_213;
    piStack_8fc = &iStack_200;
    piStack_8f4 = &iStack_1fc;
    piStack_8ec = &iStack_1f8;
    piStack_8e4 = &iStack_1f4;
    piStack_8dc = &iStack_1f0;
    piStack_8d4 = &iStack_1ec;
    piStack_8cc = &iStack_1e8;
    piStack_8c4 = &iStack_1e4;
    piStack_8bc = &iStack_1e0;
    puStack_8b4 = auStack_1dc;
    puStack_8ac = auStack_19a;
    puStack_8a4 = auStack_158;
    puStack_89c = auStack_116;
    piStack_894 = &iStack_d4;
    piStack_88c = &iStack_d0;
    piStack_884 = &iStack_cc;
    piStack_87c = &iStack_c8;
    piStack_874 = &iStack_c4;
    piStack_86c = &iStack_c0;
    puStack_864 = auStack_bc;
    piStack_85c = &iStack_78;
    piStack_854 = &iStack_74;
    puStack_84c = auStack_70;
    puStack_844 = auStack_4f;
    piStack_83c = &iStack_3c;
    puStack_834 = &uStack_38;
    puStack_82c = &uStack_34;
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    uStack_824 = 0;
    uStack_8f8 = 0;
    uStack_8f0 = 0;
    uStack_8e8 = 0;
    uStack_8e0 = 0;
    uStack_8d8 = 0;
    uStack_8d0 = 0;
    uStack_8c8 = 0;
    uStack_8c0 = 0;
    uStack_8b8 = 0;
    uStack_890 = 0;
    uStack_888 = 0;
    uStack_880 = 0;
    uStack_878 = 0;
    uStack_870 = 0;
    uStack_868 = 0;
    uStack_858 = 0;
    uStack_850 = 0;
    
    uStack_838 = 0;
    uStack_830 = 0;
    uStack_828 = 0;
    uVar3 = swWlanGetRealIdxByVidx(0,0);
    swWlanSecurityCfgGet(auStack_6f4,uVar3,0);
    swWlanBasicCfgGet(0,auStack_5a4);
    swWlanModeConfigGet(0,auStack_81c);
    pcVar4 = httpGetEnv(param_1,"tmp_region_index");
    if (pcVar4 != 0) {
      swWlanBasicCfgGet(0,auStack_42c);
      iStack_4dc = atoi(pcVar4);
      httpWlanBasicCfg_newForAp._200_4_ = atoi(pcVar4);
      pcVar4 = httpGetEnv(param_1,"tmp_mode");
      if (pcVar4 != 0) {
        aiStack_4d0[0] = atoi(pcVar4);
        httpWlanBasicCfg_newForAp._212_4_ = atoi(pcVar4);
      }
      pcVar4 = httpGetEnv(param_1,"tmp_channel");
      if (pcVar4 != 0) {
        iStack_4d4 = atoi(pcVar4);
        httpWlanBasicCfg_newForAp._208_4_ = atoi(pcVar4);
      }
      swWlanBasicDynSet(0,auStack_42c,auStack_5a4);
    }
    iVar2 = httpGetEnv(param_1,"Next");
    if (iVar2 == 0) {
      iVar2 = httpGetEnv(param_1,"Return");
      if (iVar2 != 0) {
        pcVar12 = wzdStepFindPrev;
        goto LAB_00454944;
      }
      memcpy(auStack_5a4,httpWlanBasicCfg_newForAp,376);
      memcpy(auStack_81c,httpWlanModeCfg_newForAp,296);
      memcpy(auStack_6f4,httpWlanSecCfg_newForAp,336);
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "wzdWlanInf");
      uStack_9f4 = 0;
      writePageParamSet(param_1,"%d,",auStack_5a4,0);
      writePageParamSet(param_1,""%s",",auStack_588,1);
      writePageParamSet(param_1,"%d,",auStack_598,2);
      writePageParamSet(param_1,"%d,",auStack_4e0,3);
      writePageParamSet(param_1,"%d,",&iStack_4dc,4);
      puVar7 = &uStack_9f4;
      writePageParamSet(param_1,"%d,",puVar7,5);
      writePageParamSet(param_1,"%d,",aiStack_4d0,6);
      writePageParamSet(param_1,"%d,",auStack_9f8,7);
      writePageParamSet(param_1,"%d,",puVar7,8);
      writePageParamSet(param_1,"%d,",auStack_4d8,9);
      writePageParamSet(param_1,"%d,",&iStack_4d4,10);
      writePageParamSet(param_1,"%d,",auStack_4c4,11);
      uStack_9f4 = swIsSysModeChange();
      writePageParamSet(param_1,"%d,",puVar7,12);
      writePageParamSet(param_1,"%d,",auStack_6f0,13);
      writePageParamSet(param_1,"%d,",&iStack_6ec,14);
      if (iStack_6ec == 2) {
        puVar7 = &uStack_604;
      }
      else if (iStack_6ec == 3) {
        puVar7 = &uStack_600;
      }
      else {
        uStack_9f4 = 1;
      }
      writePageParamSet(param_1,"%d,",puVar7,15);
      writePageParamSet(param_1,"%d,",auStack_4c0,16);
      writePageParamSet(param_1,""%s",",auStack_654,17);
      uStack_9ec = swGetRateTableLength();
      writePageParamSet(param_1,"%d,",&uStack_9ec,18);
      writePageParamSet(param_1,"%d,",auStack_5a0,19);
      writePageParamSet(param_1,""%s",",auStack_7cb,20);
      writePageParamSet(param_1,"%d,",auStack_594,21);
      writePageParamSet(param_1,""%s",",auStack_55c,22);
      writePageParamSet(param_1,""%s",",auStack_530,23);
      writePageParamSet(param_1,""%s",",auStack_504,24);
      writePageParamSet(param_1,"%d,",auStack_814,25);
      writePageParamSet(param_1,""%s",",auStack_810,26);
      writePageParamSet(param_1,""%s",",auStack_7ef,27);
      writePageParamSet(param_1,""%s",",auStack_7dd,28);
      writePageParamSet(param_1,""%s",",auStack_7cb,29);
      writePageParamSet(param_1,""%s",",auStack_770,30);
      writePageParamSet(param_1,""%s",",auStack_75e,31);
      writePageParamSet(param_1,""%s",",auStack_74c," ");
      writePageParamSet(param_1,""%s",",auStack_73a,"!");
      writePageParamSet(param_1,"%d,",auStack_590,""");
      writePageParamSet(param_1,"%d,",auStack_564,"#");
      writePageParamSet(param_1,"%d,",auStack_538,"$");
      writePageParamSet(param_1,"%d,",auStack_50c,"%");
      writePageParamSet(param_1,"%d,",auStack_434,"&");
      writePageParamSet(param_1,"%d,",0xffffffff,"'");
      writePageParamSet(param_1,"%d,",auStack_560,"(");
      writePageParamSet(param_1,"%d,",auStack_534,")");
      writePageParamSet(param_1,"%d,",auStack_508,"*");
      writePageParamSet(param_1,""%s",",auStack_7b9,"+");
      writePageParamSet(param_1,""%s",",auStack_798,",");
      writePageParamSet(param_1,"%d,",&uStack_9e8,"-");
      writePageParamSet(param_1,"%d,",&show_rootap,".");
      iVar2 = getProductId();
      if ((iVar2 == 0x8020001) || (iVar2 = getProductId(), iVar2 == 0x9010004)) {
        uStack_9f4 = 1;
        writePageParamSet(param_1,"%d,",&uStack_9f4,"/");
      }
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      pcVar4 = acStack_497;
      if (0xffffffff == 6) {
        do {
          if (*pcVar4 == '\0') break;
          if (*pcVar4 == ':') {
            *pcVar4 = '-';
          }
          pcVar4 = pcVar4 + 1;
        } while (pcVar4 != &cStack_485);
        httpPrintf(param_1,
                   "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n"
                   ,"wzdApBridgePara");
        writePageParamSet(param_1,""%s",",auStack_4b8,0);
        writePageParamSet(param_1,""%s",",acStack_497,1);
        writePageParamSet(param_1,"%d,",auStack_484,2);
        writePageParamSet(param_1,"%d,",auStack_480,3);
        writePageParamSet(param_1,"%d,",auStack_47c,4);
        writePageParamSet(param_1,""%s",",auStack_478,5);
        httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      }
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "wlanList");
      puVar11 = &uStack_5fa;
      iVar2 = 0;
      iVar9 = 1;
      puStack_30 = auStack_6f4;
      puStack_2c = auStack_9e4;
      puVar10 = puVar11;
      do {
        if (*puVar10 == 0) {
          writePageParamSet(param_1,""%s",","");
          uStack_9f4 = 0;
        }
        else if (cStack_5fc == '\x01') {
          iVar5 = KeytoASC(puStack_30 + iVar2 * 0x0000010e,puStack_2c);
          if (iVar5 != 1) {
            iVar2 = HttpErrorPage(param_1,0x65f6,"",0);
            return iVar2;
          }
          writePageParamSet(param_1,""%s",",puStack_2c,iVar2 << 1);
          uStack_9f4 = (uint)*puVar10;
        }
        else {
          writePageParamSet(param_1,""%s",",puStack_30 + iVar2 * 0x0000010e,iVar2 << 1);
          uStack_9f4 = (uint)*puVar10;
        }
        iVar2 = iVar2 + 1;
        writePageParamSet(param_1,"%d,",&uStack_9f4,iVar9);
        puVar10 = puVar10 + 9;
        iVar9 = iVar9 + 2;
      } while (iVar2 != 4);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "secInf");
      httpPrintf(param_1,"%d, ",uStack_6e8);
      httpPrintf(param_1,"%d, ",cStack_5fc);
      iVar2 = 0;
      httpPrintf(param_1,"%d, ",uStack_5fb);
      do {
        memset(auStack_9c0,0,129);
        if (*puVar11 != 0) {
          memset(auStack_9e4,0,"!");
          if (cStack_5fc == '\x01') {
            KeytoASC(auStack_5f8 + iVar2 * 18,auStack_9e4,*puVar11);
            puVar8 = auStack_9e4;
          }
          else {
            puVar8 = auStack_5f8 + iVar2 * 18;
          }
          stringModify(auStack_9c0,129,puVar8);
        }
        iVar2 = iVar2 + 1;
        httpPrintf(param_1,""%s", ",auStack_9c0);
        puVar11 = puVar11 + 9;
      } while (iVar2 != 4);
      iVar2 = 0;
      do {
        memcpy(auStack_6f4,httpWlanSecCfg_newForAp,336);
        iVar2 = iVar2 + 1;
        httpPrintf(param_1,"%d, ",uStack_6e6);
        httpPrintf(param_1,"%d, ",uStack_600);
        stringModify(auStack_9c0,129,auStack_654);
        httpPrintf(param_1,""%s", ",auStack_9c0);
        httpPrintf(param_1,"%d, ",iStack_6ec);
      } while (iVar2 != 4);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      iVar2 = httpGetEnv(param_1,"ssid_client");
      if (iVar2 != 0) {
        MEMCPY(auStack_2b4,iVar2," ");
      }
      iVar2 = httpGetEnv(param_1,"mac_client");
      if (iVar2 != 0) {
        MEMCPY(auStack_293,iVar2,18);
      }
      iVar2 = httpGetEnv(param_1,"rptSsid");
      if (iVar2 != 0) {
        MEMCPY(auStack_280,iVar2," ");
      }
      iVar2 = httpGetEnv(param_1,"rptBssid");
      if (iVar2 != 0) {
        MEMCPY(auStack_25f,iVar2,18);
      }
      iVar2 = httpGetEnv(param_1,"mptBssid1");
      if (iVar2 != 0) {
        MEMCPY(auStack_24c,iVar2,18);
      }
      iVar2 = httpGetEnv(param_1,"mptBssid2");
      if (iVar2 != 0) {
        MEMCPY(auStack_239,iVar2,18);
      }
      iVar2 = httpGetEnv(param_1,"mptBssid3");
      if (iVar2 != 0) {
        MEMCPY(auStack_226,iVar2,18);
      }
      iVar2 = httpGetEnv(param_1,"mptBssid4");
      if (iVar2 != 0) {
        MEMCPY(auStack_213,iVar2,18);
      }
      pcVar4 = httpGetEnv(param_1,"channel");
      if (pcVar4 != 0) {
        iStack_200 = atoi(pcVar4);
      }
      pcVar4 = httpGetEnv(param_1,"wds_enable");
      if (pcVar4 != 0) {
        iStack_1fc = atoi(pcVar4);
      }
      pcVar4 = httpGetEnv(param_1,"Region");
      if (pcVar4 != 0) {
        iStack_1f8 = atoi(pcVar4);
      }
      pcVar4 = httpGetEnv(param_1,"uni_wds_r");
      if (pcVar4 != 0) {
        iStack_1f4 = atoi(pcVar4);
      }
      pcVar4 = httpGetEnv(param_1,"secType");
      if (pcVar4 != 0) {
        iStack_1f0 = atoi(pcVar4);
      }
      pcVar4 = httpGetEnv(param_1,"wepSecret");
      if (pcVar4 != 0) {
        iStack_1ec = atoi(pcVar4);
      }
      pcVar4 = httpGetEnv(param_1,"wepSecOpt");
      if (pcVar4 != 0) {
        iStack_1e8 = atoi(pcVar4);
      }
      pcVar4 = httpGetEnv(param_1,"keytype");
      if (pcVar4 != 0) {
        iStack_1e4 = atoi(pcVar4);
      }
      pcVar4 = httpGetEnv(param_1,"keynum");
      if (pcVar4 != 0) {
        iStack_1e0 = atoi(pcVar4);
      }
      iVar2 = httpGetEnv(param_1,"key1");
      if (iVar2 != 0) {
        MEMCPY(auStack_1dc,iVar2,"A");
      }
      iVar2 = httpGetEnv(param_1,"key2");
      if (iVar2 != 0) {
        MEMCPY(auStack_19a,iVar2,"A");
      }
      iVar2 = httpGetEnv(param_1,"key3");
      if (iVar2 != 0) {
        MEMCPY(auStack_158,iVar2,"A");
      }
      iVar2 = httpGetEnv(param_1,"key4");
      if (iVar2 != 0) {
        MEMCPY(auStack_116,iVar2,"A");
      }
      pcVar4 = httpGetEnv(param_1,"length1");
      if (pcVar4 != 0) {
        iStack_d4 = atoi(pcVar4);
      }
      pcVar4 = httpGetEnv(param_1,"length2");
      if (pcVar4 != 0) {
        iStack_d0 = atoi(pcVar4);
      }
      pcVar4 = httpGetEnv(param_1,"length3");
      if (pcVar4 != 0) {
        iStack_cc = atoi(pcVar4);
      }
      pcVar4 = httpGetEnv(param_1,"length4");
      if (pcVar4 != 0) {
        iStack_c8 = atoi(pcVar4);
      }
      pcVar4 = httpGetEnv(param_1,"pskSecOpt");
      if (pcVar4 != 0) {
        iStack_c4 = atoi(pcVar4);
      }
      pcVar4 = httpGetEnv(param_1,"pskCipher");
      if (pcVar4 != 0) {
        iStack_c0 = atoi(pcVar4);
      }
      iVar2 = httpGetEnv(param_1,"pskSecret");
      if (iVar2 != 0) {
        MEMCPY(auStack_bc,iVar2,"A");
      }
      pcVar4 = httpGetEnv(param_1,"interval");
      if (pcVar4 != 0) {
        iStack_78 = atoi(pcVar4);
      }
      pcVar4 = httpGetEnv(param_1,"survey");
      if (pcVar4 != 0) {
        iStack_74 = atoi(pcVar4);
      }
      iVar2 = httpGetEnv(param_1,"survey_ssid");
      if (iVar2 != 0) {
        MEMCPY(auStack_70,iVar2," ");
      }
      iVar2 = httpGetEnv(param_1,"survey_mac");
      if (iVar2 != 0) {
        MEMCPY(auStack_4f,iVar2,18);
      }
      pcVar4 = httpGetEnv(param_1,"survey_channel");
      if (pcVar4 != 0) {
        iStack_3c = atoi(pcVar4);
      }
      pcVar4 = httpGetEnv(param_1,"survey_sec");
      if (pcVar4 != 0) {
        uVar6 = atoi(pcVar4);
        if (0xffffffff == 6) {
          if (uVar6 == 1) {
            uStack_38 = 2;
          }
          else {
            bVar1 = uVar6 < 4;
            uStack_38 = 4;
            if (uVar6 != 0) goto LAB_00455a98;
            uStack_38 = 1;
          }
        }
        else {
          bVar1 = uVar6 - 2 < 2;
          uStack_38 = 3;
LAB_00455a98:
          if (!bVar1) {
            uStack_38 = uVar6;
          }
        }
      }
      iVar2 = httpGetEnv(param_1,"connect");
      uStack_34 = (uint)(iVar2 != 0);
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "wzdSurvey");
      pageDynParaListPrintf(&puStack_93c,param_1);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      httpWizardPrintStepInfo(param_1);
      HttpWebV4Head(param_1,0,0);
      iVar2 = httpRpmFsA(param_1,"/userRpm/WzdWlanApRpm.htm");
      if (iVar2 != 2) {
        iVar2 = HttpErrorPage(param_1,10,0,0);
        iVar2 = iVar2 << 16;
        goto LAB_00454970;
      }
    }
    else {
      iVar2 = wlanNetworkOnSaveRpmHtmforWzdAp(0,param_1,1,0xffffffff);
      if (iVar2 == 2) {
        return 2;
      }
      pcVar12 = wzdStepFindNext;
LAB_00454944:
      iVar2 = (*pcVar12)(&iStack_9f0);
      if (iVar2 != 0) {
        iVar2 = GoUrl(param_1,iStack_9f0 + 8);
        iVar2 = iVar2 << 16;
        goto LAB_00454970;
      }
    }
    iVar2 = 2;
  }
  return iVar2;
}

