
int FUN_0043818c(undefined4 param_1)

{
  int iVar1;
  char *pcVar2;
  long lVar3;
  short sVar8;
  uint32_t uVar4;
  uint32_t uVar5;
  uint32_t uVar6;
  uint32_t uVar7;
  undefined1 *puVar9;
  char *pcVar10;
  int local_138;
  int local_134;
  undefined4 local_130 [2];
  char acStack_128 [16];
  in_addr_t local_118;
  in_addr_t local_114;
  in_addr_t local_110;
  in_addr_t local_10c;
  in_addr_t local_108;
  undefined4 local_100;
  undefined *local_f4;
  undefined4 local_f0;
  undefined *local_ec;
  undefined4 local_e8;
  undefined *local_e4;
  undefined4 local_e0;
  undefined *local_dc;
  undefined4 local_d8;
  undefined *local_d4;
  undefined4 local_d0;
  undefined *local_cc;
  undefined4 local_c8;
  undefined *local_c4;
  undefined4 local_c0;
  undefined4 *local_bc;
  undefined4 local_b8;
  undefined4 *local_b4;
  undefined4 local_b0;
  undefined4 *local_ac;
  undefined4 local_a8;
  undefined4 local_a4;
  undefined auStack_9c [4];
  undefined auStack_98 [16];
  undefined auStack_88 [16];
  undefined auStack_78 [16];
  undefined auStack_68 [16];
  undefined auStack_58 [16];
  undefined auStack_48 [4];
  undefined4 local_44;
  undefined4 local_40;
  undefined4 local_3c;
  int local_38;
  int *local_34;
  undefined **local_30;
  
  local_f4 = auStack_9c;
  local_ec = auStack_98;
  local_e4 = auStack_88;
  local_dc = auStack_78;
  local_d4 = auStack_68;
  local_cc = auStack_58;
  local_c4 = auStack_48;
  local_bc = &local_44;
  local_b4 = &local_40;
  local_ac = &local_3c;
  local_c8 = 16;
  local_e8 = 16;
  local_e0 = 16;
  local_d8 = 16;
  local_d0 = 16;
  local_138 = 0;
  local_134 = 0;
  local_a4 = 0;
  local_f0 = 0;
  local_c0 = 0;
  local_b8 = 0;
  local_b0 = 0;
  local_a8 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    iVar1 = HttpDenyPage(param_1);
    iVar1 = iVar1 << 16;
    goto LAB_004386d0;
  }
  pcVar2 = httpGetEnv(param_1,"ClientId");
  local_38 = 0;
  if (pcVar2 != 0) {
    lVar3 = atol(pcVar2);
    local_38 = (int)lVar3;
  }
  memset(&local_118,0,"$");
  iVar1 = httpGetEnv(param_1,"Return");
  if (iVar1 == 0) {
    iVar1 = httpGetEnv(param_1,"Next");
    if (iVar1 == 0) {
      local_34 = &local_138;
      local_138 = 0;
      pageParaSet(&local_f4,local_34,0);
      memset(&local_118,0,"$");
      swGetStaticIpCfg(0,&local_118);
      iVar1 = 0;
      uVar4 = ntohl(local_118);
      uVar5 = ntohl(local_118);
      local_30 = &local_f4;
      uVar6 = ntohl(local_118);
      uVar7 = ntohl(local_118);
      sprintf(acStack_128,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
              (int)(uVar6 & -256) >> 8,uVar7 & 255);
      pageParaSet(&local_f4,acStack_128,1);
      uVar4 = ntohl(local_114);
      uVar5 = ntohl(local_114);
      uVar6 = ntohl(local_114);
      uVar7 = ntohl(local_114);
      sprintf(acStack_128,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
              (int)(uVar6 & -256) >> 8,uVar7 & 255);
      pageParaSet(&local_f4,acStack_128,2);
      uVar4 = ntohl(local_110);
      uVar5 = ntohl(local_110);
      uVar6 = ntohl(local_110);
      uVar7 = ntohl(local_110);
      sprintf(acStack_128,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
              (int)(uVar6 & -256) >> 8,uVar7 & 255);
      pageParaSet(&local_f4,acStack_128,3);
      uVar4 = ntohl(local_10c);
      uVar5 = ntohl(local_10c);
      uVar6 = ntohl(local_10c);
      uVar7 = ntohl(local_10c);
      sprintf(acStack_128,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
              (int)(uVar6 & -256) >> 8,uVar7 & 255);
      pageParaSet(&local_f4,acStack_128,4);
      uVar4 = ntohl(local_108);
      uVar5 = ntohl(local_108);
      uVar6 = ntohl(local_108);
      uVar7 = ntohl(local_108);
      sprintf(acStack_128,"%d.%d.%d.%d",uVar4 >> 24,(uVar5 & 0xff0000) >> 16,
              (int)(uVar6 & -256) >> 8,uVar7 & 255);
      pageParaSet(&local_f4,acStack_128,5);
      local_138 = local_38;
      pageParaSet(&local_f4,local_34,6);
      swGetSystemMode(local_130);
      local_44 = 3;
      local_40 = swIsMultiSystemMode();
      local_3c = local_130[0];
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "wzdStaticIPInf");
      do {
        pageDynParaPrintf(local_30,iVar1,param_1);
        iVar1 = iVar1 + 1;
      } while (iVar1 != 10);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      httpWizardPrintStepInfo(param_1);
      HttpWebV4Head(param_1,0,0);
      iVar1 = httpRpmFsA(param_1,"/userRpm/WzdStaticIpRpm.htm");
      if (iVar1 == 2) {
        return 2;
      }
      iVar1 = 10;
      puVar9 = 0;
      goto LAB_00438780;
    }
    pcVar2 = httpGetEnv(param_1,"wanip");
    if (pcVar2 != 0) {
      do {
        pcVar10 = pcVar2;
        pcVar2 = pcVar10 + 1;
      } while (*pcVar10 == ' ');
      if ((pcVar10 != 0) && (*pcVar10 != '\0')) {
        iVar1 = swChkDotIpAddr(pcVar10);
        if (iVar1 == 0) {
          puVar9 = "";
          iVar1 = 0x138b;
        }
        else {
          local_118 = inet_addr(pcVar10);
          pcVar2 = httpGetEnv(param_1,"wanmask");
          if (pcVar2 != 0) {
            do {
              pcVar10 = pcVar2;
              pcVar2 = pcVar10 + 1;
            } while (*pcVar10 == ' ');
            if ((pcVar10 != 0) && (*pcVar10 != '\0')) {
              iVar1 = swChkDotIpAddr(pcVar10);
              if (iVar1 == 0) {
                puVar9 = "";
                iVar1 = 0x138c;
                goto LAB_00438780;
              }
              local_114 = inet_addr(pcVar10);
            }
          }
          pcVar2 = httpGetEnv(param_1,"gateway");
          if (pcVar2 != 0) {
            do {
              pcVar10 = pcVar2;
              pcVar2 = pcVar10 + 1;
            } while (*pcVar10 == ' ');
            if ((pcVar10 != 0) && (*pcVar10 != '\0')) {
              iVar1 = swChkDotIpAddr(pcVar10);
              if (iVar1 == 0) {
                puVar9 = "";
                iVar1 = 0x138f;
                goto LAB_00438780;
              }
              local_110 = inet_addr(pcVar10);
            }
          }
          pcVar2 = httpGetEnv(param_1,"dnsserver");
          if (pcVar2 != 0) {
            do {
              pcVar10 = pcVar2;
              pcVar2 = pcVar10 + 1;
            } while (*pcVar10 == ' ');
            if ((pcVar10 != 0) && (*pcVar10 != '\0')) {
              iVar1 = swChkDotIpAddr(pcVar10);
              if (iVar1 == 0) {
                puVar9 = "";
                iVar1 = 0x138d;
                goto LAB_00438780;
              }
              local_10c = inet_addr(pcVar10);
            }
          }
          pcVar2 = httpGetEnv(param_1,"dnsserver2");
          if (pcVar2 != 0) {
            do {
              pcVar10 = pcVar2;
              pcVar2 = pcVar10 + 1;
            } while (*pcVar10 == ' ');
            if ((pcVar10 != 0) && (*pcVar10 != '\0')) {
              iVar1 = swChkDotIpAddr(pcVar10);
              if (iVar1 == 0) {
                puVar9 = "";
                iVar1 = 0x138e;
                goto LAB_00438780;
              }
              local_108 = inet_addr(pcVar10);
            }
          }
          local_100 = 0x5dc;
          sVar8 = swChkStaticIpCfg(0,&local_118);
          iVar1 = sVar8;
          if (iVar1 == 0) {
            swSetStaticIpCfg(0,&local_118,1);
            goto LAB_004387c4;
          }
          puVar9 = "";
        }
LAB_00438780:
        iVar1 = HttpErrorPage(param_1,iVar1,puVar9,0);
        iVar1 = iVar1 << 16;
        goto LAB_004386d0;
      }
    }
LAB_004387c4:
    iVar1 = wzdStepFindNext(&local_134);
    if (iVar1 == 0) {
      puts("():Next step does not exist!\r");
      return 2;
    }
  }
  else {
    iVar1 = wzdStepFindPrev(&local_134);
    if (iVar1 == 0) {
      return 2;
    }
  }
  iVar1 = GoUrl(param_1,local_134 + 8);
  iVar1 = iVar1 << 16;
LAB_004386d0:
  return iVar1 >> 16;
}

