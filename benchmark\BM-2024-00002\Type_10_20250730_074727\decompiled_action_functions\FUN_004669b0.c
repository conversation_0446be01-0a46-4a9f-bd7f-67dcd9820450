
undefined4 FUN_004669b0(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  int iVar4;
  void *__ptr;
  int local_8e8;
  undefined4 local_8bc;
  undefined4 local_8b8;
  int local_8b4;
  undefined auStack_8b0 [16];
  undefined auStack_8a0 [16];
  char acStack_890 [2144];
  undefined4 local_30;
  undefined4 local_2c;
  undefined4 local_28;
  undefined4 local_24;
  undefined4 local_20;
  undefined4 local_1c;
  undefined4 local_18;
  undefined4 local_14;
  undefined4 local_10;
  undefined4 local_c;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetWifiDownSettings");
  }
  else {
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar2 = mxmlNewElement(iVar2,"soap:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"GetWifiDownSettingsResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("GetWifiDownSettingsResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar3 = mxmlNewElement(iVar2,"GetWifiDownSettingsResult");
            if (iVar3 == 0) {
              mxmlDelete(iVar1);
              puts("GetWifiDownSettingsResult=NULL");
            }
            else {
              mxmlNewText(iVar3,0,"O");
              iVar3 = mxmlNewElement(iVar2,"ControlMode");
              if (iVar3 == 0) {
                mxmlDelete(iVar1);
                puts("ControlMode=NULL");
              }
              else {
                local_8bc = 0;
                local_8b8 = 0;
                iVar4 = FUN_00466670(&local_8bc);
                if (iVar4 != 0) {
                  mxmlNewText(iVar3,0,&local_8bc);
                  local_8b4 = 0;
                  memset(auStack_8b0,0,0x880);
                  iVar3 = apmib_get(0x1cde,&local_8b4);
                  if (iVar3 == 0) {
                    puts("ERROR! apmib get MIB_AUTO_WIFI_TBL_NUM ");
                  }
                  else {
                    if (0 < local_8b4) {
                      iVar3 = FUN_0046675c(local_8b4,auStack_8b0);
                      if (iVar3 == 0) {
                        puts("ERROR! get Wifi ControlRule ");
                        return 0;
                      }
                      for (local_8e8 = 0; local_8e8 < local_8b4; local_8e8 = local_8e8 + 1) {
                        iVar3 = mxmlNewElement(iVar2,"ControlRule");
                        if (iVar3 == 0) {
                          mxmlDelete(iVar1);
                          puts("ControlRule=NULL");
                          return 0;
                        }
                        iVar4 = mxmlNewElement(iVar3,"StartTime");
                        if (iVar4 == 0) {
                          mxmlDelete(iVar1);
                          puts("StartTime=NULL");
                          return 0;
                        }
                        mxmlNewText(iVar4,0,auStack_8b0 + local_8e8 * """);
                        iVar4 = mxmlNewElement(iVar3,"EndTime");
                        if (iVar4 == 0) {
                          mxmlDelete(iVar1);
                          puts("EndTime=NULL");
                          return 0;
                        }
                        mxmlNewText(iVar4,0,auStack_8a0 + local_8e8 * """);
                        iVar4 = mxmlNewElement(iVar3,"Enable");
                        local_30 = 0;
                        local_2c = 0;
                        if (iVar4 == 0) {
                          mxmlDelete(iVar1);
                          puts("Enable=NULL");
                          return 0;
                        }
                        if (acStack_890[local_8e8 * 34] == '\0') {
                          memcpy(&local_30,"false",6);
                        }
                        else {
                          if (acStack_890[local_8e8 * 34] != '\x01') {
                            puts("ERROR! AUTO WIFI ENABLED Unrecognized");
                            return 0;
                          }
                          memcpy(&local_30,"true",5);
                        }
                        mxmlNewText(iVar4,0,&local_30);
                        iVar3 = mxmlNewElement(iVar3,"Week");
                        local_28 = 0;
                        local_24 = 0;
                        local_20 = 0;
                        local_1c = 0;
                        local_18 = 0;
                        local_14 = 0;
                        local_10 = 0;
                        local_c = 0;
                        if (iVar3 == 0) {
                          mxmlDelete(iVar1);
                          puts("Week=NULL");
                          return 0;
                        }
                        FUN_00466870(acStack_890[local_8e8 * 0x00000023],&local_28);
                        mxmlNewText(iVar3,0,&local_28);
                      }
                    }
                    if (("" == 0) &&
                       (__ptr = mxmlSaveAllocString(iVar1,0), __ptr != 0)) {
                      FUN_0041ed70("",200,__ptr,"");
                      free(__ptr);
                    }
                    mxmlDelete(0);
                    mxmlDelete(iVar1);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

