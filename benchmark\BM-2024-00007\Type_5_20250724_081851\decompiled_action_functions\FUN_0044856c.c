
undefined4 FUN_0044856c(undefined4 param_1)

{
  uint uVar1;
  uint uVar2;
  sysinfo local_50;
  
  puts("Start Test...");
  sysinfo(&local_50);
  if (299 < local_50.uptime) {
    printf(0xffffffcf);
    return 2;
  }
  uVar1 = swGetProductId();
  if (uVar1 == 0x8420202) {
LAB_004487c8:
    FUN_00447f54(param_1,0);
  }
  else {
    if (uVar1 < 0x8420203) {
      if (uVar1 != 0x7430002) {
        if (uVar1 < 0x7430003) {
          if (uVar1 != 0x7400004) {
            if (uVar1 < 0x7400005) {
              if (uVar1 != 0x7100002) {
                uVar2 = 0x7201002;
                goto LAB_00448714;
              }
LAB_0044879c:
              FUN_00447cd8(param_1,0);
              goto LAB_00448830;
            }
            if (uVar1 == 0x7400005) goto LAB_004487f8;
            if (uVar1 != 0x7410004) goto LAB_00448830;
          }
          FUN_00448268(param_1,0,local_50.uptime);
          goto LAB_00448830;
        }
        if (uVar1 != 0x8410010) {
          if (uVar1 < 0x8410011) {
            uVar2 = 0x8410009;
            if (uVar1 == 0x8020001) goto LAB_004487e0;
          }
          else {
            uVar2 = 0x8420002;
            if (uVar1 == 0x8410012) goto LAB_004487f8;
          }
          if (uVar1 != uVar2) goto LAB_00448830;
        }
        goto LAB_004487c8;
      }
    }
    else {
      if (uVar1 < 0x10430004) {
        if (uVar1 < 0x10430002) {
          if (uVar1 != 0x9400002) {
            if (uVar1 < 0x9400003) {
              if (uVar1 == 0x8430001) goto LAB_0044879c;
              uVar2 = 0x9010004;
LAB_00448714:
              if (uVar1 != uVar2) goto LAB_00448830;
              goto LAB_004487f8;
            }
            if (1 < uVar1 + 0xf6befffb) goto LAB_00448830;
          }
          FUN_00447bb4(param_1,0,local_50.uptime);
        }
        else {
LAB_004487f8:
          FUN_004483a4(param_1,0,local_50.uptime);
        }
        goto LAB_00448830;
      }
      if (0x30400002 < uVar1) {
        if ((uVar1 == 0x32200002) || (uVar1 == 0x34200002)) {
          FUN_00447dfc(param_1,0);
        }
        goto LAB_00448830;
      }
      if ((uVar1 < 0x30400001) && (uVar1 != 0x30200001)) goto LAB_00448830;
    }
LAB_004487e0:
    FUN_004480ac(param_1,0,local_50.uptime);
  }
LAB_00448830:
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  HttpWebV4Head(param_1,0,1);
  return 2;
}

