
undefined4 FUN_00437e64(void)

{
  char *__src;
  int iVar1;
  int iVar2;
  int iVar3;
  void *__ptr;
  int local_28;
  in_addr local_24;
  undefined4 local_20;
  undefined4 local_1c;
  char local_18 [16];
  
  local_24.s_addr = 0;
  local_20 = 0;
  local_1c = 0;
  local_18[0] = '\0';
  local_18[1] = '\0';
  local_18[2] = '\0';
  local_18[3] = '\0';
  local_18[4] = '\0';
  local_18[5] = '\0';
  local_18[6] = '\0';
  local_18[7] = '\0';
  local_18[8] = '\0';
  local_18[9] = '\0';
  local_18[10] = '\0';
  local_18[11] = '\0';
  local_18[12] = '\0';
  local_18[13] = '\0';
  local_18[14] = '\0';
  local_18[15] = '\0';
  apmib_get(140,&local_28);
  if (local_28 == 0) {
    memcpy(&local_20,"false",6);
  }
  else {
    memcpy(&local_20,"true",5);
  }
  apmib_get(141,&local_24);
  __src = inet_ntoa(local_24);
  if (__src != 0) {
    strncpy(local_18,__src,15);
  }
  iVar1 = mxmlNewXML("1.0");
  if (iVar1 == 0) {
    puts("Create new xml error!!!");
  }
  else {
    iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
    if (iVar2 == 0) {
      puts("Create new element error!!!");
      mxmlDelete(iVar1);
    }
    else {
      mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
      mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
      mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
      iVar2 = mxmlNewElement(iVar2,"soap:Body");
      if (iVar2 == 0) {
        puts("Create new element error!!!");
        mxmlDelete(iVar1);
      }
      else {
        iVar2 = mxmlNewElement(iVar2,"GetDMZSettingsResponse");
        if (iVar2 == 0) {
          puts("Create new element error!!!");
          mxmlDelete(iVar1);
        }
        else {
          mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
          iVar3 = mxmlNewElement(iVar2,"GetDMZSettingsResult");
          if (iVar3 == 0) {
            puts("Create new element error!!!");
            mxmlDelete(iVar1);
          }
          else {
            iVar3 = mxmlNewText(iVar3,0,"O");
            if (iVar3 == 0) {
              puts("Create new text error!!!");
              mxmlDelete(iVar1);
            }
            else {
              iVar3 = mxmlNewElement(iVar2,"Enabled");
              if (iVar3 == 0) {
                puts("Create new element error!!!");
                mxmlDelete(iVar1);
              }
              else {
                iVar3 = mxmlNewText(iVar3,0,&local_20);
                if (iVar3 == 0) {
                  puts("Create new text error!!!");
                  mxmlDelete(iVar1);
                }
                else {
                  iVar2 = mxmlNewElement(iVar2,"IPAddress");
                  if (iVar2 == 0) {
                    puts("Create new element error!!!");
                    mxmlDelete(iVar1);
                  }
                  else {
                    iVar2 = mxmlNewText(iVar2,0,local_18);
                    if (iVar2 == 0) {
                      puts("Create new text error!!!");
                      mxmlDelete(iVar1);
                    }
                    else {
                      __ptr = mxmlSaveAllocString(iVar1,0);
                      if (__ptr != 0) {
                        FUN_0041ed70("",200,__ptr,"");
                        free(__ptr);
                      }
                      mxmlDelete(iVar1);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

