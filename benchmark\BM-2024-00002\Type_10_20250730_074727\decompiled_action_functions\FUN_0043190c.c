
undefined4 FUN_0043190c(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  int iVar3;
  int iVar4;
  int iVar5;
  char *pcVar6;
  size_t sVar7;
  int local_74;
  char acStack_5c [33];
  in_addr iStack_3b;
  undefined auStack_37 [6];
  char local_31;
  char acStack_30 [20];
  undefined4 local_1c;
  undefined4 local_18;
  undefined4 local_14;
  undefined4 local_10;
  
  bVar1 = true;
  local_1c = 0;
  local_18 = 0;
  local_14 = 0;
  local_10 = 0;
  iVar2 = mxmlLoadString(0,param_1,0);
  if (iVar2 == 0) {
    return 0;
  }
  iVar3 = mxmlFindElement(iVar2,iVar2,"soap:Envelope",0,0,1);
  if ((iVar3 != 0) && (iVar3 = mxmlFindElement(iVar3,iVar2,"SetQoSSettings",0,0,1), iVar3 != 0)) {
    local_74 = mxmlFindElement(iVar3,iVar2,"QoSInfoData",0,0,1);
    if (local_74 != 0) {
      apmib_set(0x41c88,acStack_5c);
      do {
        memset(acStack_5c,0,"=");
        iVar4 = mxmlFindElement(local_74,iVar2,"QoSInfo",0,0,1);
        if (iVar4 != 0) {
          iVar5 = mxmlFindElement(iVar4,iVar2,"Hostname",0,0,1);
          if ((iVar5 != 0) && (pcVar6 = mxmlGetText(iVar5,0), pcVar6 != 0)) {
            strncpy(acStack_5c,pcVar6," ");
          }
          iVar5 = mxmlFindElement(iVar4,iVar2,"IPAddress",0,0,1);
          if ((iVar5 != 0) && (pcVar6 = mxmlGetText(iVar5,0), pcVar6 != 0)) {
            inet_aton(pcVar6,&iStack_3b);
          }
          iVar5 = mxmlFindElement(iVar4,iVar2,"MACAddress",0,0,1);
          if (((iVar5 != 0) && (iVar5 = mxmlGetText(iVar5,0), iVar5 != 0)) &&
             (iVar5 = FUN_0042c834(iVar5,auStack_37,6), iVar5 == 0)) {
            bVar1 = false;
            goto LAB_00431ee4;
          }
          iVar5 = mxmlFindElement(iVar4,iVar2,"Priority",0,0,1);
          local_74 = iVar4;
          if ((iVar5 != 0) && (pcVar6 = mxmlGetText(iVar5,0), pcVar6 != 0)) {
            iVar5 = atoi(pcVar6);
            local_31 = iVar5;
            if (local_31 == '\0') goto LAB_00431d88;
          }
          iVar5 = mxmlFindElement(iVar4,iVar2,"Type",0,0,1);
          if ((iVar5 != 0) && (pcVar6 = mxmlGetText(iVar5,0), pcVar6 != 0)) {
            strncpy(acStack_30,pcVar6,16);
          }
          apmib_set(0x21c87,acStack_5c);
          iVar5 = apmib_set(0x11c86,acStack_5c);
          if (iVar5 == 0) {
            puts("Add table entry error!");
            bVar1 = false;
            goto LAB_00431ee4;
          }
        }
LAB_00431d88:
      } while (iVar4 != 0);
    }
    bVar1 = true;
    iVar4 = mxmlFindElement(iVar3,iVar2,"UploadBandwidth",0,0,1);
    if ((iVar4 != 0) && (pcVar6 = mxmlGetText(iVar4,0), pcVar6 != 0)) {
      sVar7 = strlen(pcVar6);
      if ((15 < sVar7) || (iVar4 = FUN_0042e708(pcVar6), iVar4 == -1)) {
        bVar1 = false;
        goto LAB_00431ee4;
      }
      apmib_set(0x1b5c,pcVar6);
    }
    iVar3 = mxmlFindElement(iVar3,iVar2,"DownloadBandwidth",0,0,1);
    if ((iVar3 != 0) && (pcVar6 = mxmlGetText(iVar3,0), pcVar6 != 0)) {
      sVar7 = strlen(pcVar6);
      if ((sVar7 < 16) && (iVar3 = FUN_0042e708(pcVar6), iVar3 != -1)) {
        apmib_set(0x1b5d,pcVar6);
      }
      else {
        bVar1 = false;
      }
    }
  }
LAB_00431ee4:
  mxmlDelete(iVar2);
  if (("" == 0) || (!bVar1)) {
    if (bVar1) {
      memcpy(&local_1c,"O",3);
      apmib_update(4);
      FUN_00421468("killall -9 loop");
      FUN_00421468("loop&");
    }
    else {
      memcpy(&local_1c,"ERROR",6);
    }
    FUN_004260e0("SetQoSSettings",&local_1c);
  }
  return 0;
}

