
/* WARNING: Type propagation algorithm not settling */

int FUN_004262e0(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  short sVar7;
  char *pcVar3;
  size_t sVar4;
  char *pcVar5;
  undefined4 uVar6;
  int iVar8;
  long lVar9;
  uint uStack_110;
  undefined4 uStack_10c;
  char acStack_108 [24];
  undefined uStack_f0;
  byte bStack_ef;
  char acStack_ee [9];
  undefined uStack_e5;
  char acStack_e4 [24];
  undefined uStack_cc;
  int iStack_c8;
  uint uStack_c4;
  uint uStack_c0;
  uint uStack_bc;
  uint uStack_b8;
  uint uStack_b4;
  uint uStack_b0;
  uint auStack_ac [2];
  char acStack_a4 [5];
  char acStack_9f [7];
  long lStack_98;
  char *pcStack_94;
  undefined4 uStack_90;
  int *piStack_8c;
  undefined4 uStack_88;
  uint *puStack_84;
  undefined4 uStack_80;
  uint *puStack_7c;
  undefined4 uStack_78;
  uint *puStack_74;
  undefined4 uStack_70;
  uint *puStack_6c;
  undefined4 uStack_68;
  uint *puStack_64;
  undefined4 uStack_60;
  uint *puStack_5c;
  undefined4 uStack_58;
  uint *puStack_54;
  undefined4 uStack_50;
  uint *puStack_4c;
  undefined4 uStack_48;
  char *pcStack_44;
  undefined4 uStack_40;
  char *pcStack_3c;
  undefined4 uStack_38;
  undefined4 uStack_34;
  byte abStack_20 [12];
  
  piStack_8c = &iStack_c8;
  puStack_84 = &uStack_c4;
  puStack_7c = &uStack_c0;
  puStack_74 = &uStack_bc;
  puStack_6c = &uStack_b8;
  puStack_64 = &uStack_b4;
  puStack_5c = &uStack_b0;
  puStack_54 = auStack_ac;
  puStack_4c = auStack_ac + 1;
  pcStack_44 = acStack_a4;
  pcStack_3c = acStack_9f;
  uStack_38 = 5;
  uStack_40 = 5;
  uStack_90 = 25;
  uStack_34 = 0;
  uStack_88 = 0;
  uStack_80 = 0;
  uStack_78 = 0;
  uStack_70 = 0;
  uStack_68 = 0;
  uStack_60 = 0;
  uStack_58 = 0;
  uStack_50 = 0;
  uStack_48 = 0;
  pcStack_94 = acStack_e4;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    sVar7 = HttpDenyPage(param_1);
    goto LAB_00426c10;
  }
  memset(&uStack_10c,0,"(");
  memset(&uStack_10c,0,"(");
  memset(acStack_e4,0,"P");
  if ("" == 0) {
    memset(l_schedEntry,0,"(");
  }
  pcVar3 = httpGetEnv(param_1,"ClientId");
  lVar9 = 0;
  if (pcVar3 != 0) {
    lVar9 = atol(pcVar3);
  }
  iVar2 = httpGetEnv(param_1,"Return");
  if (iVar2 == 0) {
    iVar2 = httpGetEnv(param_1,"Next");
    if (iVar2 == 0) {
      iVar2 = swChkEntryName(2,0x5c5384);
      if (iVar2 == 0) {
        "" = 0;
      }
      iVar2 = "";
      if ("" == 1) {
        uStack_cc = 0;
        strncpy(acStack_e4,l_schedEntry + 4,24);
        if (l_schedEntry[29] < '\0') {
          iStack_c8 = iVar2;
        }
        else {
          iStack_c8 = 0;
          swUint8ToWeekDays(abStack_20);
          uStack_c4 = abStack_20[0] & 1;
          uStack_c0 = ((abStack_20[0] & 2) << 24) >> 25;
          uStack_bc = ((abStack_20[0] & 4) << 24) >> 26;
          uStack_b8 = ((abStack_20[0] & 8) << 24) >> 27;
          uStack_b4 = ((abStack_20[0] & 16) << 24) >> 28;
          uStack_b0 = ((abStack_20[0] & " ") << 24) >> 29;
          auStack_ac[0] = ((abStack_20[0] & "@") << 24) >> 30;
        }
        iVar2 = strcmp(l_schedEntry + 30,"0000");
        if ((iVar2 == 0) && (iVar2 = strcmp(l_schedEntry + "#","2400"), iVar2 == 0)) {
          auStack_ac[1] = 1;
        }
        else {
          auStack_ac[1] = 0;
          strcpy(acStack_a4,l_schedEntry + 30);
          strcpy(acStack_9f,l_schedEntry + "#");
        }
      }
      lStack_98 = lVar9;
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "wzdSchedInf");
      iVar2 = 0;
      do {
        iVar8 = iVar2 + 1;
        pageDynParaPrintf(&pcStack_94,iVar2,param_1);
        iVar2 = iVar8;
      } while (iVar8 != 13);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      HttpWebV4Head(param_1,0,1);
      iVar2 = httpRpmFsA(param_1,"/userRpm/WzdAccessCtrlSchedAddRpm.htm");
      if (iVar2 == 2) {
        return 2;
      }
      iVar2 = 10;
    }
    else {
      memset(&uStack_10c,0,"(");
      puts("timesched: Here is save!");
      uStack_10c = 1;
      pcVar3 = httpGetEnv(param_1,"time_sched_name");
      if (pcVar3 != 0) {
        uStack_f0 = 0;
        strncpy(acStack_108,pcVar3,24);
        printf("timesched save: name - %s\n",acStack_108);
      }
      iVar2 = getEnvToInt(param_1,"day_type",0,1);
      bStack_ef = 128;
      if (iVar2 == 0) {
        uStack_110 = 0;
        iVar2 = httpGetEnv(param_1,"Mon_select");
        if (iVar2 != 0) {
          uStack_110 = 0x1000000;
        }
        iVar2 = httpGetEnv(param_1,"Tue_select");
        if (iVar2 != 0) {
          uStack_110 = uStack_110 | 0x2000000;
        }
        iVar2 = httpGetEnv(param_1,"Wed_select");
        if (iVar2 != 0) {
          uStack_110 = uStack_110 | 0x4000000;
        }
        iVar2 = httpGetEnv(param_1,"Thu_select");
        if (iVar2 != 0) {
          uStack_110 = uStack_110 | 0x8000000;
        }
        iVar2 = httpGetEnv(param_1,"Fri_select");
        if (iVar2 != 0) {
          uStack_110 = uStack_110 | 0x10000000;
        }
        iVar2 = httpGetEnv(param_1,"Sat_select");
        if (iVar2 != 0) {
          uStack_110 = uStack_110 | 0x20000000;
        }
        iVar2 = httpGetEnv(param_1,"Sun_select");
        if (iVar2 != 0) {
          uStack_110 = uStack_110 | 0x40000000;
        }
        bStack_ef = swWeekDaysToUint8(uStack_110);
      }
      printf("timesched save: nDay - %x\n",bStack_ef);
      iVar2 = httpGetEnv(param_1,"all_hours");
      if (iVar2 == 0) {
        pcVar3 = httpGetEnv(param_1,"time_sched_start_time");
        if (pcVar3 != 0) {
          acStack_ee[4] = 0;
          strncpy(acStack_ee,pcVar3,4);
          iVar2 = 0;
          sVar4 = strlen(acStack_ee);
          pcVar5 = pcVar3;
          if (sVar4 == 4) {
            do {
              iVar2 = iVar2 + 1;
              if (9 < (byte)(*pcVar5 - 0x30U)) goto LAB_00426840;
              pcVar5 = pcVar3 + iVar2;
            } while (iVar2 != 4);
            iVar2 = swChkLegalFirewallTime(acStack_ee);
            if (iVar2 != 0) {
              pcVar3 = httpGetEnv(param_1,"time_sched_end_time");
              if (pcVar3 != 0) {
                uStack_e5 = 0;
                strncpy(acStack_ee + 5,pcVar3,4);
                sVar4 = strlen(acStack_ee + 5);
                iVar2 = 0;
                pcVar5 = pcVar3;
                if (sVar4 == 4) {
                  do {
                    iVar2 = iVar2 + 1;
                    if (9 < (byte)(*pcVar5 - 0x30U)) goto LAB_004268f8;
                    pcVar5 = pcVar3 + iVar2;
                  } while (iVar2 != 4);
                  iVar2 = swChkLegalFirewallTime(acStack_ee + 5);
                  if (iVar2 != 0) goto LAB_00426908;
                }
              }
LAB_004268f8:
              iVar2 = 0x1f41;
              goto LAB_00426c04;
            }
          }
        }
LAB_00426840:
        iVar2 = 8000;
      }
      else {
        builtin_strncpy(acStack_ee,"0000",5);
        uStack_e5 = 0;
        memcpy(acStack_ee + 5,"2400",4);
LAB_00426908:
        printf("timesched save: time - %s:%s\n",acStack_ee,acStack_ee + 5);
        bVar1 = "" != 0;
        if (bVar1) {
          iVar2 = swGetFilterEntryNumCfg(2);
          iVar2 = iVar2 + -1;
        }
        else {
          iVar2 = 0;
        }
        uVar6 = swSetScheduleEntry(&uStack_10c,iVar2,bVar1,1);
        iVar2 = swFilterFindErrorNum(uVar6);
        if (iVar2 == 0) {
          memcpy(l_schedEntry,&uStack_10c,"(");
          "" = 1;
          pcVar3 = "../userRpm/WzdAccessCtrlRuleAddRpm.htm";
          goto LAB_004269ec;
        }
      }
    }
LAB_00426c04:
    sVar7 = HttpErrorPage(param_1,iVar2,0,0);
  }
  else {
    pcVar3 = "../userRpm/WzdAccessCtrlTargetAddRpm.htm";
LAB_004269ec:
    sVar7 = GoUrl(param_1,pcVar3);
  }
LAB_00426c10:
  return sVar7;
}

