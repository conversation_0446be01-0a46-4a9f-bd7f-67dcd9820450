
code * FUN_0044f244(int param_1)

{
  code *pcVar1;
  int iVar2;
  int iVar3;
  int iVar4;
  FILE *__stream;
  long lVar5;
  void *__ptr;
  char local_80 [12];
  char local_74 [12];
  sysinfo local_68;
  undefined4 local_28;
  undefined4 local_24;
  undefined4 local_20;
  undefined4 local_1c;
  undefined4 local_18;
  int local_14 [2];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetInternetConnUpTime");
    pcVar1 = 0;
  }
  else {
    local_80[0] = '\0';
    local_80[1] = '\0';
    local_80[2] = '\0';
    local_80[3] = '\0';
    local_80[4] = '\0';
    local_80[5] = '\0';
    local_80[6] = '\0';
    local_80[7] = '\0';
    local_80[8] = '\0';
    local_80[9] = '\0';
    local_74[0] = '\0';
    local_74[1] = '\0';
    local_74[2] = '\0';
    local_74[3] = '\0';
    local_74[4] = '\0';
    local_74[5] = '\0';
    local_74[6] = '\0';
    local_74[7] = '\0';
    local_74[8] = '\0';
    local_74[9] = '\0';
    local_28 = 0;
    local_24 = 0;
    local_20 = 0;
    local_1c = 0;
    local_18 = 0;
    local_14[0] = 0;
    iVar2 = mxmlNewXML("1.0");
    if (iVar2 == 0) {
      puts("xml=NULL");
      pcVar1 = 0;
    }
    else {
      iVar3 = mxmlNewElement(iVar2,"soap:Envelope");
      if (iVar3 == 0) {
        mxmlDelete(iVar2);
        puts("soap_env=NULL");
        pcVar1 = 0;
      }
      else {
        mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar3 = mxmlNewElement(iVar3,"soap:Body");
        if (iVar3 == 0) {
          mxmlDelete(iVar2);
          puts("body=NULL");
          pcVar1 = 0;
        }
        else {
          iVar3 = mxmlNewElement(iVar3,"GetInternetConnUpTimeResponse");
          if (iVar3 == 0) {
            mxmlDelete(iVar2);
            puts("GetInternetConnUpTimeResponse_xml=NULL");
            pcVar1 = 0;
          }
          else {
            mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
            iVar4 = mxmlNewElement(iVar3,"GetInternetConnUpTimeResult");
            if (iVar4 == 0) {
              mxmlDelete(iVar2);
              puts("GetInternetConnUpTimeResult_xml=NULL");
              pcVar1 = 0;
            }
            else {
              mxmlNewText(iVar4,0,FUN_004ad49c);
              iVar3 = mxmlNewElement(iVar3,"UpTime");
              if (iVar3 == 0) {
                mxmlDelete(iVar2);
                puts("UpTime_xml=NULL");
                pcVar1 = 0;
              }
              else {
                apmib_get("h",local_14);
                if (local_14[0] == 3) {
                  FUN_00426a2c("ppp0",&local_28);
                }
                else {
                  FUN_00426a2c("eth1",&local_28);
                }
                iVar4 = FUN_00426c54();
                if ((iVar4 == 0) && (local_28 != '\0')) {
                  __stream = fopen("/tmp/int_uptime","r+");
                  if (__stream == 0) {
                    "" = "";
                  }
                  else {
                    fread(local_74,1,9,__stream);
                    sysinfo(&local_68);
                    lVar5 = atol(local_74);
                    iVar4 = (local_68.uptime - lVar5) - "";
                    "" = local_68.uptime - lVar5;
                    if (iVar4 < 0) {
                      "" = 0;
                    }
                    "" = "";
                    fclose(__stream);
                  }
                  snprintf(local_80,9,"%ld","");
                }
                else {
                  "" = 0;
                  "" = 0;
                  snprintf(local_80,9,"%ld",0);
                }
                mxmlNewText(iVar3,0,local_80);
                __ptr = mxmlSaveAllocString(iVar2,0);
                if (__ptr == 0) {
                  puts("retstring=NULL");
                }
                else {
                  FUN_0041ed70("",200,__ptr,"");
                  free(__ptr);
                }
                mxmlDelete(iVar2);
                pcVar1 = FUN_004ad49c;
              }
            }
          }
        }
      }
    }
  }
  return pcVar1;
}

