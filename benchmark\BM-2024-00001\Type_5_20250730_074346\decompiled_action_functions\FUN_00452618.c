
int FUN_00452618(undefined4 param_1)

{
  int iVar1;
  short sVar10;
  uint uVar2;
  int iVar3;
  int iVar4;
  undefined4 uVar5;
  char *pcVar6;
  char *pcVar7;
  size_t sVar8;
  uint uVar9;
  char *__s;
  uint uVar11;
  undefined4 uVar12;
  uint uVar13;
  uint local_290;
  char acStack_28c [8];
  int local_284;
  undefined4 local_280;
  undefined4 local_27c;
  undefined4 local_278;
  undefined4 local_274;
  undefined4 local_270;
  undefined4 local_26c;
  undefined4 local_268;
  undefined2 local_264;
  undefined auStack_260 [4];
  undefined auStack_25c [4];
  undefined auStack_258 [4];
  undefined auStack_254 [4];
  undefined auStack_250 [4];
  int *local_24c;
  undefined4 local_248;
  undefined4 *local_244;
  undefined4 local_240;
  char *local_23c;
  undefined4 local_238;
  undefined *local_234;
  undefined4 local_230;
  undefined4 local_22c;
  int local_224;
  undefined4 local_220;
  char local_21c [18];
  undefined auStack_20a [18];
  undefined *local_1f8;
  undefined4 local_1f4;
  undefined *local_1f0;
  undefined4 local_1ec;
  undefined *local_1e8;
  undefined4 local_1e4;
  undefined *local_1e0;
  undefined4 local_1dc;
  undefined *local_1d8;
  undefined4 local_1d4;
  undefined4 local_1d0;
  char *local_1c8;
  undefined4 local_1c4;
  undefined *local_1c0;
  undefined4 local_1bc;
  uint *local_1b8;
  undefined4 local_1b4;
  undefined *local_1b0;
  undefined4 local_1ac;
  undefined4 *local_1a8;
  undefined4 local_1a4;
  undefined4 local_1a0;
  char local_198 [18];
  undefined auStack_186 [18];
  uint local_174;
  undefined auStack_170 [28];
  undefined4 local_154;
  undefined4 local_150;
  undefined4 local_14c;
  undefined auStack_148 [6];
  char acStack_142 [16];
  undefined local_132;
  char acStack_131 [248];
  byte local_39;
  undefined *local_38;
  undefined *local_34;
  char *local_30;
  undefined *local_2c;
  
  local_290 = 0;
  swGetParentCtrlTableSize();
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar10 = HttpDenyPage(param_1);
  }
  else {
    iVar1 = httpGetEnv(param_1,"Add");
    if ((iVar1 != 0) || (iVar1 = httpGetEnv(param_1,"Modify"), iVar1 != 0)) {
      iVar1 = FUN_00451ffc(param_1);
      return iVar1;
    }
    memset(&local_150,0,280);
    local_24c = &local_224;
    local_244 = &local_220;
    local_23c = local_21c;
    local_234 = auStack_20a;
    local_1f8 = auStack_260;
    local_1f0 = auStack_25c;
    local_1e8 = auStack_258;
    local_1e0 = auStack_254;
    local_1d8 = auStack_250;
    local_1c8 = local_198;
    local_1c0 = auStack_186;
    local_1b8 = &local_174;
    local_1b0 = auStack_170;
    local_1a8 = &local_154;
    local_1bc = 17;
    local_1c4 = 18;
    local_238 = 18;
    local_230 = 18;
    local_1ac = 25;
    local_22c = 0;
    local_248 = 0;
    local_240 = 0;
    local_1d0 = 0;
    local_1f4 = 0;
    local_1ec = 0;
    local_1e4 = 0;
    local_1dc = 0;
    local_1d4 = 0;
    local_1a0 = 0;
    local_1b4 = 0;
    local_1a4 = 0;
    uVar2 = getEnvToInt(param_1,"Page",1,0x7fffffff);
    uVar11 = 1;
    if (uVar2 != 0xffffff80) {
      uVar11 = uVar2;
    }
    local_284 = 0;
    local_280 = 0;
    local_27c = 0;
    local_278 = 0;
    swGetParentCtrlGlobalCfg(&local_284);
    iVar1 = getEnvToInt(param_1,"ctrl_enable",0,1);
    uVar12 = 0;
    if (iVar1 != -128) {
      local_284 = iVar1;
      if (iVar1 == 1) {
        uVar12 = httpGetEnv(param_1,"parent_mac_addr");
        swMacStr2Eth(uVar12,&local_27c);
      }
      uVar12 = swSetParentCtrlGlobalCfg(&local_284);
    }
    iVar1 = getEnvToInt(param_1,"enableId",0,7);
    if (iVar1 != -128) {
      iVar3 = getEnvToInt(param_1,"enable",0,1);
      iVar4 = swGetParentCtrlTableSize();
      iVar4 = getEnvToInt(param_1,"Page",1,iVar4 + 7U >> 3);
      if (iVar4 != -128) {
        iVar1 = iVar1 + (iVar4 + -1) * 8;
        swEnableParentCtrlEntry(iVar1,iVar3 != 0);
      }
    }
    uVar5 = swGetParentCtrlTableSize();
    getEnvToInt(param_1,"EntryIndex",0,uVar5);
    pcVar6 = httpGetEnv(param_1,"doAll");
    if (pcVar6 == 0) {
      pcVar6 = httpGetEnv(param_1,"Del");
      if (pcVar6 == 0) {
        iVar3 = httpGetEnv(param_1,"Save");
        if (iVar3 != 0) {
          memset(&local_150,0,280);
          puts("parentctrl rule: Here is save!");
          local_150 = 1;
          iVar3 = httpGetEnv(param_1,"child_mac");
          if (iVar3 != 0) {
            swMacStr2Eth(iVar3,auStack_148);
            printf("parentctrl save: mac %s\n",iVar3);
          }
          pcVar6 = httpGetEnv(param_1,"url_comment");
          if (pcVar6 != 0) {
            local_132 = 0;
            strncpy(acStack_142,pcVar6,16);
          }
          pcVar6 = acStack_131;
          iVar3 = 0;
          do {
            sprintf(acStack_28c,"url_%d",iVar3);
            pcVar7 = httpGetEnv(param_1,acStack_28c);
            if (pcVar7 != 0) {
              do {
                __s = pcVar7;
                pcVar7 = __s + 1;
              } while (*__s == ' ');
              if (__s != 0) {
                sVar8 = strlen(__s);
                if (30 < sVar8) {
                  iVar1 = 9000;
                  goto LAB_0045327c;
                }
                iVar4 = swChkLegalDomain(__s);
                if (iVar4 == 0) {
                  iVar1 = 0x2329;
                  goto LAB_0045327c;
                }
                pcVar6[30] = '\0';
                strncpy(pcVar6,__s,30);
              }
            }
            iVar3 = iVar3 + 1;
            pcVar6 = pcVar6 + 31;
          } while (iVar3 != 8);
          local_39 = getEnvToInt(param_1,"scheds_lists",0,255);
          local_14c = getEnvToInt(param_1,"enable",0,1);
          pcVar6 = httpGetEnv(param_1,"Changed");
          iVar3 = strcmp(pcVar6,"1");
          if (iVar3 == 0) {
            pcVar6 = httpGetEnv(param_1,"SelIndex");
            if (pcVar6 != 0) {
              iVar1 = atoi(pcVar6);
            }
            puts("save!!");
            uVar12 = 1;
          }
          else {
            iVar1 = 0;
            uVar12 = 0;
          }
          uVar12 = swSetParentCtrlEntry(&local_150,iVar1,uVar12,1);
        }
      }
      else {
        iVar1 = atoi(pcVar6);
        uVar12 = swDelFilterEntry(4,(uVar11 - 1) * 8 + iVar1,1);
      }
    }
    else {
      iVar1 = strcmp(pcVar6,"DelAll");
      if (iVar1 == 0) {
        swDelAllFilterEntry(4);
      }
      iVar1 = strcmp(pcVar6,"EnAll");
      if (iVar1 == 0) {
        swParentCtrlCfgAll(1);
      }
      iVar1 = strcmp(pcVar6,"DisAll");
      if (iVar1 == 0) {
        swParentCtrlCfgAll(0);
      }
    }
    iVar1 = swFilterFindErrorNum(uVar12);
    if (iVar1 == 0) {
      local_274 = 0;
      local_270 = 0;
      local_26c = 0;
      local_268 = 0;
      local_264 = 0;
      swGetParentCtrlGlobalCfg(&local_284);
      HttpClientMacGet(param_1,&local_274);
      local_224 = local_284;
      local_220 = local_280;
      swMac2Str(&local_27c,local_21c,0);
      memcpy(auStack_20a,&local_274,18);
      iVar1 = strcmp(local_21c,"00-00-00-00-00-00");
      if (iVar1 == 0) {
        local_21c[0] = '\0';
        local_21c[1] = '\0';
        local_21c[2] = '\0';
        local_21c[3] = '\0';
      }
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "parent_ctrl_global_cfg_dyn_array");
      pageDynParaPrintf(&local_24c,0,param_1);
      pageDynParaPrintf(&local_24c,1,param_1);
      pageDynParaPrintf(&local_24c,2,param_1);
      pageDynParaPrintf(&local_24c,3,param_1);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      memset(&local_150,0,280);
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "parent_ctrl_data_param");
      iVar1 = swGetParentCtrlEntry((uVar11 - 1) * 8,&local_150);
      if ((iVar1 != -1) || (iVar1 = uVar11 - 2, uVar2 = uVar11 - 1, uVar11 < 2)) {
        iVar1 = uVar11 - 1;
        uVar2 = uVar11;
      }
      local_34 = auStack_186;
      local_38 = auStack_148;
      local_30 = acStack_142;
      local_2c = auStack_170;
      uVar11 = 0;
      uVar13 = 0;
      while (uVar9 = swGetParentCtrlTableSize(), uVar11 < uVar9) {
        memset(&local_150,0,280);
        iVar3 = swGetParentCtrlEntry(uVar11,&local_150);
        if (iVar3 == -1) break;
        uVar11 = uVar11 + 1;
        if (iVar1 << 3 < uVar11) {
          if ((int)(uVar2 << 3) < uVar11) break;
          uVar13 = uVar13 + 1;
          swMac2Str(local_38,local_198,0);
          iVar3 = strcmp(local_198,"00-00-00-00-00-00");
          if (iVar3 == 0) {
            local_198[0] = '\0';
            local_198[1] = '\0';
            local_198[2] = '\0';
            local_198[3] = '\0';
          }
          memcpy(local_34,local_30,17);
          local_174 = local_39;
          swGetRuleNamesBySeq(local_2c,2,local_174);
          local_154 = local_14c;
          pageDynParaListPrintf(&local_1c8,param_1);
        }
      }
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      local_290 = uVar2;
      pageParaSet(&local_1f8,&local_290,0);
      local_290 = (uint)((int)(uVar2 << 3) < uVar11);
      pageParaSet(&local_1f8,&local_290,1);
      local_290 = uVar13;
      pageParaSet(&local_1f8,&local_290,2);
      local_290 = 5;
      pageParaSet(&local_1f8,&local_290,3);
      local_290 = swGetFilterEntryNumCfg(4);
      pageParaSet(&local_1f8,&local_290,4);
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "parent_ctrl_page_param");
      pageDynParaListPrintf(&local_1f8,param_1);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      HttpWebV4Head(param_1,0,1);
      iVar1 = httpRpmFsA(param_1,"/userRpm/ParentCtrlRpm.htm");
      if (iVar1 == 2) {
        return 2;
      }
      iVar1 = 10;
    }
LAB_0045327c:
    sVar10 = HttpErrorPage(param_1,iVar1,0,0);
  }
  return sVar10;
}

