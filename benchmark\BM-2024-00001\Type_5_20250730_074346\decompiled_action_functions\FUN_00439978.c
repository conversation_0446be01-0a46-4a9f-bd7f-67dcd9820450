
int FUN_00439978(undefined4 param_1)

{
  int iVar1;
  code *pcVar2;
  short local_10 [4];
  
  local_10[0] = 0;
  iVar1 = httpGetEnv(param_1,"detected");
  if (iVar1 == 0) {
    iVar1 = getProductId();
    if (iVar1 == 0x8100002) {
      pcVar2 = wanIsConnected;
    }
    else {
      pcVar2 = swIsWanConnected;
    }
    iVar1 = (*pcVar2)(0);
    if (iVar1 == 0) {
      iVar1 = FUN_00438c28(param_1);
      return iVar1;
    }
    swDetectWanAutoType(0);
    httpStatusSet(param_1,0);
    httpHeaderGenerate(param_1);
    iVar1 = HttpAccessPermit(param_1);
    if (iVar1 == 0) {
      iVar1 = HttpDenyPage(param_1);
      iVar1 = iVar1 << 16;
    }
    else {
      HttpWebV4Head(param_1,0,0);
      iVar1 = httpRpmFsA(param_1,"/userRpm/WzdWanAutoTypeRpm.htm");
      if (iVar1 == 2) {
        return 2;
      }
      iVar1 = HttpErrorPage(param_1,10,0,0);
      iVar1 = iVar1 << 16;
    }
  }
  else {
    iVar1 = swGetDetectedWanAutoType(0);
    if (iVar1 != 1) {
      local_10[0] = (ushort)(byte)(iVar1 + "0") << 8;
      httpSetEnv(param_1,"wan",local_10);
      httpSetEnv(param_1,"Next","1");
      iVar1 = FUN_00438d7c(param_1);
      return iVar1;
    }
    iVar1 = GoUrl(param_1,"../userRpm/WzdWanTroubleShootingRpm.htm");
    iVar1 = iVar1 << 16;
  }
  return iVar1 >> 16;
}

