
undefined4 FUN_0045b638(int param_1)

{
  undefined4 uVar1;
  int iVar2;
  int iVar3;
  char *pcVar4;
  int iVar5;
  void *__ptr;
  in_addr iStack_7c;
  in_addr iStack_78;
  undefined4 local_74;
  undefined auStack_70 [104];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","SetRouterLanSettings");
    uVar1 = 0;
  }
  else {
    local_74 = 0;
    memset(auStack_70,0,100);
    iVar2 = mxmlLoadString(0,param_1,0);
    if (iVar2 != 0) {
      iVar3 = mxmlFindElement(iVar2,iVar2,"soap:Envelope",0,0,1);
      if (iVar3 == 0) {
        puts("state==NULL");
      }
      else {
        iVar3 = mxmlFindElement(iVar3,iVar2,"SetRouterLanSettings",0,0,1);
        if (iVar3 == 0) {
          puts("node==NULL");
        }
        else {
          iVar5 = mxmlFindElement(iVar3,iVar2,"RouterIPAddress",0,0,1);
          if (iVar5 == 0) {
            puts("state1==NULL");
          }
          else {
            pcVar4 = mxmlGetText(iVar5,0);
            if (pcVar4 == 0) {
              puts("IPAddress==NULL");
            }
            else {
              inet_aton(pcVar4,&iStack_7c);
              iVar5 = apmib_set(170,&iStack_7c);
              if (iVar5 != 0) {
                iVar5 = mxmlFindElement(iVar3,iVar2,"RouterSubnetMask",0,0,1);
                if (iVar5 == 0) {
                  puts("state1==NULL");
                }
                else {
                  pcVar4 = mxmlGetText(iVar5,0);
                  if (pcVar4 == 0) {
                    puts("RouterSubnetMask==NULL");
                  }
                  else {
                    inet_aton(pcVar4,&iStack_78);
                    iVar5 = apmib_set(171,&iStack_78);
                    if (iVar5 != 0) {
                      iVar3 = mxmlFindElement(iVar3,iVar2,"DHCPServerEnabled",0,0,1);
                      if (iVar3 == 0) {
                        puts("state1==NULL");
                      }
                      else {
                        pcVar4 = mxmlGetText(iVar3,0);
                        if (pcVar4 == 0) {
                          puts("DHCPServerEnabled==NULL");
                        }
                        else {
                          strncmp(pcVar4,"true",4);
                          apmib_get(173,&local_74);
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    iVar3 = mxmlNewXML("1.0");
    if (iVar3 == 0) {
      puts("xml=NULL");
      uVar1 = 0;
    }
    else {
      iVar5 = mxmlNewElement(iVar3,"soap:Envelope");
      if (iVar5 == 0) {
        puts("soap_env=NULL");
        uVar1 = 0;
      }
      else {
        mxmlElementSetAttr(iVar5,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar5,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar5,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar5 = mxmlNewElement(iVar5,"soap:Body");
        if (iVar5 == 0) {
          puts("body=NULL");
          uVar1 = 0;
        }
        else {
          iVar5 = mxmlNewElement(iVar5,"SetRouterLanSettingsResponse");
          if (iVar5 == 0) {
            puts("SetRouterLanSettingsResponse_xml=NULL");
            uVar1 = 0;
          }
          else {
            mxmlElementSetAttr(iVar5,"xmlns","http://purenetworks.com/HNAP1/");
            iVar5 = mxmlNewElement(iVar5,"SetRouterLanSettingsResult");
            if (iVar5 == 0) {
              puts("SetRouterLanSettingsResult_xml=NULL");
              uVar1 = 0;
            }
            else {
              mxmlNewText(iVar5,0,"O");
              if ("" == 0) {
                apmib_update(4);
                __ptr = mxmlSaveAllocString(iVar3,0);
                if (__ptr == 0) {
                  puts("retstring=NULL");
                }
                else {
                  FUN_0041ed70("",200,__ptr,"");
                  free(__ptr);
                }
                system("killall udhcpd");
                system("sysconf init gw all");
                mxmlDelete(iVar3);
                uVar1 = mxmlDelete(iVar2);
              }
              else {
                mxmlDelete(iVar3);
                uVar1 = mxmlDelete(iVar2);
              }
            }
          }
        }
      }
    }
  }
  return uVar1;
}

