
undefined4 FUN_0043fd34(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  void *__ptr;
  char local_14 [12];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","PollingFirmwareDownload");
  }
  else {
    local_14[0] = '\0';
    local_14[1] = '\0';
    local_14[2] = '\0';
    local_14[3] = '\0';
    local_14[4] = '\0';
    local_14[5] = '\0';
    local_14[6] = '\0';
    local_14[7] = '\0';
    iVar1 = FUN_0042f184();
    if (-1 < iVar1) {
      snprintf(local_14,4,"%d",iVar1);
    }
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar2 = mxmlNewElement(iVar2,"soap:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"PollingFirmwareDownloadResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("PollingFirmwareDownloadResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar3 = mxmlNewElement(iVar2,"PollingFirmwareDownloadResult");
            if (iVar3 == 0) {
              mxmlDelete(iVar1);
              puts("PollingFirmwareDownloadResult=NULL");
            }
            else {
              mxmlNewText(iVar3,0,"O");
              iVar2 = mxmlNewElement(iVar2,"DownloadPercentage");
              if (iVar2 == 0) {
                mxmlDelete(iVar1);
                puts("DownloadPercentage=NULL");
              }
              else {
                mxmlNewText(iVar2,0,local_14);
                if ("" == 0) {
                  __ptr = mxmlSaveAllocString(iVar1,0);
                  if (__ptr != 0) {
                    FUN_0041ed70("",200,__ptr,"");
                    free(__ptr);
                  }
                }
                mxmlDelete(iVar1);
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

