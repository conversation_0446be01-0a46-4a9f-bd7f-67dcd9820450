
int FUN_00425c5c(undefined4 param_1)

{
  int iVar1;
  char *pcVar2;
  in_addr_t iVar3;
  undefined4 uVar4;
  undefined4 *puVar5;
  short sVar6;
  int iVar7;
  undefined4 *puVar8;
  uint uVar9;
  uint local_f8;
  int local_f4;
  uint local_f0;
  undefined4 local_ec;
  undefined2 local_e8;
  undefined4 local_e4;
  undefined4 local_e0;
  undefined4 local_dc;
  undefined auStack_d8 [16];
  in_addr_t local_c8;
  undefined4 local_c4;
  undefined2 local_c0;
  char acStack_b8 [20];
  undefined auStack_a4 [4];
  int local_a0;
  in_addr_t local_9c;
  undefined4 local_98;
  undefined2 local_94;
  undefined4 *local_90;
  undefined4 local_8c;
  undefined4 *local_88;
  undefined4 local_84;
  undefined4 *local_80;
  undefined4 local_7c;
  undefined4 local_78;
  undefined *local_70;
  undefined4 local_6c;
  undefined *local_68;
  undefined4 local_64;
  undefined *local_60;
  undefined4 local_5c;
  undefined4 local_58;
  undefined auStack_50 [4];
  undefined auStack_4c [18];
  undefined auStack_3a [18];
  
  local_f4 = 0;
  local_e4 = 0;
  local_e0 = 0;
  local_dc = 0;
  memset(auStack_50,0,"(");
  local_88 = &local_e0;
  local_80 = &local_dc;
  local_68 = auStack_4c;
  local_60 = auStack_3a;
  local_64 = 18;
  local_5c = 16;
  local_78 = 0;
  local_8c = 0;
  local_84 = 0;
  local_7c = 0;
  local_58 = 0;
  local_6c = 0;
  local_90 = &local_e4;
  local_70 = auStack_50;
  iVar1 = httpGetEnv(param_1,"Capture");
  if ((iVar1 != 0) && (iVar1 = swIsArpTopParaLocked(), iVar1 != 0)) {
    swLockCurrentArpTable();
  }
  iVar1 = httpGetEnv(param_1,"reserve");
  if (iVar1 != 0) {
    pcVar2 = httpGetEnv(param_1,"i");
    iVar3 = inet_addr(pcVar2);
    uVar4 = httpGetEnv(param_1,"mac");
    iVar1 = swMacStr2Eth(uVar4,&local_ec);
    iVar7 = 3000;
    if (iVar1 == -1) goto LAB_0042627c;
    iVar1 = getEnvToInt(param_1,"lock",0,1);
    if (iVar1 != -128) {
      local_f4 = iVar1;
    }
    local_98 = local_ec;
    local_94 = local_e8;
    local_a0 = local_f4;
    local_9c = iVar3;
    uVar4 = swSetArpFixmapEntryCfg(0,auStack_a4,0,1);
    iVar7 = swArpFindErrorNum(uVar4);
    if (iVar7 != 0) goto LAB_0042627c;
  }
  iVar1 = httpGetEnv(param_1,"delete");
  if (iVar1 != 0) {
    pcVar2 = httpGetEnv(param_1,"i");
    iVar3 = inet_addr(pcVar2);
    uVar4 = httpGetEnv(param_1,"mac");
    iVar1 = swMacStr2Eth(uVar4,&local_ec);
    if (iVar1 == -1) {
      iVar7 = 3000;
      goto LAB_0042627c;
    }
    iVar1 = getEnvToInt(param_1,"lock",0,1);
    if (iVar1 != -128) {
      local_f4 = iVar1;
    }
    local_c4 = local_ec;
    local_c0 = local_e8;
    local_c8 = iVar3;
    swDelArpRunTimeEntryCfg(&local_c8);
  }
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  puVar5 = swGetSystemArpEntries(&local_f0,1);
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "arpClientListDyn");
  puVar8 = puVar5;
  for (uVar9 = 0; uVar9 < local_f0; uVar9 = uVar9 + 1) {
    if (puVar8[3] == 6) {
      local_f4 = 1;
    }
    else if (puVar8[3] == 2) {
      local_f4 = 0;
    }
    pageParaSet(&local_70,&local_f4,0);
    sprintf(acStack_b8,"%02X-%02X-%02X-%02X-%02X-%02X",(uint)*(byte *)(puVar8 + 1),
            (uint)*(byte *)(puVar8 + 5),(uint)*(byte *)(puVar8 + 6),
            (uint)*(byte *)(puVar8 + 7),(uint)*(byte *)(puVar8 + 2),
            (uint)*(byte *)(puVar8 + 9));
    pageParaSet(&local_70,acStack_b8,1);
    uVar4 = *puVar8;
    puVar8 = puVar8 + 4;
    inet_ntoa_b(uVar4,auStack_d8);
    pageParaSet(&local_70,auStack_d8,2);
    pageDynParaListPrintf(&local_70,param_1);
  }
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  iVar1 = httpGetEnv(param_1,"ResAll");
  if ((((iVar1 == 0) || (local_f0 == 0)) || (iVar1 = swResArpCfgAll(puVar5), iVar1 == 1)) ||
     (iVar7 = swArpFindErrorNum(), iVar7 == 0)) {
    pageParaSet(&local_90,&local_f0,0);
    local_f8 = 3;
    pageParaSet(&local_90,&local_f8,1);
    iVar1 = swIsArpTopParaLocked();
    local_f8 = (uint)(iVar1 == 0);
    pageParaSet(&local_90,&local_f8,2);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "arpClientDyn");
    pageDynParaPrintf(&local_90,0,param_1);
    pageDynParaPrintf(&local_90,1,param_1);
    pageDynParaPrintf(&local_90,2,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/LanArpBindingListRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    iVar7 = 10;
  }
LAB_0042627c:
  sVar6 = HttpErrorPage(param_1,iVar7,0,0);
  return sVar6;
}

