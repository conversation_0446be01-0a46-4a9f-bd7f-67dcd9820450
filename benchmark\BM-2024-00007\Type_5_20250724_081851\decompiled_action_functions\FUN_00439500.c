
/* WARNING: Globals starting with '_' overlap smaller symbols at the same address */

int FUN_00439500(undefined4 param_1)

{
  undefined4 *puVar1;
  int iVar2;
  int iVar3;
  char *pcVar4;
  short sVar5;
  int iVar6;
  undefined4 *puVar7;
  uint uVar8;
  undefined4 local_a20;
  undefined4 local_a1c;
  long local_a18;
  uint local_a14;
  int local_a10 [2];
  undefined1 *local_a08;
  undefined4 local_a04;
  int local_a00;
  undefined4 local_9fc;
  int local_9f8;
  undefined4 local_9f4;
  int local_9f0;
  undefined4 local_9ec;
  undefined4 local_9e8;
  undefined auStack_9e0 [18];
  undefined auStack_9ce [16];
  undefined auStack_9be [18];
  undefined auStack_9ac [52];
  undefined auStack_978 [52];
  char acStack_944 [64];
  char acStack_904 [64];
  sysinfo local_8c4;
  undefined auStack_884 [244];
  char acStack_790 [256];
  undefined auStack_690 [4];
  undefined auStack_68c [128];
  undefined auStack_60c [4];
  undefined auStack_608 [4];
  undefined auStack_604 [18];
  undefined auStack_5f2 [18];
  undefined auStack_5e0 [4];
  undefined auStack_5dc [4];
  undefined auStack_5d8 [4];
  undefined auStack_5d4 [4];
  undefined auStack_5d0 [4];
  undefined auStack_5cc [128];
  undefined auStack_54c [128];
  undefined auStack_4cc [128];
  undefined auStack_44c [4];
  undefined auStack_448 [4];
  undefined auStack_444 [4];
  undefined auStack_440 [4];
  undefined auStack_43c [128];
  undefined auStack_3bc [4];
  undefined auStack_3b8 [4];
  undefined auStack_3b4 [4];
  undefined auStack_3b0 [4];
  undefined auStack_3ac [4];
  undefined auStack_3a8 [256];
  undefined auStack_2a8 [256];
  undefined auStack_1a8 [4];
  undefined auStack_1a4 [4];
  undefined auStack_1a0 [4];
  undefined auStack_19c [50];
  undefined auStack_16a [50];
  undefined auStack_138 [256];
  uint local_38;
  code *local_34;
  undefined4 *local_30;
  
  local_a1c = 20000;
  iVar2 = wlanGetDetectStatus(0);
  if ((iVar2 != 0) && ("" != 0)) {
    "" = ~"";
  }
  memset(auStack_9e0,0,"2");
  iVar6 = 10;
  memset(auStack_690,0,724);
  memset(cStatusStatist,0,"(");
  memset(&cStatusWan,0,456);
  memset(auStack_884,0,244);
  statusMainDynArray = auStack_3bc;
  "" = auStack_3b8;
  "" = auStack_3b4;
  "" = auStack_3b0;
  "" = auStack_3ac;
  "" = auStack_3a8;
  "" = auStack_2a8;
  "" = auStack_1a8;
  "" = auStack_1a4;
  "" = auStack_1a0;
  iVar2 = getStatusWithCpuUsage();
  if (iVar2 != 0) {
    
    "" = auStack_19c;
  }
  iVar3 = getStatusWithMemoUsage();
  iVar2 = iVar6;
  if (iVar3 != 0) {
    
    (&statusMainDynArray)[iVar6 * 2] = auStack_16a;
  }
  iVar3 = getStatusWithNatSession();
  iVar6 = iVar2;
  if (iVar3 != 0) {
    
    (&statusMainDynArray)[iVar2 * 2] = auStack_138;
  }
  (&statusMainDynArray)[iVar6 * 2] = 0;
  
  "" = 0;
  "" = 0;
  "" = 0;
  "" = 0;
  "" = 0;
  
  "" = 0;
  "" = 0;
  "" = 0;
  iVar2 = getStatusWithCpuUsage();
  iVar6 = 10;
  if (iVar2 != 0) {
    
    
  }
  iVar3 = getStatusWithMemoUsage();
  iVar2 = iVar6;
  if (iVar3 != 0) {
    
    ("")[iVar6 * 2] = "2";
  }
  iVar6 = getStatusWithNatSession();
  if (iVar6 != 0) {
    ("")[iVar2 * 2] = 256;
  }
  statusLanDynArray = auStack_9e0;
  "" = auStack_9ce;
  "" = auStack_9be;
  statusWlanDynArray = auStack_690;
  "" = auStack_68c;
  "" = auStack_60c;
  "" = auStack_608;
  "" = auStack_604;
  "" = auStack_5f2;
  "" = auStack_5e0;
  "" = auStack_5dc;
  "" = auStack_5d8;
  "" = auStack_5d4;
  "" = auStack_5d0;
  "" = auStack_5cc;
  "" = auStack_54c;
  "" = auStack_4cc;
  "" = auStack_44c;
  "" = auStack_448;
  "" = auStack_444;
  "" = auStack_440;
  "" = auStack_43c;
  
  
  
  "" = 0;
  
  
  "" = 0;
  
  "" = 0;
  "" = 0;
  "" = 0;
  "" = 0;
  
  
  
  
  "" = 0;
  "" = 0;
  "" = 0;
  "" = 0;
  "" = 0;
  "" = 0;
  "" = 0;
  "" = 0;
  local_a20 = 0;
  pageParaSet(&statusMainDynArray,&local_a20,0);
  local_a14 = getMaxWanPortNumber();
  pageParaSet(&statusMainDynArray,&local_a14,1);
  
  pageParaSet(&statusMainDynArray,&local_a20,2);
  pageParaSet(&statusMainDynArray,&local_a1c,3);
  sysinfo(&local_8c4);
  local_a18 = local_8c4.uptime;
  pageParaSet(&statusMainDynArray,&local_a18,4);
  pcVar4 = getSysSoftwareRevisionPrefix();
  strcpy(acStack_944,pcVar4);
  pcVar4 = getSysSoftwareRevision();
  strcat(acStack_944,pcVar4);
  iVar2 = getIsFirmwareBetaEdition();
  if (iVar2 == 1) {
    strcat(acStack_944,"");
  }
  pageParaSet(&statusMainDynArray,acStack_944,5);
  pcVar4 = getSysHardwareRevision();
  strcpy(acStack_904,pcVar4);
  iVar2 = getIsFirmwareBetaEdition();
  if (iVar2 == 1) {
    strcat(acStack_904,"");
  }
  pageParaSet(&statusMainDynArray,acStack_904,6);
  local_a20 = 0;
  pageParaSet(&statusMainDynArray,&local_a20,8);
  local_a20 = 3;
  pageParaSet(&statusMainDynArray,&local_a20,9);
  swGetSystemMode(local_a10);
  pageParaSet(&statusMainDynArray,local_a10,7);
  iVar2 = getStatusWithCpuUsage();
  if (iVar2 != 0) {
    getSysCpuUsage(auStack_9ac);
    pageParaSet(&statusMainDynArray,auStack_9ac,10);
  }
  iVar2 = getStatusWithMemoUsage();
  if (iVar2 != 0) {
    getSysMemUsage(auStack_978);
    pageParaSet(&statusMainDynArray,auStack_978,11);
  }
  iVar2 = getStatusWithNatSession();
  if (iVar2 != 0) {
    strcpy(acStack_790,
           "<A href=\"activeSessionRpm.htm?curPage=1\" id=\"t_detail_click\" target=\"_parent\">Detail</A>"
          );
    pageParaSet(&statusMainDynArray,acStack_790,12);
  }
  puVar7 = &elmRpm;
  do {
    if ((code *)*puVar7 != 0) {
      (*(code *)*puVar7)(param_1);
    }
    puVar7 = puVar7 + 1;
  } while (puVar7 != &systemArpEntries);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    sVar5 = HttpDenyPage(param_1);
  }
  else {
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "statusPara");
    iVar2 = 0;
    do {
      
      pageDynParaPrintf(&statusMainDynArray,iVar2,param_1);
      iVar2 = iVar6;
    } while (iVar6 != 13);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "lanPara");
    pageDynParaListPrintf(&statusLanDynArray,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "wlanPara");
    iVar2 = 0;
    do {
      
      pageDynParaPrintf(&statusWlanDynArray,iVar2,param_1);
      iVar2 = iVar6;
    } while (iVar6 != 19);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "statistList");
    puVar7 = (undefined4 *)(cStatusStatist + 8);
    for (uVar8 = 0; iVar2 = uVar8 * "@", uVar8 < local_a14; uVar8 = uVar8 + 1) {
      local_a08 = cStatusStatComma + iVar2;
      local_9e8 = 0;
      
      
      
      
      
      
      
      FUN_004393a8(local_a08,puVar7[-1]);
      FUN_004393a8(0x005be044,*puVar7);
      FUN_004393a8(0x005be054,puVar7[1]);
      puVar1 = puVar7 + 2;
      puVar7 = puVar7 + 5;
      FUN_004393a8(0x005be064,*puVar1);
      pageDynParaListPrintf(&local_a08,param_1);
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    if ((local_a10[0] == 1) || (local_a10[0] == 4)) {
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "wanPara");
      local_38 = 0;
      puVar7 = &cStatusWan;
      while (local_38 < local_a14) {
        local_34 = pageDynParaListPrintf;
        _
        _
        _
        _
        _0x00000000 = 0;
        _0x00000000 = 0;
        _0x00000000 = 0;
        _0x00000000 = 0;
        _0x00000000 = 0;
        _0x00000000 = 0;
        _0x00000000 = 0;
        "" = puVar7 + local_38 * "m";
        statusWanDynArray = puVar7 + local_38 * "9";
        "" = puVar7 + local_38 * "c";
        "" = puVar7 + local_38 * "C";
        "" = puVar7 + local_38 * "I";
        "" = puVar7 + local_38 * "O";
        "" = puVar7 + local_38 * "o";
        
        "" = puVar7 + local_38 * "i";
        "" = statusWanDynArray + 22;
        "" = statusWanDynArray + 15;
        "" = statusWanDynArray + 21;
        "" = puVar7 + local_38 * "Q";
        "" = puVar7 + local_38 * "[";
        "" = puVar7 + local_38 * "e";
        "" = puVar7 + local_38 * "q";
        "" = statusWanDynArray + 1;
        
        
        
        
        
        
        "" = 0;
        
        "" = 0;
        "" = 0;
        
        
        "" = 0;
        "" = 0;
        "" = 0;
        "" = 0;
        "" = 0;
        local_38 = local_38 + 1;
        local_30 = puVar7;
        pageDynParaListPrintf(&statusWanDynArray,param_1);
        puVar7 = local_30;
      }
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    }
    HttpWebV4Head(param_1,0,1);
    switch(local_a10[0]) {
    default:
    case 1:
      pcVar4 = "/userRpm/StatusRpm.htm";
      break;
    case 3:
    case 6:
    case 7:
    case 8:
      pcVar4 = "/userRpm/StatusRpm_AP.htm";
      break;
    case 4:
      pcVar4 = "/userRpm/StatusRpm_APC.htm";
    }
    iVar2 = httpRpmFsA(param_1,pcVar4);
    if (iVar2 == 2) {
      return 2;
    }
    sVar5 = HttpErrorPage(param_1,10,0,0);
  }
  return sVar5;
}

