
int FUN_0042ae30(undefined4 param_1)

{
  int iVar1;
  short sVar6;
  char *pcVar2;
  uint uVar3;
  in_addr iVar4;
  size_t sVar5;
  undefined4 uVar7;
  int iVar8;
  uint uVar9;
  undefined4 local_3f8;
  uint local_3f4;
  uint local_3f0;
  undefined auStack_3ec [8];
  undefined auStack_3e4 [64];
  in_addr local_3a4;
  in_addr local_3a0;
  in_addr local_39c;
  in_addr local_398;
  in_addr local_394;
  undefined *local_37c;
  undefined4 local_378;
  char *local_374;
  undefined4 local_370;
  char *local_36c;
  undefined4 local_368;
  char *local_364;
  undefined4 local_360;
  char *local_35c;
  undefined4 local_358;
  char *local_354;
  undefined4 local_350;
  char *local_34c;
  undefined4 local_348;
  char *local_344;
  undefined4 local_340;
  int *local_33c;
  undefined4 local_338;
  char *local_334;
  undefined4 local_330;
  int *local_32c;
  undefined4 local_328;
  uint *local_324;
  undefined4 local_320;
  uint *local_31c;
  undefined4 local_318;
  char *local_314;
  undefined4 local_310;
  undefined *local_30c;
  undefined4 local_308;
  undefined4 local_304;
  char local_2fc [119];
  undefined local_285;
  char local_284 [119];
  undefined local_20d;
  uint local_20c;
  int local_208;
  in_addr local_204;
  undefined auStack_200 [4];
  char acStack_1fc [120];
  char acStack_184 [120];
  char acStack_10c [16];
  char acStack_fc [16];
  char acStack_ec [16];
  char acStack_dc [16];
  char acStack_cc [16];
  int local_bc;
  char acStack_b8 [16];
  int local_a8;
  uint local_a4;
  uint local_a0;
  char acStack_9c [127];
  undefined local_1d;
  undefined auStack_1c [8];
  
  local_37c = auStack_200;
  local_374 = acStack_1fc;
  local_36c = acStack_184;
  local_364 = acStack_10c;
  local_35c = acStack_fc;
  local_354 = acStack_ec;
  local_34c = acStack_dc;
  local_344 = acStack_cc;
  local_33c = &local_bc;
  local_334 = acStack_b8;
  local_32c = &local_a8;
  local_324 = &local_a4;
  local_31c = &local_a0;
  local_314 = acStack_9c;
  local_30c = auStack_1c;
  local_330 = 16;
  local_360 = 16;
  local_358 = 16;
  local_350 = 16;
  local_348 = 16;
  local_340 = 16;
  local_368 = "x";
  local_310 = 128;
  local_370 = "x";
  local_304 = 0;
  local_378 = 0;
  local_338 = 0;
  local_328 = 0;
  local_320 = 0;
  local_318 = 0;
  local_308 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar6 = HttpDenyPage(param_1);
    goto LAB_0042b6c0;
  }
  pcVar2 = httpGetEnv(param_1,"wan");
  uVar9 = 0;
  if (((pcVar2 != 0) && (uVar9 = atoi(pcVar2), uVar9 < 0)) ||
     (uVar3 = getMaxWanPortNumber(), uVar3 <= uVar9)) {
    uVar9 = 0;
  }
  memset(local_2fc,0,252);
  iVar1 = httpGetEnv(param_1,"Connect");
  if ((iVar1 == 0) && (iVar1 = httpGetEnv(param_1,"Save"), iVar1 == 0)) {
    iVar1 = httpGetEnv(param_1,"Disconnect");
    if (iVar1 != 0) {
      swDhcpPlusStop(uVar9);
      uVar7 = "<";
LAB_0042b2dc:
      taskDelay(uVar7);
    }
LAB_0042b2ec:
    swGetDhcpPlusCfg(uVar9,local_2fc);
    swDhcpPlusStatusGet();
    dhcpPlusDhcpParaGet(auStack_3e4);
    strcpy(acStack_1fc,local_2fc);
    strcpy(acStack_184,local_284);
    pcVar2 = inet_ntoa(local_3a4);
    sprintf(acStack_10c,"%s",pcVar2);
    pcVar2 = inet_ntoa(local_3a0);
    sprintf(acStack_fc,"%s",pcVar2);
    pcVar2 = inet_ntoa(local_39c);
    sprintf(acStack_ec,"%s",pcVar2);
    pcVar2 = inet_ntoa(local_398);
    sprintf(acStack_dc,"%s",pcVar2);
    pcVar2 = inet_ntoa(local_394);
    sprintf(acStack_cc,"%s",pcVar2);
    local_bc = local_208;
    pcVar2 = inet_ntoa(local_204);
    sprintf(acStack_b8,"%s",pcVar2);
    local_a8 = 2 - (uint)(local_20c == 1);
    iVar1 = swDhcpPlusIsConnecting();
    local_a0 = (uint)(iVar1 == 0);
    local_a4 = (uint)(iVar1 != 0);
    iVar1 = swDhcpPlusIsConnected();
    if (iVar1 != 0) {
      local_a0 = 0;
    }
    pcVar2 = swDhcpPlusStateStrGet();
    sVar5 = strlen(pcVar2);
    if (sVar5 < 128) {
      pcVar2 = swDhcpPlusStateStrGet();
      strcpy(acStack_9c,pcVar2);
      local_1d = 0;
    }
    local_3f8 = getMaxWanPortNumber();
    pageParaSet(&local_37c,&local_3f8,0);
    pageParaSet(&local_37c,acStack_184,2);
    swGetSystemMode(auStack_3ec);
    local_3f8 = 3;
    pageParaSet(&local_37c,&local_3f8,14);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "dhcpPlusInf");
    iVar1 = 0;
    do {
      iVar8 = iVar1 + 1;
      pageDynParaPrintf(&local_37c,iVar1,param_1);
      iVar1 = iVar8;
    } while (iVar8 != 15);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    httpPrintfWanTypeInfo(param_1);
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/WanDhcpPlusCfgRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    uVar7 = 10;
  }
  else {
    pcVar2 = httpGetEnv(param_1,"dhcpPlusUsrName");
    if (pcVar2 == 0) {
      local_2fc[0] = '\0';
    }
    else {
      local_285 = 0;
      strncpy(local_2fc,pcVar2,"w");
    }
    pcVar2 = httpGetEnv(param_1,"dhcpPlusUsrPsw");
    if (pcVar2 == 0) {
      local_284[0] = '\0';
    }
    else {
      local_20d = 0;
      strncpy(local_284,pcVar2,"w");
    }
    pcVar2 = httpGetEnv(param_1,"mtu");
    if ((pcVar2 == 0) || (local_208 = atoi(pcVar2), 924 < local_208 - 0x240U)) {
      local_208 = 0x5dc;
    }
    pcVar2 = httpGetEnv(param_1,"defaultSvrAddr");
    if (pcVar2 == 0) {
      iVar4.s_addr = inet_addr("************");
LAB_0042b1b8:
      local_204.s_addr = iVar4.s_addr;
      pcVar2 = httpGetEnv(param_1,"linktype");
      if (pcVar2 == 0) {
        local_20c = 0;
      }
      else {
        iVar1 = atoi(pcVar2);
        local_20c = (uint)(iVar1 == 1);
      }
      swSetDhcpPlusCfg(uVar9,local_2fc);
      iVar1 = httpGetEnv(param_1,"Connect");
      if ((iVar1 != 0) || (local_20c == 1)) {
        iVar1 = swDhcpcStateIsStop();
        if (iVar1 != 0) {
          ucDhcpcStart(uVar9);
        }
        swDhcpPlusStart(uVar9);
        uVar7 = 30;
        goto LAB_0042b2dc;
      }
      goto LAB_0042b2ec;
    }
    iVar4.s_addr = inet_addr(pcVar2);
    swGetLanIpMask(&local_3f4,&local_3f0);
    iVar1 = swChkLegalIpAddr(iVar4.s_addr);
    if ((iVar1 != 0) && (((iVar4.s_addr ^ local_3f4) & local_3f0) != 0)) goto LAB_0042b1b8;
    uVar7 = 0x13a8;
  }
  sVar6 = HttpErrorPage(param_1,uVar7,0,0);
LAB_0042b6c0:
  return sVar6;
}

