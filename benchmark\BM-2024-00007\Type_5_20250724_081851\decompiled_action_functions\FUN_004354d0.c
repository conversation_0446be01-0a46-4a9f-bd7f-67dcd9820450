
int FUN_004354d0(undefined4 param_1)

{
  int iVar1;
  char *pcVar2;
  long lVar3;
  int iVar4;
  uint uVar5;
  short sVar6;
  undefined4 uVar7;
  int local_70;
  int local_6c;
  int local_68 [2];
  undefined auStack_60 [4];
  undefined auStack_5c [4];
  undefined4 local_58;
  undefined4 local_54;
  int local_50;
  undefined *local_4c;
  undefined4 local_48;
  undefined *local_44;
  undefined4 local_40;
  undefined4 *local_3c;
  undefined4 local_38;
  undefined4 *local_34;
  undefined4 local_30;
  int *local_2c;
  undefined4 local_28;
  undefined4 local_24;
  
  local_4c = auStack_60;
  local_44 = auStack_5c;
  local_3c = &local_58;
  local_34 = &local_54;
  local_2c = &local_50;
  local_70 = 0;
  local_24 = 0;
  local_48 = 0;
  local_40 = 0;
  local_38 = 0;
  local_30 = 0;
  local_28 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    iVar1 = HttpDenyPage(param_1);
    iVar1 = iVar1 << 16;
LAB_004358dc:
    iVar1 = iVar1 >> 16;
  }
  else {
    pcVar2 = httpGetEnv(param_1,"ClientId");
    iVar1 = 0;
    if (pcVar2 != 0) {
      lVar3 = atol(pcVar2);
      iVar1 = (int)lVar3;
    }
    iVar4 = wzdStepSetFlag(9,0);
    if (iVar4 == 0) {
      return 2;
    }
    iVar4 = httpGetEnv(param_1,"Next");
    if (iVar4 == 0) {
      iVar4 = httpGetEnv(param_1,"Return");
      if (iVar4 == 0) {
        pcVar2 = httpGetEnv(param_1,"wan");
        if (pcVar2 == 0) {
          sVar6 = swGetWanType(0);
        }
        else {
          iVar4 = atoi(pcVar2);
          sVar6 = iVar4;
        }
        local_6c = sVar6;
        pageParaSet(&local_4c,&local_6c,0);
        local_6c = iVar1;
        pageParaSet(&local_4c,&local_6c,1);
        swGetSystemMode(local_68);
        local_58 = 3;
        local_54 = swIsMultiSystemMode();
        iVar1 = getProductId();
        if (iVar1 == 0x8020001) {
          local_68[0] = getWzdSysMode();
        }
        local_50 = local_68[0];
        iVar1 = getProductId();
        if (iVar1 == 0x8100001) {
          local_50 = getWzdSysMode();
        }
        httpPrintf(param_1,
                   "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n"
                   ,"wzdWanTypeInf");
        pageDynParaPrintf(&local_4c,0,param_1);
        pageDynParaPrintf(&local_4c,1,param_1);
        pageDynParaPrintf(&local_4c,2,param_1);
        pageDynParaPrintf(&local_4c,3,param_1);
        pageDynParaPrintf(&local_4c,4,param_1);
        httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
        httpWizardPrintStepInfo(param_1);
        HttpWebV4Head(param_1,0,0);
        iVar1 = httpRpmFsA(param_1,"/userRpm/WzdWanTypeRpm.htm");
        if (iVar1 != 2) {
          iVar1 = HttpErrorPage(param_1,10,0,0);
          iVar1 = iVar1 << 16;
          goto LAB_004358dc;
        }
      }
      else {
        iVar1 = wzdStepFindPrev(&local_70);
        if (iVar1 != 0) goto LAB_004358c4;
      }
    }
    else {
      pcVar2 = httpGetEnv(param_1,"wan");
      if (pcVar2 != 0) {
        uVar5 = atoi(pcVar2);
        iVar1 = getProductId();
        if ((iVar1 != 0x8020001) &&
           (0x00000010 = (int)uVar5, iVar1 = swIsWanConnected(0), iVar1 == 0)) {
          iVar1 = FUN_0043537c(param_1);
          return iVar1;
        }
        switch(uVar5 & -1) {
        case 0:
          iVar1 = wzdStepBrunchActive(4);
          if (iVar1 == 0) goto LAB_00435b54;
          if ("" == 1) {
            uVar7 = 0;
LAB_004357ec:
            iVar1 = wzdStepSetFlag(9,uVar7);
            if (iVar1 == 0) {
              return 2;
            }
          }
          else {
            pcVar2 = getLanguage(9);
            iVar1 = strcmp(pcVar2,"CN");
            if (iVar1 != 0) {
              iVar1 = getProductId();
              if (iVar1 == 0x8020001) {
                iVar1 = getWzdSysMode();
              }
              else {
                swGetSystemMode(local_68);
                iVar1 = local_68[0];
              }
              iVar4 = getProductId();
              if (iVar4 == 0x8100001) {
                iVar1 = getWzdSysMode();
              }
              if (iVar1 != 4) {
                uVar7 = 1;
                goto LAB_004357ec;
              }
            }
          }
          "" = 0;
          goto LAB_0043588c;
        case 1:
          uVar7 = 5;
          break;
        case 2:
          uVar7 = 6;
          break;
        default:
          iVar1 = FUN_00435f34(param_1);
          return iVar1;
        case 6:
          uVar7 = 7;
          break;
        case 7:
          uVar7 = 8;
        }
        iVar1 = wzdStepBrunchActive(uVar7);
        if (iVar1 == 0) {
          return 2;
        }
      }
LAB_0043588c:
      iVar1 = wzdStepFindNext(&local_70);
      if (iVar1 != 0) {
        printf("():Turn to next page id:%d!, tag:%d\r\n",*(undefined4 *)(local_70 + 4),
               *(undefined4 *)(local_70 + "P"));
LAB_004358c4:
        iVar1 = GoUrl(param_1,local_70 + 8);
        iVar1 = iVar1 << 16;
        goto LAB_004358dc;
      }
    }
LAB_00435b54:
    iVar1 = 2;
  }
  return iVar1;
}

