
int FUN_00444dac(undefined4 param_1)

{
  int iVar1;
  short sVar3;
  char *pcVar2;
  char *__nptr;
  undefined4 uVar4;
  int local_28;
  undefined auStack_24 [4];
  undefined *local_20;
  undefined4 local_1c;
  undefined4 local_18;
  
  local_20 = auStack_24;
  local_28 = 0;
  local_18 = 0;
  local_1c = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar3 = HttpDenyPage(param_1);
    goto LAB_00445080;
  }
  pcVar2 = httpGetEnv(param_1,"ReqAccessed");
  if (pcVar2 == 0) {
LAB_00444ea4:
    local_28 = 0;
  }
  else {
    do {
      __nptr = pcVar2;
      pcVar2 = __nptr + 1;
    } while (*__nptr == ' ');
    if (__nptr == 0) goto LAB_00444ea4;
    local_28 = atoi(__nptr);
  }
  if (("" == '\0') && (local_28 == 0)) {
    swGetTimeAuto();
    "" = '\x01';
    local_28 = 1;
    taskDelay("Z");
  }
  iVar1 = swSntpStatusProcess();
  if (iVar1 == 0) {
    iVar1 = swSntpStatusSuccess();
    if (iVar1 != 0) {
      "" = 1;
      "" = '\0';
      iVar1 = FUN_004450a4(param_1);
      return iVar1;
    }
    iVar1 = swSntpStatusFailed();
    if (iVar1 == 0) goto LAB_00444fb0;
    "" = '\0';
    pcVar2 = "../userRpm/DateTimeCfgRpm.htm";
    uVar4 = 0x13a4;
  }
  else {
    puts("sntp proccessing");
LAB_00444fb0:
    pageParaSet(&local_20,&local_28,0);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "getGmtInf");
    pageDynParaPrintf(&local_20,0,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar1 = httpRpmFsA(param_1,"/userRpm/GetGMTRpm.htm");
    if (iVar1 == 2) {
      return 2;
    }
    uVar4 = 10;
    pcVar2 = 0;
  }
  sVar3 = HttpErrorPage(param_1,uVar4,pcVar2,0);
LAB_00445080:
  return sVar3;
}

