POST /HNAP1/ HTTP/1.1
Host: *************:32775
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0
Accept: */*
Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2
Accept-Encoding: gzip, deflate
Content-Type: text/xml; charset=utf-8
SOAPAction: "http://purenetworks.com/HNAP1/SetIPv4FirewallSettings"
HNAP_AUTH: 20FE2B4C34A27D113678BC5E815F3944 1732365295
X-Requested-With: XMLHttpRequest
Content-Length: 948
Origin: http://*************:32775
Connection: keep-alive
Referer: http://*************:32775/Wireless.html
Cookie: uid=ujcI4DPmyw; PrivateKey=C8B843B87E7C03EF5F224D6D4949A7F1; timeout=8

<?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Body><SetIPv4FirewallSettings xmlns="http://purenetworks.com/HNAP1/"><IPv4_FirewallStatus>1</IPv4_FirewallStatus><IPv4FirewallRuleLists>1</IPv4FirewallRuleLists><IPv4FirewallRule>1</IPv4FirewallRule><Name>1</Name><SrcInterface>1</SrcInterface><SrcIPv4AddressRangeStart>1</SrcIPv4AddressRangeStart><SrcIPv4AddressRangeEnd>1</SrcIPv4AddressRangeEnd><ProtocolSrcRangeStart>1</ProtocolSrcRangeStart><ProtocolSrcRangeEnd>1</ProtocolSrcRangeEnd><DestInterface>1</DestInterface><DestIPv4AddressRangeStart>1</DestIPv4AddressRangeStart><DestIPv4AddressRangeEnd>1</DestIPv4AddressRangeEnd><ProtocolRangeStart>1</ProtocolRangeStart><ProtocolRangeEnd>1</ProtocolRangeEnd><Protocol>1</Protocol></SetIPv4FirewallSettings></soap:Body></soap:Envelope>
