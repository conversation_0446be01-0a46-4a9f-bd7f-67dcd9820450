
undefined4 FUN_00438674(void)

{
  int iVar1;
  int iVar2;
  int iVar3;
  int iVar4;
  char *__src;
  void *__ptr;
  undefined4 local_184;
  int local_180;
  int local_158;
  in_addr local_154;
  ushort local_150;
  ushort local_14e;
  char local_14c;
  undefined auStack_12c [20];
  uint local_118;
  uint local_114;
  char local_10f;
  char acStack_10c [260];
  
  memset(acStack_10c,0,256);
  apmib_get("o",&local_158);
  iVar1 = mxmlNewXML("1.0");
  if (iVar1 == 0) {
    puts("Create new xml error!!!");
  }
  else {
    iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
    if (iVar2 == 0) {
      puts("Create new element error!!!");
      mxmlDelete(iVar1);
    }
    else {
      mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
      mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
      mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
      iVar2 = mxmlNewElement(iVar2,"soap:Body");
      if (iVar2 == 0) {
        puts("Create new element error!!!");
        mxmlDelete(iVar1);
      }
      else {
        iVar2 = mxmlNewElement(iVar2,"GetVirtualServerSettingsResponse");
        if (iVar2 == 0) {
          puts("Create new element error!!!");
          mxmlDelete(iVar1);
        }
        else {
          mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
          iVar3 = mxmlNewElement(iVar2,"GetVirtualServerSettingsResult");
          if (iVar3 == 0) {
            puts("Create new element error!!!");
            mxmlDelete(iVar1);
          }
          else {
            iVar3 = mxmlNewText(iVar3,0,"O");
            if (iVar3 == 0) {
              puts("Create new text error!!!");
              mxmlDelete(iVar1);
            }
            else {
              iVar2 = mxmlNewElement(iVar2,"VirtualServerList");
              if (iVar2 == 0) {
                puts("Create new element error!!!");
                mxmlDelete(iVar1);
              }
              else {
                if (0 < local_158) {
                  for (local_180 = 1; local_180 <= local_158; local_180 = local_180 + 1) {
                    memset(&local_154,0,"F");
                    local_154.s_addr._0_1_ = local_180;
                    apmib_get(0x8070,&local_154);
                    iVar3 = mxmlNewElement(iVar2,"VirtualServerInfo");
                    if (iVar3 == 0) {
                      printf("Create new element error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    if (local_10f == '\x01') {
                      memcpy(acStack_10c,"true",5);
                    }
                    else {
                      memcpy(acStack_10c,"false",6);
                    }
                    iVar4 = mxmlNewElement(iVar3,"Enabled");
                    if (iVar4 == 0) {
                      puts("Create new element error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    iVar4 = mxmlNewText(iVar4,0,acStack_10c);
                    if (iVar4 == 0) {
                      puts("Create new text error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    iVar4 = mxmlNewElement(iVar3,"VirtualServerDescription");
                    if (iVar4 == 0) {
                      puts("Create new element error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    iVar4 = mxmlNewText(iVar4,0,auStack_12c);
                    if (iVar4 == 0) {
                      puts("Create new text error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    if ((local_118 << 8) >> 16 == ((local_114 & 255) << 8 | local_118 >> 24)) {
                      snprintf(acStack_10c,255,"%u",(local_118 << 8) >> 16);
                    }
                    else {
                      snprintf(acStack_10c,255,"%u-%u",(local_118 << 8) >> 16,
                               (local_114 & 255) << 8 | local_118 >> 24);
                    }
                    iVar4 = mxmlNewElement(iVar3,"ExternalPort");
                    if (iVar4 == 0) {
                      puts("Create new element error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    iVar4 = mxmlNewText(iVar4,0,acStack_10c);
                    if (iVar4 == 0) {
                      printf("Create new text error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    if (local_150 == local_14e) {
                      snprintf(acStack_10c,255,"%u",local_150);
                    }
                    else {
                      snprintf(acStack_10c,255,"%u-%u",local_150,local_14e);
                    }
                    iVar4 = mxmlNewElement(iVar3,"InternalPort");
                    if (iVar4 == 0) {
                      puts("Create new element error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    iVar4 = mxmlNewText(iVar4,0,acStack_10c);
                    if (iVar4 == 0) {
                      puts("Create new text error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    if (local_14c == '\x01') {
                      memcpy(acStack_10c,&PTR_0x00504354,4);
                      local_184 = 6;
                    }
                    else if (local_14c == '\x02') {
                      memcpy(acStack_10c,&PTR_0x00504455,4);
                      local_184 = 17;
                    }
                    else if (local_14c == '\x03') {
                      memcpy(acStack_10c,"BOTH",5);
                      local_184 = 0;
                    }
                    else {
                      memcpy(acStack_10c,"Other",6);
                      local_184 = -1;
                    }
                    iVar4 = mxmlNewElement(iVar3,"ProtocolType");
                    if (iVar4 == 0) {
                      puts("Create new element error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    iVar4 = mxmlNewText(iVar4,0,acStack_10c);
                    if (iVar4 == 0) {
                      puts("Create new text error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    snprintf(acStack_10c,255,"%d",local_184);
                    iVar4 = mxmlNewElement(iVar3,"protocolNumber");
                    if (iVar4 == 0) {
                      puts("Create new element error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    iVar4 = mxmlNewText(iVar4,0,acStack_10c);
                    if (iVar4 == 0) {
                      puts("Create new text error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    __src = inet_ntoa(local_154);
                    strcpy(acStack_10c,__src);
                    iVar4 = mxmlNewElement(iVar3,"LocalIPAddress");
                    if (iVar4 == 0) {
                      puts("Create new element error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    iVar4 = mxmlNewText(iVar4,0,acStack_10c);
                    if (iVar4 == 0) {
                      puts("Create new text error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    iVar3 = mxmlNewElement(iVar3,"ScheduleName");
                    if (iVar3 == 0) {
                      puts("Create new element error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                    iVar3 = mxmlNewText(iVar3,0,"all");
                    if (iVar3 == 0) {
                      puts("Create new text error!!!");
                      mxmlDelete(iVar1);
                      return 0;
                    }
                  }
                }
                __ptr = mxmlSaveAllocString(iVar1,0);
                if (__ptr != 0) {
                  FUN_0041ed70("",200,__ptr,"");
                  free(__ptr);
                }
                mxmlDelete(iVar1);
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

