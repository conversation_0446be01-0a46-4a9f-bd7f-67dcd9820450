
int FUN_004297f0(undefined4 param_1)

{
  bool bVar1;
  int iVar2;
  short sVar4;
  char *__src;
  uint uVar3;
  undefined1 *puVar5;
  uint uVar6;
  byte *pbVar7;
  byte *pbVar8;
  uint local_110;
  char local_10c [8];
  int local_104 [2];
  byte local_fc [24];
  undefined4 local_e4;
  undefined4 local_e0;
  undefined4 local_dc;
  undefined4 local_d8;
  char local_d4 [20];
  char acStack_c0 [17];
  undefined local_af;
  undefined *local_ac;
  undefined4 local_a8;
  undefined *local_a4;
  undefined4 local_a0;
  undefined4 local_9c;
  undefined auStack_94 [4];
  undefined auStack_90 [20];
  undefined auStack_7c [4];
  undefined *local_78;
  undefined4 local_74;
  undefined *local_70;
  undefined4 local_6c;
  undefined *local_68;
  undefined4 local_64;
  undefined4 local_60;
  undefined auStack_58 [18];
  undefined auStack_46 [22];
  byte *local_30;
  
  swGetSystemMode(local_104);
  local_ac = auStack_58;
  local_a4 = auStack_46;
  local_78 = auStack_94;
  local_70 = auStack_90;
  local_68 = auStack_7c;
  local_6c = 18;
  local_a8 = 18;
  local_a0 = 18;
  local_9c = 0;
  local_60 = 0;
  local_74 = 0;
  local_64 = 0;
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    sVar4 = HttpDenyPage(param_1);
    goto LAB_00429e14;
  }
  pbVar7 = local_fc;
  local_d4[4] = '\0';
  local_d4[5] = '\0';
  local_d4[6] = '\0';
  local_d4[7] = '\0';
  local_d4[8] = '\0';
  local_d4[9] = '\0';
  local_d4[10] = '\0';
  local_d4[11] = '\0';
  local_d4[12] = '\0';
  local_d4[13] = '\0';
  local_d4[14] = '\0';
  local_d4[15] = '\0';
  local_d4[16] = '\0';
  local_d4[17] = '\0';
  local_d8 = 0;
  local_e0 = 0;
  local_dc = 0;
  local_10c[4] = 0;
  local_fc[4] = 0;
  local_fc[5] = 0;
  local_fc[6] = 0;
  local_fc[7] = 0;
  local_fc[8] = 0;
  local_fc[9] = 0;
  local_fc[10] = 0;
  local_fc[11] = 0;
  local_e4 = 0;
  local_10c[0] = '\0';
  local_10c[1] = '\0';
  local_10c[2] = '\0';
  local_10c[3] = '\0';
  local_fc[0] = 0;
  local_fc[1] = 0;
  local_fc[2] = 0;
  local_fc[3] = 0;
  local_d4[0] = '\0';
  local_d4[1] = '\0';
  local_d4[2] = '\0';
  local_d4[3] = '\0';
  HttpClientMacGet(param_1,local_d4);
  iVar2 = httpGetEnv(param_1,"Save");
  if (iVar2 == 0) {
LAB_00429b20:
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "macCloneList");
    pbVar8 = local_fc + 12;
    uVar6 = 0;
    swGetWanMac(pbVar8);
    iVar2 = 0;
    swGetWanDefaultMac(local_fc);
    pbVar7 = local_fc;
    local_30 = pbVar8;
    while( true ) {
      uVar3 = getMaxWanPortNumber();
      bVar1 = uVar3 <= uVar6;
      uVar6 = uVar6 + 1;
      if break;
      sprintf(acStack_c0,"%02X-%02X-%02X-%02X-%02X-%02X",local_30[iVar2],pbVar8[1],
              pbVar8[2],pbVar8[3],pbVar8[4],pbVar8[5]);
      pageParaSet(&local_ac,acStack_c0,0);
      sprintf(acStack_c0,"%02X-%02X-%02X-%02X-%02X-%02X",local_fc[iVar2],pbVar7[1],
              pbVar7[2],pbVar7[3],pbVar7[4],pbVar7[5]);
      iVar2 = iVar2 + 6;
      pageParaSet(&local_ac,acStack_c0,1);
      pageDynParaListPrintf(&local_ac,param_1);
      pbVar7 = pbVar7 + 6;
      pbVar8 = pbVar8 + 6;
    }
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    local_110 = getMaxWanPortNumber();
    pageParaSet(&local_78,&local_110,0);
    pageParaSet(&local_78,local_d4,1);
    iVar2 = strcmp(local_d4,"00-00-00-00-00-00");
    local_110 = (uint)(iVar2 == 0);
    pageParaSet(&local_78,&local_110,2);
    httpPrintf(param_1,
               "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
               "macCloneInf");
    pageDynParaPrintf(&local_78,0,param_1);
    pageDynParaPrintf(&local_78,1,param_1);
    pageDynParaPrintf(&local_78,2,param_1);
    httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
    HttpWebV4Head(param_1,0,1);
    iVar2 = httpRpmFsA(param_1,"/userRpm/MacCloneCfgRpm.htm");
    if (iVar2 == 2) {
      return 2;
    }
    iVar2 = 10;
    puVar5 = 0;
  }
  else {
    swGetWanMac(pbVar7);
    uVar6 = 0;
    while( true ) {
      uVar3 = getMaxWanPortNumber();
      bVar1 = uVar3 <= uVar6;
      uVar6 = uVar6 + 1;
      if break;
      sprintf(local_10c,"mac%d",uVar6);
      __src = httpGetEnv(param_1,local_10c);
      if (__src != 0) {
        local_af = 0;
        strncpy(acStack_c0,__src,17);
        iVar2 = swChkStrMacAddr(acStack_c0);
        if (iVar2 == 0) {
          puVar5 = "";
          iVar2 = 0x1399;
          goto LAB_00429e0c;
        }
        swMacStr2Eth(acStack_c0,pbVar7);
      }
      pbVar7 = pbVar7 + 6;
    }
    iVar2 = swChkWanMac(local_fc);
    if (iVar2 == 0) {
      swGetWanMac(local_fc + 12);
      iVar2 = memcmp(local_fc + 12,local_fc,12);
      if ((((iVar2 != 0) && (iVar2 = swCloneWanMac(0,local_fc), iVar2 == 0)) &&
          (iVar2 = swIsSysModeChange(), iVar2 == 0)) && (local_104[0] == 4)) {
        swWlanWispMacCloneFinalConf(0);
      }
      goto LAB_00429b20;
    }
    puVar5 = "";
  }
LAB_00429e0c:
  sVar4 = HttpErrorPage(param_1,iVar2,puVar5,0);
LAB_00429e14:
  return sVar4;
}

