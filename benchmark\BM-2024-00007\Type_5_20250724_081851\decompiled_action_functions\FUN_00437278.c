
/* WARNING: Globals starting with '_' overlap smaller symbols at the same address */

undefined4 FUN_00437278(undefined4 param_1)

{
  uint32_t uVar1;
  uint32_t uVar2;
  uint32_t uVar3;
  uint32_t uVar4;
  char *__src;
  undefined4 uVar5;
  int iVar6;
  uint32_t local_d8;
  undefined auStack_d4 [4];
  char acStack_d0 [16];
  char acStack_c0 [16];
  uint32_t local_b0 [4];
  char local_a0 [52];
  char acStack_6c [72];
  
  builtin_strncpy(local_a0 + 4,".htm",5);
  builtin_strncpy(local_a0,"main",4);
  memset(local_a0 + 9,0,")");
  swGetLanCfg(local_b0);
  swGetWanIpMask(0,&local_d8,auStack_d4);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  _0x00000000 = "@";
  _0x00000000 = 16;
  "" = 0;
  "" = 0;
  indexDynArray = acStack_d0;
  _0x00000000 = &local_d8;
  "" = acStack_6c;
  uVar1 = ntohl(local_b0[0]);
  uVar2 = ntohl(local_b0[0]);
  uVar3 = ntohl(local_b0[0]);
  uVar4 = ntohl(local_b0[0]);
  sprintf(acStack_d0,"%d.%d.%d.%d",uVar1 >> 24,(uVar2 & 0xff0000) >> 16,
          (int)(uVar3 & -256) >> 8,uVar4 & 255);
  uVar1 = ntohl(local_d8);
  uVar2 = ntohl(local_d8);
  uVar3 = ntohl(local_d8);
  uVar4 = ntohl(local_d8);
  sprintf(acStack_c0,"%d.%d.%d.%d",uVar1 >> 24,(uVar2 & 0xff0000) >> 16,
          (int)(uVar3 & -256) >> 8,uVar4 & 255);
  __src = getDefaultLanDomain();
  strncpy(acStack_6c,__src,"@");
  uVar5 = swGetFirstState();
  iVar6 = httpGetEnv(param_1,"redirectToWlanSec");
  if (iVar6 != 0) {
    uVar5 = 255;
  }
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "framePara");
  httpPrintf(param_1,"%d,\r\n",uVar5);
  httpPrintf(param_1,"\"%s\",\r\n",local_a0);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "indexPara");
  pageDynParaPrintf(&indexDynArray,0,param_1);
  pageDynParaPrintf(&indexDynArray,1,param_1);
  pageDynParaPrintf(&indexDynArray,2,param_1);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  HttpWebV4Head(param_1,0,0);
  iVar6 = httpRpmFsA(param_1,"/userRpm/Index.htm");
  if (iVar6 != 2) {
    puts("open filesys error\r");
  }
  return 2;
}

