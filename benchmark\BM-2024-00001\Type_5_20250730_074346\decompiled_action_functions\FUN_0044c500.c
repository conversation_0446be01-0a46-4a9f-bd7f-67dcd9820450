
int FUN_0044c500(undefined4 param_1)

{
  int iVar1;
  short sVar8;
  char *pcVar2;
  uint32_t uVar3;
  uint32_t uVar4;
  uint32_t uVar5;
  uint32_t uVar6;
  undefined4 uVar7;
  int iVar9;
  code *pcVar10;
  undefined4 local_3e0;
  undefined4 local_3dc;
  char local_3d8 [80];
  char local_388;
  undefined auStack_387 [43];
  undefined local_35c;
  undefined auStack_35b [43];
  char acStack_330 [64];
  undefined *local_2f0;
  undefined4 local_2ec;
  undefined *local_2e8;
  undefined4 local_2e4;
  undefined *local_2e0;
  undefined4 local_2dc;
  undefined *local_2d8;
  undefined4 local_2d4;
  undefined *local_2d0;
  undefined4 local_2cc;
  undefined *local_2c8;
  undefined4 local_2c4;
  undefined *local_2c0;
  undefined4 local_2bc;
  undefined *local_2b8;
  undefined4 local_2b4;
  undefined *local_2b0;
  undefined4 local_2ac;
  undefined *local_2a8;
  undefined4 local_2a4;
  undefined *local_2a0;
  undefined4 local_29c;
  undefined *local_298;
  undefined4 local_294;
  undefined4 local_290;
  undefined4 local_28c;
  int local_288;
  int local_284;
  uint local_280;
  char acStack_27c [45];
  char acStack_24f [47];
  uint32_t local_220;
  uint32_t local_21c;
  uint32_t local_210;
  undefined auStack_12c [4];
  undefined auStack_128 [4];
  undefined auStack_124 [4];
  undefined auStack_120 [4];
  undefined auStack_11c [45];
  undefined auStack_ef [45];
  undefined auStack_c2 [16];
  undefined auStack_b2 [16];
  undefined auStack_a2 [16];
  undefined auStack_92 [44];
  undefined auStack_66 [50];
  undefined auStack_34 [4];
  char *local_30;
  
  local_388 = '\0';
  local_3e0 = 0;
  memset(auStack_387,0,"+");
  local_35c = 0;
  memset(auStack_35b,0,"+");
  memset(acStack_330,0,"@");
  local_3d8[0] = '\0';
  local_3d8[1] = '\0';
  local_3d8[2] = '\0';
  local_3d8[3] = '\0';
  local_3d8[4] = '\0';
  local_3d8[5] = '\0';
  local_3d8[6] = '\0';
  local_3d8[7] = '\0';
  local_3d8[8] = '\0';
  local_3d8[9] = '\0';
  local_3d8[10] = '\0';
  local_3d8[11] = '\0';
  local_3d8[12] = '\0';
  local_3d8[13] = '\0';
  local_3d8[14] = '\0';
  local_3d8[15] = '\0';
  local_3d8[16] = '\0';
  local_3d8[17] = '\0';
  local_3d8[18] = '\0';
  local_3d8[19] = '\0';
  local_3d8[20] = '\0';
  local_3d8[21] = '\0';
  local_3d8[22] = '\0';
  local_3d8[23] = '\0';
  local_3d8[24] = '\0';
  local_3d8[25] = '\0';
  local_3d8[26] = '\0';
  local_3d8[27] = '\0';
  local_3d8[28] = '\0';
  local_3d8[29] = '\0';
  local_3d8[30] = '\0';
  local_3d8[31] = '\0';
  local_3d8[32] = '\0';
  local_3d8[33] = '\0';
  local_3d8[34] = '\0';
  local_3d8[35] = '\0';
  local_3d8[36] = '\0';
  local_3d8[37] = '\0';
  local_3d8[38] = '\0';
  local_3d8[39] = '\0';
  local_3d8[40] = '\0';
  local_3d8[41] = '\0';
  local_3d8[42] = '\0';
  local_3d8[43] = '\0';
  local_3d8[44] = '\0';
  local_3d8[45] = '\0';
  local_3d8[46] = '\0';
  local_3d8[47] = '\0';
  local_3dc = 0;
  memset(local_3d8 + "0",0," ");
  local_2e8 = auStack_128;
  local_2e0 = auStack_124;
  local_2d8 = auStack_120;
  local_2d0 = auStack_11c;
  local_2c8 = auStack_ef;
  local_2c0 = auStack_c2;
  local_2b8 = auStack_b2;
  local_2b0 = auStack_a2;
  local_2a8 = auStack_92;
  local_2a0 = auStack_66;
  local_298 = auStack_34;
  local_2f0 = auStack_12c;
  local_2a4 = ",";
  local_2ac = 16;
  local_2bc = 16;
  local_2b4 = 16;
  local_2c4 = "-";
  local_2cc = "-";
  local_29c = "0";
  local_290 = 0;
  local_2ec = 0;
  local_2e4 = 0;
  local_2dc = 0;
  local_2d4 = 0;
  local_294 = 0;
  local_28c = 0;
  memset(local_2f0,0,252);
  memset(&local_288,0,"h");
  memset(&local_220,0,244);
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar1 = HttpAccessPermit(param_1);
  if (iVar1 == 0) {
    sVar8 = HttpDenyPage(param_1);
    goto LAB_0044ce70;
  }
  swGet6to4TunnelCfg(&local_288);
  iVar1 = httpGetEnv(param_1,"Save");
  if (iVar1 != 0) {
    iVar1 = httpGetEnv(param_1,"ipv6Enable");
    if (iVar1 == 0) {
      IPV6_ECHO("ucSetIPv6Enable(FALSE);");
    }
    else {
      IPV6_ECHO("ucSetIPv6Enable(TRUE);");
    }
    ucSetIPv6Enable(iVar1 != 0);
    pcVar2 = httpGetEnv(param_1,"mtu");
    if (pcVar2 != 0) {
      local_284 = atoi(pcVar2);
    }
    iVar1 = httpGetEnv(param_1,"manual");
    local_280 = (uint)(iVar1 != 0);
    pcVar2 = httpGetEnv(param_1,"dnsserver1");
    if (pcVar2 != 0) {
      strcpy(acStack_27c,pcVar2);
    }
    pcVar2 = httpGetEnv(param_1,"dnsserver2");
    if (pcVar2 == 0) {
      memset(acStack_24f,0,"-");
    }
    else {
      strcpy(acStack_24f,pcVar2);
    }
    stopCurrentConnection(5);
    swSet6to4TunnelCfg(&local_288);
    iVar1 = swGet6TO4Enable();
    if (iVar1 == 1) {
      iVar1 = getTunnelStatus();
      if (iVar1 != 0) goto LAB_0044c974;
      pcVar10 = swWan6to4TunnleInit;
    }
    else {
      pcVar10 = swDelete6to4Tunnel;
    }
    (*pcVar10)();
  }
LAB_0044c974:
  LanIpv6RpmHtm(param_1,5);
  local_3e0 = 5;
  pageParaSet(&local_2f0,&local_3e0,0);
  pageParaSet(&local_2f0,&local_288,1);
  pageParaSet(&local_2f0,&local_284,2);
  pageParaSet(&local_2f0,&local_280,3);
  pageParaSet(&local_2f0,acStack_27c,4);
  pageParaSet(&local_2f0,acStack_24f,5);
  swGetNetStatus(0,&local_220);
  uVar3 = ntohl(local_220);
  uVar4 = ntohl(local_220);
  uVar5 = ntohl(local_220);
  local_30 = local_3d8 + " ";
  uVar6 = ntohl(local_220);
  sprintf(local_3d8,"%d.%d.%d.%d",uVar3 >> 24,(uVar4 & 0xff0000) >> 16,
          (int)(uVar5 & -256) >> 8,uVar6 & 255);
  pageParaSet(&local_2f0,local_3d8,6);
  uVar3 = ntohl(local_21c);
  uVar4 = ntohl(local_21c);
  uVar5 = ntohl(local_21c);
  uVar6 = ntohl(local_21c);
  sprintf(local_3d8 + 16,"%d.%d.%d.%d",uVar3 >> 24,(uVar4 & 0xff0000) >> 16,
          (int)(uVar5 & -256) >> 8,uVar6 & 255);
  pageParaSet(&local_2f0,local_3d8 + 16,7);
  uVar3 = ntohl(local_210);
  uVar4 = ntohl(local_210);
  uVar5 = ntohl(local_210);
  uVar6 = ntohl(local_210);
  sprintf(local_30,"%d.%d.%d.%d",uVar3 >> 24,(uVar4 & 0xff0000) >> 16,(int)(uVar5 & -256) >> 8
          ,uVar6 & 255);
  pageParaSet(&local_2f0,local_30,8);
  uVar7 = getWanIpv6IfName();
  swGetIpv6Address(uVar7,&local_388,&local_3dc,local_3d8 + "0",3);
  if (local_388 != '\0') {
    sprintf(acStack_330,"%s/48",&local_388);
  }
  pageParaSet(&local_2f0,acStack_330,9);
  memset(acStack_330,0,"@");
  if (local_288 == 1) {
    getTunnelPrefix(&local_35c);
    sprintf(acStack_330,"%s/64",&local_35c);
  }
  else {
    strcpy(acStack_330,"");
  }
  pageParaSet(&local_2f0,acStack_330,10);
  local_3e0 = swGetIPv6Enable();
  pageParaSet(&local_2f0,&local_3e0,11);
  httpPrintf(param_1,
             "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
             "Tunnel6to4Info");
  iVar1 = 0;
  do {
    iVar9 = iVar1 + 1;
    pageDynParaPrintf(&local_2f0,iVar1,param_1);
    iVar1 = iVar9;
  } while (iVar9 != 13);
  httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
  httpPrintfWanIpv6TypeInfo(param_1);
  HttpWebV4Head(param_1,0,1);
  iVar1 = httpRpmFsA(param_1,"/userRpm/Wan6to4TunnelCfgRpm.htm");
  if (iVar1 == 2) {
    return 2;
  }
  sVar8 = HttpErrorPage(param_1,10,0,0);
LAB_0044ce70:
  return sVar8;
}

