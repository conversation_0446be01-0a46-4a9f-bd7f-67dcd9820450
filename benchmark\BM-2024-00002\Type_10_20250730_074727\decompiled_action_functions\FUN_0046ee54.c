
undefined4 FUN_0046ee54(undefined4 param_1)

{
  int iVar1;
  int iVar2;
  undefined4 local_1c;
  undefined4 local_18;
  undefined2 local_14;
  int local_10;
  int local_c;
  
  local_1c = 0;
  local_18 = 0;
  local_14 = 0;
  local_10 = 0;
  local_c = 0;
  iVar1 = mxmlLoadString(0,param_1,0);
  if (iVar1 == 0) {
    fwrite("AccessControl: tree is NULL,  exit\n",1,"#",stderr);
  }
  else {
    iVar2 = mxmlFindElement(iVar1,iVar1,"soap:Envelope",0,0,1);
    if (iVar2 == 0) {
      fwrite("AccessControl: state is NULL,  exit\n",1,"$",stderr);
      mxmlDelete(iVar1);
    }
    else {
      iVar2 = mxmlFindElement(iVar2,iVar1,"GetAccessCtlSwitch",0,0,1);
      if (iVar2 == 0) {
        fwrite("AccessControl: state1 is failed\n",1," ",stderr);
        mxmlDelete(iVar1);
      }
      else {
        iVar2 = apmib_get("z",&local_10);
        if (iVar2 == 0) {
          fwrite("Get MIB_MACFILTER_ENABLED Error, Exit!\n",1,"'",stderr);
          mxmlDelete(iVar1);
        }
        else {
          iVar2 = apmib_get(0x1ccf,&local_c);
          if (iVar2 == 0) {
            fwrite("Get MIB_MACFILTER_WHITE_ENABLED Error, Exit!\n",1,"-",stderr);
            mxmlDelete(iVar1);
          }
          else {
            if ((local_10 == 1) || (local_c == 1)) {
              memcpy(&local_1c,"O",2);
              if ((local_10 == 1) && (local_c == 0)) {
                memcpy(&local_18,"Black",5);
              }
              if ((local_10 == 0) && (local_c == 1)) {
                memcpy(&local_18,"White",5);
              }
            }
            else {
              memcpy(&local_1c,"OFF",3);
              memcpy(&local_18,"",1);
            }
            iVar2 = FUN_0046d87c(&local_1c,&local_18);
            if (iVar2 == 0) {
              mxmlDelete(iVar1);
            }
            else {
              fwrite("ReplyGetAccessCtlSwitch Error, Exit!\n",1,"%",stderr);
              mxmlDelete(iVar1);
            }
          }
        }
      }
    }
  }
  return 0;
}

