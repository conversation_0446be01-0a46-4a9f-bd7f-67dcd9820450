
undefined4 FUN_00451ba8(undefined4 param_1)

{
  bool bVar1;
  uint uVar2;
  uint uVar3;
  sysinfo local_50;
  
  puts("Start Test...");
  sysinfo(&local_50);
  if (299 < local_50.uptime) {
    printf(0xffffffcf);
    return 2;
  }
  uVar2 = swGetProductId();
  if (uVar2 < 0x8420004) {
    if (uVar2 < 0x8420002) {
      if (uVar2 < 0x8020003) {
        if (0x8020000 < uVar2) {
LAB_00451edc:
          FUN_004515a8(param_1,0,local_50.uptime);
          goto LAB_00451f48;
        }
        if (uVar2 != 0x7400005) {
          if (uVar2 < 0x7400006) {
            if (uVar2 == 0x7201002) goto LAB_00451ef0;
            if (uVar2 != 0x7400004) {
              uVar3 = 0x7100002;
LAB_00451db8:
              if (uVar2 != uVar3) goto LAB_00451f48;
              goto LAB_00451ea0;
            }
          }
          else if (uVar2 != 0x7410004) {
            if (uVar2 < 0x7410005) {
              bVar1 = uVar2 < 0x7400008;
              goto LAB_00451e5c;
            }
            if (uVar2 != 0x7430002) goto LAB_00451f48;
            goto LAB_00451edc;
          }
          FUN_00450ff4(param_1,0,local_50.uptime);
          goto LAB_00451f48;
        }
      }
      else {
        if (uVar2 < 0x8410013) {
          if (uVar2 < 0x8410010) {
            uVar3 = 0x8410009;
            if (uVar2 == 0x8400002) goto LAB_00451ef0;
LAB_00451d9c:
            if (uVar2 != uVar3) goto LAB_00451f48;
          }
          goto LAB_00451ec8;
        }
        if (uVar2 != 0x8411002) {
          if ((uVar2 < 0x8411002) || (0x8411004 < uVar2)) goto LAB_00451f48;
          goto LAB_00451f38;
        }
      }
LAB_00451ef0:
      FUN_004519e0(param_1,0,local_50.uptime);
      goto LAB_00451f48;
    }
  }
  else {
    if (uVar2 == 0x9411001) {
LAB_00451f38:
      FUN_00451130(param_1,0,local_50.uptime);
      goto LAB_00451f48;
    }
    if (0x9411001 < uVar2) {
      if (0x30400002 < uVar2) {
        if ((uVar2 == 0x32200002) || ((0x32200001 < uVar2 && (uVar2 + 0xcbdffffe < 2)))) {
          FUN_0045132c(param_1,0);
        }
        goto LAB_00451f48;
      }
      if (uVar2 < 0x30400001) {
        if (uVar2 < 0x10430002) goto LAB_00451f48;
        if (uVar2 < 0x10430004) goto LAB_00451ef0;
        bVar1 = uVar2 + 0xcfdfffff < 2;
LAB_00451e5c:
        if (!bVar1) goto LAB_00451f48;
      }
      goto LAB_00451edc;
    }
    if (uVar2 == 0x9010004) goto LAB_00451ef0;
    if (0x9010004 < uVar2) {
      if (uVar2 != 0x9400004) {
        if (uVar2 < 0x9400005) {
          if (uVar2 != 0x9400002) goto LAB_00451f48;
        }
        else if (1 < uVar2 + 0xf6befffb) goto LAB_00451f48;
      }
      FUN_00451484(param_1,0);
      goto LAB_00451f48;
    }
    if (uVar2 == 0x8430001) {
LAB_00451ea0:
      FUN_00451764(param_1,0);
      goto LAB_00451f48;
    }
    uVar3 = 0x8420202;
    if (uVar2 < 0x8430002) goto LAB_00451d9c;
    if (uVar2 != 0x8430002) {
      uVar3 = 0x8450001;
      goto LAB_00451db8;
    }
  }
LAB_00451ec8:
  FUN_00451888(param_1,0);
LAB_00451f48:
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  HttpWebV4Head(param_1,0,1);
  return 2;
}

