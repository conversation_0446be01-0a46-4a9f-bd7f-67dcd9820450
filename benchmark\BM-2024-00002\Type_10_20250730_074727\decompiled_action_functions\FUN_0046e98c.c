
undefined4 FUN_0046e98c(undefined4 param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  char *pcVar4;
  int iVar5;
  undefined4 uVar6;
  undefined4 local_10 [2];
  
  printf("---String = %s\n",param_1);
  local_10[0] = 0;
  iVar1 = mxmlLoadString(0,param_1,0);
  if (iVar1 == 0) {
    fwrite("AccessControl: tree is NULL,  exit\n",1,"#",stderr);
  }
  else {
    iVar2 = mxmlFindElement(iVar1,iVar1,"soap:Envelope",0,0,1);
    if (iVar2 == 0) {
      fwrite("AccessControl: state is NULL,  exit\n",1,"$",stderr);
      mxmlDelete(iVar1);
    }
    else {
      iVar3 = mxmlFindElement(iVar2,iVar1,"SetAccessCtlSwitch",0,0,1);
      if (iVar3 == 0) {
        fwrite("AccessControl: state1 is failed\n",1," ",stderr);
        mxmlDelete(iVar1);
      }
      else {
        iVar3 = mxmlFindElement(iVar3,iVar1,"SwitchState",0,0,1);
        pcVar4 = mxmlGetText(iVar3,0);
        if ((iVar3 == 0) || (pcVar4 == 0)) {
          fwrite("AccessControl: state2 or SwitchState is failed\n",1,"/",stderr);
          mxmlDelete(iVar1);
        }
        else {
          iVar5 = strncmp(pcVar4,"OFF",3);
          if (iVar5 == 0) {
            local_10[0] = 0;
            apmib_set("z",local_10);
            apmib_set(0x1ccf,local_10);
          }
          else {
            iVar5 = strncmp(pcVar4,"ON",2);
            if (iVar5 == 0) {
              local_10[0] = 1;
              iVar2 = mxmlFindElement(iVar2,iVar1,"SetAccessCtlList",0,0,1);
              if (iVar2 == 0) {
                fwrite("AccessControl: state3 is failed\n",1," ",stderr);
                mxmlDelete(iVar1);
                return 0;
              }
              uVar6 = mxmlFindElement(iVar2,iVar1,"AccessCtlType",0,0,1);
              pcVar4 = mxmlGetText(uVar6,0);
              if ((iVar3 == 0) || (pcVar4 == 0)) {
                fwrite("AccessControl: state4 or AclType is failed\n",1,"+",stderr);
                mxmlDelete(iVar1);
                return 0;
              }
              iVar2 = strncmp(pcVar4,"Black",5);
              if (iVar2 == 0) {
                local_10[0] = 1;
                apmib_set("z",local_10);
                local_10[0] = 0;
                apmib_set(0x1ccf,local_10);
              }
              else {
                iVar2 = strncmp(pcVar4,"White",5);
                if (iVar2 == 0) {
                  local_10[0] = 1;
                  apmib_set(0x1ccf,local_10);
                  local_10[0] = 0;
                  apmib_set("z",local_10);
                }
              }
            }
          }
          apmib_update(4);
          mxmlDelete(iVar1);
          if ("" == 0) {
            system("sysconf init gw all &");
          }
        }
      }
    }
  }
  return 0;
}

