
undefined4 FUN_00441098(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  char *__s;
  int local_18;
  char local_14 [12];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetFactoryDefault");
  }
  else {
    local_18 = 0;
    local_14[0] = '\0';
    local_14[1] = '\0';
    local_14[2] = '\0';
    local_14[3] = '\0';
    local_14[4] = '\0';
    local_14[5] = '\0';
    local_14[6] = '\0';
    local_14[7] = '\0';
    iVar1 = apmib_get(0x1b63,&local_18);
    if (iVar1 == 0) {
      puts("error, apmib set auto reboot");
    }
    if (local_18 == 0) {
      snprintf(local_14,5,"true");
    }
    else {
      snprintf(local_14,6,"false");
    }
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      printf("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar2 = mxmlNewElement(iVar2,"soap:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"GetFactoryDefaultResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("GetFactoryDefaultResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar3 = mxmlNewElement(iVar2,"GetFactoryDefaultResult");
            if (iVar3 == 0) {
              mxmlDelete(iVar1);
              puts("GetFactoryDefaultResult=NULL");
            }
            else {
              mxmlNewText(iVar3,0,"O");
              iVar2 = mxmlNewElement(iVar2,"IsDefault");
              if (iVar2 == 0) {
                mxmlDelete(iVar1);
                puts("IsDefault_xml=NULL");
              }
              else {
                mxmlNewText(iVar2,0,local_14);
                if ("" == 0) {
                  __s = mxmlSaveAllocString(iVar1,0);
                  if (__s != 0) {
                    puts(__s);
                    FUN_0041ed70("",200,__s,"");
                    free(__s);
                  }
                }
                mxmlDelete(iVar1);
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

