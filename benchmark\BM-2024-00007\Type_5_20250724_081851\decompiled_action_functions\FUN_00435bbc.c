
int FUN_00435bbc(undefined4 param_1)

{
  undefined4 uVar1;
  int iVar2;
  short sVar5;
  char *pcVar3;
  uint uVar4;
  char cVar6;
  long unaff_s2;
  short asStack_28 [2];
  undefined4 auStack_24 [4];
  
  asStack_28[0] = 0;
  swGetSystemMode(auStack_24);
  uVar1 = swIsMultiSystemMode();
  httpStatusSet(param_1,0);
  httpHeaderGenerate(param_1);
  iVar2 = HttpAccessPermit(param_1);
  if (iVar2 == 0) {
    HTTP_DEBUG_PRINT("basicNetwork/httpWizardWan.c:2239","%s---%d","WzdWanTroubleShootingRpmHtm",
                     0x8bf);
    sVar5 = HttpDenyPage(param_1);
LAB_00435f08:
    return sVar5;
  }
  pcVar3 = httpGetEnv(param_1,"ClientId");
  if (pcVar3 != 0) {
    unaff_s2 = atol(pcVar3);
  }
  iVar2 = httpGetEnv(param_1,"Next");
  if (iVar2 == 0) {
    iVar2 = httpGetEnv(param_1,"Return");
    if (iVar2 == 0) {
      httpPrintf(param_1,
                 "<SCRIPT language=\"javascript\" type=\"text/javascript\">\nvar %s = new Array(\n",
                 "wzdTroubleShooting");
      httpPrintf(param_1,"%d,",auStack_24[0]);
      httpPrintf(param_1,"%d,",uVar1);
      httpPrintf(param_1,"%d,",unaff_s2);
      httpPrintf(param_1,"0,0 );\n</SCRIPT>\n");
      HttpWebV4Head(param_1,0,0);
      iVar2 = httpRpmFsA(param_1,"/userRpm/WzdWanTroubleShootingRpm.htm");
      if (iVar2 == 2) {
        return 2;
      }
      sVar5 = HttpErrorPage(param_1,10,0,0);
    }
    else {
      sVar5 = GoUrl(param_1,"../userRpm/WzdWanTypeRpm.htm");
    }
    goto LAB_00435f08;
  }
  pcVar3 = httpGetEnv(param_1,"opt");
  if (pcVar3 == 0) {
LAB_00435d60:
    HTTP_DEBUG_PRINT("basicNetwork/httpWizardWan.c:2272","%s---%d---opt_value overflow",
                     "WzdWanTroubleShootingRpmHtm",0x8e0);
    return -1;
  }
  uVar4 = atoi(pcVar3);
  uVar4 = uVar4 & -1;
  if (uVar4 != 2) {
    cVar6 = '\x01';
    if (uVar4 == 3) goto LAB_00435d90;
    if (uVar4 != 1) goto LAB_00435d60;
  }
  cVar6 = '\0';
  "" = uVar4;
LAB_00435d90:
  asStack_28[0] = (ushort)(byte)(cVar6 + "0") << 8;
  httpSetEnv(param_1,"wan",asStack_28);
  httpSetEnv(param_1,"Next","1");
  iVar2 = FUN_004354d0(param_1);
  return iVar2;
}

