
undefined4 FUN_00440684(int param_1)

{
  int iVar1;
  int iVar2;
  int iVar3;
  char *pcVar4;
  void *__ptr;
  char local_20 [8];
  undefined4 local_18;
  undefined4 local_14;
  int local_10;
  int local_c;
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetAutoRebootSettings");
  }
  else {
    local_20[0] = '\0';
    local_20[1] = '\0';
    local_20[2] = '\0';
    local_20[3] = '\0';
    local_20[4] = '\0';
    local_20[5] = '\0';
    local_20[6] = '\0';
    local_20[7] = '\0';
    local_18 = 0;
    local_14 = 0;
    local_10 = 0;
    local_c = 0;
    iVar1 = apmib_get(0x1b64,&local_10);
    if (iVar1 == 0) {
      puts("error, apmib set auto reboot");
    }
    iVar1 = apmib_get(0x1b65,&local_c);
    if (iVar1 == 0) {
      puts("error, apmib set auto reboot");
    }
    else {
      snprintf(local_20,7,"%d",local_c);
      snprintf(&local_18,7,"%d",(local_c + 2) % 24);
    }
    iVar1 = mxmlNewXML("1.0");
    if (iVar1 == 0) {
      puts("Create new xml erro!!!");
    }
    else {
      iVar2 = mxmlNewElement(iVar1,"soap:Envelope");
      if (iVar2 == 0) {
        mxmlDelete(iVar1);
        puts("soap_env=NULL");
      }
      else {
        mxmlElementSetAttr(iVar2,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar2,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar2,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar2 = mxmlNewElement(iVar2,"soap:Body");
        if (iVar2 == 0) {
          mxmlDelete(iVar1);
          puts("body=NULL");
        }
        else {
          iVar2 = mxmlNewElement(iVar2,"GetAutoRebootSettingsResponse");
          if (iVar2 == 0) {
            mxmlDelete(iVar1);
            puts("GetAutoRebootSettingsResponse=NULL");
          }
          else {
            mxmlElementSetAttr(iVar2,"xmlns","http://purenetworks.com/HNAP1/");
            iVar3 = mxmlNewElement(iVar2,"GetAutoRebootSettingsResult");
            if (iVar3 == 0) {
              mxmlDelete(iVar1);
              puts("GetAutoRebootSettingsResult=NULL");
            }
            else {
              mxmlNewText(iVar3,0,"O");
              iVar3 = mxmlNewElement(iVar2,"AutoReboot");
              if (iVar3 == 0) {
                mxmlDelete(iVar1);
                puts("enabled=NULL");
              }
              else {
                if (local_10 == 0) {
                  pcVar4 = "false";
                }
                else {
                  pcVar4 = "true";
                }
                mxmlNewText(iVar3,0,pcVar4);
                iVar3 = mxmlNewElement(iVar2,"StartTime");
                if (iVar3 == 0) {
                  mxmlDelete(iVar1);
                  puts("StartTime_xml=NULL");
                }
                else {
                  mxmlNewText(iVar3,0,local_20);
                  iVar2 = mxmlNewElement(iVar2,"StopTime");
                  if (iVar2 == 0) {
                    mxmlDelete(iVar1);
                    puts("StopTime_xml=NULL");
                  }
                  else {
                    mxmlNewText(iVar2,0,&local_18);
                    if ("" == 0) {
                      __ptr = mxmlSaveAllocString(iVar1,0);
                      if (__ptr != 0) {
                        FUN_0041ed70("",200,__ptr,"");
                        free(__ptr);
                      }
                    }
                    mxmlDelete(iVar1);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

