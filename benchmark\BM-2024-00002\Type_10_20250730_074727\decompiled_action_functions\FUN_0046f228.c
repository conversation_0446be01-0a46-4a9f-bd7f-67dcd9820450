
undefined4 FUN_0046f228(undefined4 param_1)

{
  int iVar1;
  int iVar2;
  char *pcVar3;
  int local_64;
  char local_50 [20];
  undefined4 local_3c;
  undefined auStack_38 [28];
  undefined auStack_1c [20];
  
  local_50[0] = '\0';
  local_50[1] = '\0';
  local_50[2] = '\0';
  local_50[3] = '\0';
  local_50[4] = '\0';
  local_50[5] = '\0';
  local_50[6] = '\0';
  local_50[7] = '\0';
  local_50[8] = '\0';
  local_50[9] = '\0';
  local_50[10] = '\0';
  local_50[11] = '\0';
  local_50[12] = '\0';
  local_50[13] = '\0';
  local_50[14] = '\0';
  local_50[15] = '\0';
  local_50[16] = '\0';
  local_50[17] = '\0';
  local_3c = 0;
  iVar1 = mxmlLoadString(0,param_1,0);
  if (iVar1 == 0) {
    fwrite("AccessControl: tree is NULL,  exit\n",1,"#",stderr);
  }
  else {
    iVar2 = mxmlFindElement(iVar1,iVar1,"soap:Envelope",0,0,1);
    if (iVar2 == 0) {
      fwrite("AccessControl: state is NULL,  exit\n",1,"$",stderr);
      mxmlDelete(iVar1);
    }
    else {
      local_64 = mxmlFindElement(iVar2,iVar1,"SetAccessCtlList",0,0,1);
      if (local_64 == 0) {
        fwrite("AccessControl: state1 is NULL,  exit\n",1,"%",stderr);
        mxmlDelete(iVar1);
      }
      else {
        iVar2 = mxmlFindElement(local_64,iVar1,"AccessCtlType",0,0,1);
        pcVar3 = mxmlGetText(iVar2,0);
        if ((iVar2 == 0) || (pcVar3 == 0)) {
          fwrite("AccessControl: state2 or AclType is NULL,  exit\n",1,"0",stderr);
          mxmlDelete(iVar1);
        }
        else {
          iVar2 = strncmp(pcVar3,"Black",5);
          if (iVar2 == 0) {
            apmib_set(0x1ccf,&local_3c);
            local_3c = 1;
            apmib_set("z",&local_3c);
            memset(auStack_38,0,27);
            iVar2 = apmib_set(0x4007f,auStack_38);
            if (iVar2 == 0) {
              puts("del all black list Error!");
              mxmlDelete(iVar1);
            }
            else {
              do {
                do {
                  if (local_64 == 0) goto LAB_0046f934;
                  local_64 = mxmlFindElement(local_64,iVar1,"AclClientInfo",0,0,1);
                } while (local_64 == 0);
                iVar2 = mxmlFindElement(local_64,iVar1,"Mac",0,0,1);
                pcVar3 = mxmlGetText(iVar2,0);
                if ((iVar2 == 0) || (pcVar3 == 0)) {
                  fwrite("AccessControl: state4  or MacValue is failed\n",1,"-",stderr);
                  mxmlDelete(iVar1);
                  return 0;
                }
                memset(auStack_38,0,27);
                memset(local_50,0,18);
                strncpy(local_50,pcVar3,18);
                FUN_0046d23c(local_50,":");
                FUN_0046d128(local_50,auStack_38,12);
                iVar2 = apmib_set(0x1007d,auStack_38);
              } while (iVar2 != 0);
              fwrite("AccessControl: Add Black list info failed\n",1,"*",stderr);
              mxmlDelete(iVar1);
            }
          }
          else {
            iVar2 = strncmp(pcVar3,"White",5);
            if (iVar2 == 0) {
              apmib_set("z",&local_3c);
              local_3c = 1;
              apmib_set(0x1ccf,&local_3c);
              memset(auStack_1c,0,16);
              iVar2 = apmib_set(0x41cd8,auStack_1c);
              if (iVar2 == 0) {
                puts("del all white list Error!");
                mxmlDelete(iVar1);
              }
              else {
                do {
                  do {
                    if (local_64 == 0) goto LAB_0046f934;
                    local_64 = mxmlFindElement(local_64,iVar1,"AclClientInfo",0,0,1);
                  } while (local_64 == 0);
                  iVar2 = mxmlFindElement(local_64,iVar1,"Mac",0,0,1);
                  pcVar3 = mxmlGetText(iVar2,0);
                  if ((iVar2 == 0) || (pcVar3 == 0)) {
                    fwrite("AccessControl: state4 or MacValue is failed\n",1,",",stderr);
                    mxmlDelete(iVar1);
                    return 0;
                  }
                  memset(auStack_1c,0,16);
                  memset(local_50,0,18);
                  strncpy(local_50,pcVar3,18);
                  FUN_0046d23c(local_50,":");
                  FUN_0046d128(local_50,auStack_1c,12);
                  iVar2 = apmib_set(0x11cd6,auStack_1c);
                } while (iVar2 != 0);
                fwrite("AccessControl: Add White list info failed\n",1,"*",stderr);
                mxmlDelete(iVar1);
              }
            }
            else {
LAB_0046f934:
              apmib_update(4);
              mxmlDelete(iVar1);
              if ("" == 0) {
                system("sysconf init gw all &");
              }
            }
          }
        }
      }
    }
  }
  return 0;
}

