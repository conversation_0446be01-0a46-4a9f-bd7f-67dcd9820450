
undefined * FUN_00457c60(int param_1)

{
  undefined *puVar1;
  int iVar2;
  int iVar3;
  int iVar4;
  char *pcVar5;
  void *__ptr;
  char local_a0 [32];
  undefined4 local_80;
  undefined4 local_7c;
  undefined4 local_78;
  undefined4 local_74;
  undefined4 local_70;
  undefined4 local_6c;
  undefined4 local_68;
  undefined4 local_64;
  char local_60 [32];
  undefined4 local_40;
  undefined4 local_3c;
  undefined4 local_38;
  undefined4 local_34;
  undefined4 local_30;
  undefined4 local_2c;
  undefined4 local_28;
  undefined4 local_24;
  in_addr local_20;
  in_addr local_1c;
  byte local_18;
  byte local_17;
  byte local_16;
  byte local_15;
  byte local_14;
  byte local_13;
  int local_10 [2];
  
  if (param_1 == 0) {
    printf("%s:Input String=NULL\n","GetRouterLanSettings");
    puVar1 = 0;
  }
  else {
    local_a0[0] = '\0';
    local_a0[1] = '\0';
    local_a0[2] = '\0';
    local_a0[3] = '\0';
    local_a0[4] = '\0';
    local_a0[5] = '\0';
    local_a0[6] = '\0';
    local_a0[7] = '\0';
    local_a0[8] = '\0';
    local_a0[9] = '\0';
    local_a0[10] = '\0';
    local_a0[11] = '\0';
    local_a0[12] = '\0';
    local_a0[13] = '\0';
    local_a0[14] = '\0';
    local_a0[15] = '\0';
    local_a0[16] = '\0';
    local_a0[17] = '\0';
    local_a0[18] = '\0';
    local_a0[19] = '\0';
    local_a0[20] = '\0';
    local_a0[21] = '\0';
    local_a0[22] = '\0';
    local_a0[23] = '\0';
    local_a0[24] = '\0';
    local_a0[25] = '\0';
    local_a0[26] = '\0';
    local_a0[27] = '\0';
    local_a0[28] = '\0';
    local_a0[29] = '\0';
    local_a0[30] = '\0';
    local_a0[31] = '\0';
    local_80 = 0;
    local_7c = 0;
    local_78 = 0;
    local_74 = 0;
    local_70 = 0;
    local_6c = 0;
    local_68 = 0;
    local_64 = 0;
    local_60[0] = '\0';
    local_60[1] = '\0';
    local_60[2] = '\0';
    local_60[3] = '\0';
    local_60[4] = '\0';
    local_60[5] = '\0';
    local_60[6] = '\0';
    local_60[7] = '\0';
    local_60[8] = '\0';
    local_60[9] = '\0';
    local_60[10] = '\0';
    local_60[11] = '\0';
    local_60[12] = '\0';
    local_60[13] = '\0';
    local_60[14] = '\0';
    local_60[15] = '\0';
    local_60[16] = '\0';
    local_60[17] = '\0';
    local_60[18] = '\0';
    local_60[19] = '\0';
    local_60[20] = '\0';
    local_60[21] = '\0';
    local_60[22] = '\0';
    local_60[23] = '\0';
    local_60[24] = '\0';
    local_60[25] = '\0';
    local_60[26] = '\0';
    local_60[27] = '\0';
    local_60[28] = '\0';
    local_60[29] = '\0';
    local_60[30] = '\0';
    local_60[31] = '\0';
    local_40 = 0;
    local_3c = 0;
    local_38 = 0;
    local_34 = 0;
    local_30 = 0;
    local_2c = 0;
    local_28 = 0;
    local_24 = 0;
    local_10[0] = 255;
    iVar2 = mxmlNewXML("1.0");
    if (iVar2 == 0) {
      puts("xml=NULL");
      puVar1 = 0;
    }
    else {
      iVar3 = mxmlNewElement(iVar2,"soap:Envelope");
      if (iVar3 == 0) {
        mxmlDelete(iVar2);
        puts("soap_env=NULL");
        puVar1 = 0;
      }
      else {
        mxmlElementSetAttr(iVar3,"xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        mxmlElementSetAttr(iVar3,"xmlns:xsd","http://www.w3.org/2001/XMLSchema");
        mxmlElementSetAttr(iVar3,"xmlns:soap","http://schemas.xmlsoap.org/soap/envelope/");
        iVar3 = mxmlNewElement(iVar3,"soap:Body");
        if (iVar3 == 0) {
          mxmlDelete(iVar2);
          puts("body=NULL");
          puVar1 = 0;
        }
        else {
          iVar3 = mxmlNewElement(iVar3,"GetRouterLanSettingsResponse");
          if (iVar3 == 0) {
            mxmlDelete(iVar2);
            puts("GetRouterLanSettingsResponse_xml=NULL");
            puVar1 = 0;
          }
          else {
            mxmlElementSetAttr(iVar3,"xmlns","http://purenetworks.com/HNAP1/");
            iVar4 = mxmlNewElement(iVar3,"GetRouterLanSettingsResult");
            if (iVar4 == 0) {
              mxmlDelete(iVar2);
              puts("GetRouterLanSettingsResult_xml=NULL");
              puVar1 = 0;
            }
            else {
              mxmlNewText(iVar4,0,"O");
              iVar4 = mxmlNewElement(iVar3,"RouterIPAddress");
              if (iVar4 == 0) {
                mxmlDelete(iVar2);
                puts("RouterIPAddress_xml=NULL");
                puVar1 = 0;
              }
              else {
                apmib_get(170,&local_20);
                pcVar5 = inet_ntoa(local_20);
                if (pcVar5 == 0) {
                  mxmlDelete(iVar2);
                  puts("value=NULL");
                  puVar1 = 0;
                }
                else {
                  strncpy(local_60,pcVar5," ");
                  mxmlNewText(iVar4,0,local_60);
                  iVar4 = mxmlNewElement(iVar3,"RouterSubnetMask");
                  if (iVar4 == 0) {
                    mxmlDelete(iVar2);
                    puts("RouterSubnetMask_xml=NULL");
                    puVar1 = 0;
                  }
                  else {
                    apmib_get(171,&local_1c);
                    pcVar5 = inet_ntoa(local_1c);
                    if (pcVar5 == 0) {
                      mxmlDelete(iVar2);
                      puts("value=NULL");
                      puVar1 = 0;
                    }
                    else {
                      strncpy(&local_40,pcVar5," ");
                      mxmlNewText(iVar4,0,&local_40);
                      iVar4 = mxmlNewElement(iVar3,"DHCPServerEnabled");
                      if (iVar4 == 0) {
                        mxmlDelete(iVar2);
                        puts("DHCPServerEnabled_xml=NULL");
                        puVar1 = 0;
                      }
                      else {
                        apmib_get(173,local_10);
                        if (local_10[0] == 2) {
                          memcpy(&local_80,"true",5);
                        }
                        else {
                          memcpy(&local_80,"false",4);
                        }
                        mxmlNewText(iVar4,0,&local_80);
                        iVar3 = mxmlNewElement(iVar3,"RouterMACAddress");
                        if (iVar3 == 0) {
                          mxmlDelete(iVar2);
                          puts("RouterMACAddress_xml=NULL");
                          puVar1 = 0;
                        }
                        else {
                          memset(&local_18,0,6);
                          apmib_get(201,&local_18);
                          snprintf(local_a0,31,"%02x:%02x:%02x:%02x:%02x:%02x",local_18,
                                   local_17,local_16,local_15,local_14,
                                   local_13);
                          mxmlNewText(iVar3,0,local_a0);
                          __ptr = mxmlSaveAllocString(iVar2,0);
                          if (__ptr == 0) {
                            puts("retstring=NULL");
                          }
                          else {
                            FUN_0041ed70("",200,__ptr,"");
                            free(__ptr);
                          }
                          mxmlDelete(iVar2);
                          puVar1 = "O";
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return puVar1;
}

